{"permissions": {"allow": ["Bash(find:*)", "Bash(aws ecs describe-services:*)", "Bash(aws ecs list-tasks:*)", "Bash(aws ecs describe-tasks:*)", "Bash(aws ecs describe-task-definition:*)", "Bash(aws ecs describe-clusters:*)", "Bash(aws ecs list-container-instances:*)", "Bash(aws logs describe-log-groups:*)", "Bash(aws ecs describe-container-instances:*)", "Bash(aws ecs describe-capacity-providers:*)", "Bash(aws autoscaling describe-auto-scaling-groups:*)", "Bash(aws ec2 describe-instances:*)", "Bash(aws logs create-log-group:*)", "Bash(aws elbv2 describe-target-groups:*)", "Bash(aws elbv2 describe-target-health:*)", "<PERSON><PERSON>(timeout:*)"], "deny": []}}