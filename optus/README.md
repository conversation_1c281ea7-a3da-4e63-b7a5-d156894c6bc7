# Optus Voicemail System - Modular Terraform Infrastructure

This repository contains the modular Terraform infrastructure for the Optus Voicemail System, refactored from the original monolithic architecture to improve maintainability, scalability, and team collaboration.

## 🏗️ Architecture Overview

The infrastructure is organized into distinct layers following AWS best practices:

```
┌─────────────────────────┐
│     optus-modules       │  ← Reusable Components (Admin Dashboard)
├─────────────────────────┤
│     optus-ecs-lab       │  ← Compute Layer (ECS Services)
├─────────────────────────┤
│     optus-db-lab        │  ← Database Layer (RDS, ElastiCache)
├─────────────────────────┤
│      optus-vpc          │  ← Network Foundation (VPC, Subnets)
├─────────────────────────┤
│  optus-backend-config   │  ← State Management (S3, DynamoDB)
└─────────────────────────┘
```

## 📁 Directory Structure

```
optus/
├── README.md                           # This file
├── norwood-staging/                    # Original monolithic version (reference)
├── optus-backend-config/               # Terraform state backend setup
│   ├── main.tf
│   └── variables.tf
├── optus-vpc/                          # Network infrastructure
│   └── terraform/
│       ├── backend.tf
│       ├── main.tf
│       ├── outputs.tf
│       └── variables.tf
├── optus-db-lab/                       # Database layer
│   └── terraform/
│       ├── backend.tf
│       ├── main.tf
│       ├── outputs.tf
│       ├── variables.tf
│       └── modules/
│           ├── aurora_rds/
│           ├── elasticache/
│           └── kamailio_rds/
├── optus-ecs-lab/                      # Compute services
│   └── terraform/
│       ├── backend.tf
│       ├── main.tf
│       ├── outputs.tf
│       ├── variables.tf
│       └── modules/
│           ├── sbc/                    # Kamailio SBC
│           └── mozart-voicemail/       # Voicemail service
└── optus-modules/                      # Reusable components
    ├── main.tf
    ├── network.tf
    ├── outputs.tf
    ├── provider.tf
    └── modules/
        └── admin-dashboard/
```

## 🚀 Deployment Guide

### Prerequisites

1. **AWS CLI** configured with Optus profile:

   ```bash
   aws configure --profile Optus
   ```
2. **Terraform** >= 1.5.7 installed
3. **Required permissions** for AWS services:

   - VPC, Subnets, Route Tables
   - RDS, ElastiCache
   - ECS, EC2, Load Balancers
   - IAM, Secrets Manager, SSM
   - S3, DynamoDB

### Step 1: Backend Configuration

Deploy the Terraform state backend first:

```bash
cd optus-backend-config/
terraform init
terraform plan
terraform apply
```

This creates:

- S3 buckets for Terraform state storage
- DynamoDB table for state locking
- Proper encryption and access controls

### Step 2: Network Foundation

Deploy the VPC and networking components:

```bash
cd ../optus-vpc/terraform/
terraform init
terraform plan
terraform apply
```

This creates:

- VPC with DNS support enabled
- Public subnets (2) for load balancers
- Private subnets (10) for applications
- Database subnets (2) for RDS
- Internet Gateway and NAT Gateway
- Route tables and associations

In lab, this module will be replaced and provided by Optus

### Step 3: Database Layer

Deploy databases and caching:

```bash
cd ../../optus-db-lab/terraform/
terraform init
terraform plan
terraform apply
```

This creates:

- Aurora RDS cluster for Mozart application
- RDS instance for Kamailio
- ElastiCache Redis cluster
- Security groups and subnet groups
- Secrets Manager entries for database credentials

### Step 4: Compute Services

Deploy ECS services and applications:

```bash
cd ../../optus-ecs-lab/terraform/
terraform init
terraform plan
terraform apply
```

This creates:

- ECS cluster with EC2 capacity providers
- SBC (Kamailio) service with Network Load Balancer
- Mozart Voicemail service with FreeSwitch
- Auto Scaling Groups and Launch Templates
- CloudWatch Log Groups
- IAM roles and policies

### Step 5: Additional Modules (Optional)

Deploy additional components like admin dashboard:

```bash
cd ../../optus-modules/
terraform init
terraform plan
terraform apply
```

## 🔧 Configuration

### Environment Variables

Each module uses environment-specific naming:

- **Lab Environment**: `-lab` suffix
- **Production**: Remove suffix or use `-prod`

### AWS Profile

All modules are configured to use the `Optus` AWS profile:

```hcl
provider "aws" {
  profile = "Optus"
  region  = "ap-southeast-2"
}
```

### Tagging Strategy

Resources are tagged according to Optus standards:

```hcl
tags = {
  o_b_bu         = "networks"
  o_b_pri-own    = "Alfred Lam"
  o_b_bus-own    = "James Burden"
  o_t_app-plat   = "networks"
  o_t_app        = "voicemail"
  o_t_env        = "poc"
  o_t_app-own    = "Brian Easson"
  o_t_tech-own   = "Serena Feng"
  o_b_cc         = "gk881infra"
  o_s_app-class  = "cat2"
  o_b_project    = "VMS"
  o_s_data-class = "conf_non_pii"
  o_t_app-role   = "app"
  o_a_avail      = "24x7"
  o_s_sra        = "00642"
  o_t_dep-mthd   = "hybrid"
  o_t_lifecycle  = "inbuild"
}
```

## 📊 Module Dependencies

```mermaid
graph TD
    A[optus-backend-config] --> B[optus-vpc]
    B --> C[optus-db-lab]
    C --> D[optus-ecs-lab]
    D --> E[optus-modules]
  
    B --> |VPC ID, Subnets| C
    B --> |VPC ID, Subnets| D
    C --> |DB Endpoints| D
    D --> |ECS Cluster| E
```

### Key Outputs and Inputs

| Module        | Key Outputs                   | Used By                     |
| ------------- | ----------------------------- | --------------------------- |
| optus-vpc     | vpc_id, subnet_ids            | optus-db-lab, optus-ecs-lab |
| optus-db-lab  | db_endpoints, cache_endpoints | optus-ecs-lab               |
| optus-ecs-lab | cluster_id, service_names     | optus-modules               |

## 🔐 Security Features

### Secrets Management

- AWS Secrets Manager for sensitive data
- SSM Parameter Store for non-sensitive configuration
- Proper IAM role separation

### Network Security

- Private subnets for applications
- Security groups with minimal required access
- VPC isolation and proper routing

### Compliance

- Optus tagging standards implemented
- Encryption at rest and in transit
- Resource naming conventions

## 🔍 Monitoring and Logging

### CloudWatch Integration

- ECS service logs: `/ecs/{service-name}`
- Application logs with proper retention policies
- Auto Scaling metrics and alarms

### Service Discovery

- ECS service discovery for internal communication
- Load balancer health checks
- Application-level health endpoints

## 🛠️ Troubleshooting

### Common Issues

1. **State Backend Access**

   ```bash
   # Verify S3 bucket exists and access
   aws s3 ls s3://optus-vpc-state --profile Optus
   ```
2. **Module Dependencies**

   - Always deploy in order: backend → vpc → db → ecs → modules
   - Use `terraform output` to verify required values between modules
3. **ECS Service Deployment**

   ```bash
   # Check ECS service status
   aws ecs describe-services --cluster mozart-lab-cluster --services service-name --profile Optus
   ```
4. **Database Connectivity**

   ```bash
   # Test RDS connectivity from ECS task
   aws ecs execute-command --cluster mozart-lab-cluster --task task-id --container container-name --command "ping db-endpoint" --interactive --profile Optus
   ```

### Debug Commands

```bash
# Show Terraform state
terraform show

# Refresh state
terraform refresh

# Validate configuration
terraform validate

# Format code
terraform fmt -recursive
```

## 🔄 Migration from Monolithic

To migrate from the original `norwood-staging` setup:

1. **Export existing resources**:

   ```bash
   terraform show > current-state.txt
   ```
2. **Import existing resources** into new modules:

   ```bash
   terraform import module.vpc.aws_vpc.main vpc-xxxxxx
   ```
3. **Gradually refactor** by moving resources between state files

## 🤝 Contributing

### Code Standards

- Use consistent naming conventions
- Follow Terraform best practices
- Include proper variable descriptions
- Add outputs for reusable values

### Testing

```bash
# Validate syntax
terraform validate

# Plan before apply
terraform plan

# Use workspace for testing
terraform workspace new test
```

## 📝 Version History

- **v2.0** - Modular architecture implementation
- **v1.0** - Original monolithic setup (norwood-staging)

---

**Note**: This infrastructure supports the Optus Voicemail Management System (VMS) with high availability, scalability, and compliance with Optus operational standards.
