terraform {
  backend "s3" {
    bucket         = "optus-ecs-lab-state"  # your S3 bucket
    key            = "terraform.tfstate" # path inside the bucket
    region         = "ap-southeast-2"             # your AWS region
    encrypt        = true                         # enable SSE-S3 encryption
    # kms_key_id   = "arn:aws:kms:…"               # optional: use a customer-managed KMS key
    # dynamodb_table = "terraform-modules-lock-table"            # optional: for state locking
    #acl            = "bucket-owner-full-control"  # optional: default permissions
    profile        = "Optus"
  }
}

