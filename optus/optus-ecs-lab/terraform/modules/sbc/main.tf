# Kamailio resources

# Create SSM parameters for kamailio configuration 
resource "aws_ssm_parameter" "kamailio_config" {
  for_each = {
    "MYPUBLICKAMAILIO"          = "kamailio-nlb-a232ff11a7a26225.elb.ap-southeast-2.amazonaws.com:55060" 
    "MYSQLPUBLICIP"             = "kamailio-cluster.cluster-cdygk0cswvru.ap-southeast-2.rds.amazonaws.com"
    "PATH"                      = "/usr/sbin/kamailio:/usr/sbin/:/usr/bin:/usr/local:/usr/local/sbin/"
    "MYIP"                      = "***********"
  }

  name        = "/kamailio-lab/${each.key}"
  description = "Kamailio LAB application configuration parameter"
  type        = "String"
  value       = each.value

  tags = {
    Name = "kamailio-lab-${each.key}"
  }
}

data "aws_launch_template" "existing" {
  filter {
    name   = "tag:Name"
    values = ["mozart-lab-launch_template"]
  }
}

data "aws_ecs_cluster" "existing" {
  cluster_name = "mozart-lab-cluster"
}

resource "aws_autoscaling_group" "ecs-lab-kamailio" {
  name                 = "kamailio-lab-asg"
  vpc_zone_identifier  = var.subnets
  # launch_template = data.aws_launch_template.existing.id
  min_size             = 1
  max_size             = 1
  desired_capacity     = 1

  launch_template {
    id      = data.aws_launch_template.existing.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "${var.app_name}-ecs-instance"
    propagate_at_launch = true
  }

  tag {
    key                 = "AmazonECSManaged"
    value               = ""
    propagate_at_launch = true
  }
  depends_on = [data.aws_launch_template.existing]
}

resource "aws_ecs_capacity_provider" "ec2" {
  name = "${var.app_name}-ec2-capacity-provider"

  auto_scaling_group_provider {
    auto_scaling_group_arn = aws_autoscaling_group.ecs-lab-kamailio.arn
    
    managed_scaling {
      maximum_scaling_step_size = 1000
      minimum_scaling_step_size = 1
      status                    = "ENABLED"
      target_capacity           = 100
    }
  }
}

#resource "aws_ecs_cluster_capacity_providers" "example" {
#  cluster_name = data.aws_ecs_cluster.existing.cluster_name

#  capacity_providers = [aws_ecs_capacity_provider.ec2.name]

#  default_capacity_provider_strategy {
#    base              = 1
#    weight            = 100
#    capacity_provider = aws_ecs_capacity_provider.ec2.name
#  }
#}

resource "aws_iam_policy" "kamailio_secrets_access" {
  name        = "kamailio-lab-secrets-access"
  description = "Allow kamailio ECS tasks to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "ssm:GetParameters",
          "ssm:GetParameter",
          "ssm:DescribeParameters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_security_group" "kamailio_nlb" {
  name        = "kamailio-lab-nlb-sg"
  description = "Allow SIP traffic for kamailio server"
  vpc_id      = var.vpc_id

  ingress {
    protocol    = "udp"
    from_port   = 55060
    to_port     = 55060
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 55060
    to_port     = 55060
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "kamailio-lab-nlb-sg"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb" "kamailio" {
  name               = "kamailio-lab-nlb"
  internal           = false
  load_balancer_type = "network"
  security_groups    = [aws_security_group.kamailio_nlb.id]
  subnets            = var.alb_subnets

  tags = {
    Name = "kamailio-lab-nlb"
  }
}

resource "aws_security_group" "kamailio" {
  name        = "kamailio-lab-sg"
  description = "Allow inbound access from the NLB only for kamailio server"
  vpc_id      = var.vpc_id

  ingress {
    protocol        = "udp"
    from_port       = 55060
    to_port         = 55060
    security_groups = [aws_security_group.kamailio_nlb.id]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 55060
    to_port     = 55060
    security_groups = [aws_security_group.kamailio_nlb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "kamailio-lab-sg"
  }
}

resource "aws_lb_target_group" "kamailio" {
  name        = "kamailio-lab-target-group"
  port        = 55060
  protocol    = "UDP"
  vpc_id      = var.vpc_id
  target_type = "instance"

  health_check {
    protocol            = "TCP"
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 30
    interval            = 60
    port                = "traffic-port"
  }

  tags = {
    Name = "kamailio-lab-target-group"
  }
  
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb_listener" "kamailio" {
  load_balancer_arn = aws_lb.kamailio.arn
  port              = 55060
  protocol          = "UDP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.kamailio.arn
  }
}


resource "aws_ecs_task_definition" "kamailio" {
  family                   = "kamailio-lab-task"
  network_mode             = "bridge"  # or "host" if using host networking
  requires_compatibilities = ["EC2"]   # or ["FARGATE"] if using Fargate
  execution_role_arn       = aws_iam_role.kamailio_execution_role.arn
  task_role_arn            = aws_iam_role.kamailio_task_role.arn
  cpu                      = var.cpu      # 2 vCPU
  memory                   = var.memory      # 4GB memory

  container_definitions = jsonencode([{
    name      = "kamailio-lab"
    # image     = "961341535886.dkr.ecr.ap-southeast-2.amazonaws.com/test/kamailio:latest"
    image     = "961341535886.dkr.ecr.ap-southeast-2.amazonaws.com/test/kamailio@sha256:999ef2ed651599ba6af1bf6623222e0026c14778320f7b4880a60f4e828affac"
    essential = true

    portMappings = [{
      containerPort = 55060
      hostPort      = 55060
      protocol      = "udp"
    }]

    secrets = concat(
        # Non-sensitive parameters from SSM Parameter Store
        [
          for key in keys(aws_ssm_parameter.kamailio_config) : {
            name      = key
            valueFrom = aws_ssm_parameter.kamailio_config[key].arn
          }
        ])

    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-group         = "/ecs/kamailio-lab"
        awslogs-region        = "ap-southeast-2"
        awslogs-stream-prefix = "kamailio"
      }
    }
  }])
}

resource "aws_ecs_service" "kamailio" {
  name            = "kamailio-lab-service"
  cluster         = data.aws_ecs_cluster.existing.id
  task_definition = aws_ecs_task_definition.kamailio.arn
  desired_count   = 1
  launch_type     = null

  capacity_provider_strategy {
    capacity_provider = "${var.app_name}-ec2-capacity-provider"
    weight            = 100
    base              = 1
  }

  #network_configuration {
  #  security_groups  = [aws_security_group.kamailio.id]
  #  subnets          = module.networking.public_subnets
  #   assign_public_ip = true
  #}

  load_balancer {
    target_group_arn = aws_lb_target_group.kamailio.arn
    container_name   = "kamailio-lab"
    container_port   = 55060
  }

  lifecycle {
    ignore_changes = [
      task_definition     # ← prevents roll‑backs
    ]
  }

  depends_on = [aws_lb_listener.kamailio]

  tags = {
    Name = "kamailio-lab-service"
  }
}

# Supporting Resources

resource "aws_iam_role" "kamailio_execution_role" {
  name = "ecs-kamailio-lab-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

resource "aws_iam_role_policy_attachment" "kamailio_execution_role_policy" {
  role       = aws_iam_role.kamailio_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_role_policy_attachment" "kamailio_secrets_policy" {
  role       = aws_iam_role.kamailio_execution_role.name
  policy_arn = aws_iam_policy.kamailio_secrets_access.arn
}

resource "aws_iam_role" "kamailio_task_role" {
  name = "ecs-kamailio-lab-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

resource "aws_cloudwatch_log_group" "kamailio" {
  name              = "/ecs/kamailio-lab"
  retention_in_days = 30
}
