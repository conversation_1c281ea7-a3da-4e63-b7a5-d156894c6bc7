provider "aws" {
  profile = "Optus"
  region  = "ap-southeast-2"
}

resource "aws_ecs_cluster" "ecs_lab" {
  name = "mozart-lab-cluster"

  tags = {
    Name = "mozart-lab-cluster"
  }
}

resource "aws_iam_instance_profile" "ec2_instance_profile" {
  name = "mozart-lab-ec2-instance-profile"
  role = aws_iam_role.ec2_instance_role.name
}

resource "aws_launch_template" "ecs_lab" {
  name_prefix          = "mozart-lab-ecs-"
  image_id             = data.aws_ami.ecs_optimized.id
  instance_type        = "t3.medium"
  # security_groups      = [aws_security_group.instance.id]
  iam_instance_profile {
    name  = aws_iam_instance_profile.ec2_instance_profile.name
  }
  key_name             = null
  user_data            = base64encode(<<-EOF
                          #!/bin/bash
                          echo ECS_CLUSTER=${aws_ecs_cluster.ecs_lab.name} >> /etc/ecs/ecs.config
                          EOF
                        )

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Name = "mozart-lab-launch_template"
  }

}

data "aws_ami" "ecs_optimized" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-ecs-hvm-*-x86_64-ebs"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

resource "aws_ecs_cluster_capacity_providers" "example" {
  cluster_name = aws_ecs_cluster.ecs_lab.name

  capacity_providers = [
    module.sbc.kamailio_ec2_capacity_provider_name,
    module.mozart-voicemail.voicemail_ec2_capacity_provider_name
    ]
  
  #default_capacity_provider_strategy {
  #  base              = 1
  #  weight            = 100
  #  capacity_provider = " "
  #}
  depends_on = [
    module.sbc, 
    module.mozart-voicemail
  ]
}

# Add a custom policy for additional EC2 permissions
resource "aws_iam_role_policy" "ec2_instance_additional_policy" {
  name = "mozart-lab-ec2-additional-policy"
  role = aws_iam_role.ec2_instance_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ec2:DescribeInstances",
          "ec2:DescribeTags",
          "ssm:GetParameters",
          "ssm:GetParameter",
          "ssm:DescribeParameters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}


resource "aws_iam_role" "ec2_instance_role" {
  name = "mozart-lab-ec2-instance-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role" "ecs_task_execution_role" {
  name = "mozart-lab-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_role_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Add permissions for Secrets Manager
resource "aws_iam_policy" "secrets_manager_access" {
  name        = "mozart-lab-secrets-manager-access"
  description = "Allow ECS tasks in LAB env to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "ssm:GetParameters",
          "ssm:GetParameter",
          "ssm:DescribeParameters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_secrets_manager_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = aws_iam_policy.secrets_manager_access.arn
}

resource "aws_iam_role" "ecs_task_role" {
  name = "mozart-lab-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "task_role_secrets_manager_policy" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.secrets_manager_access.arn
}


resource "aws_iam_role_policy_attachment" "ec2_instance_role_policy" {
  role       = aws_iam_role.ec2_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

# Create secrets for sensitive values
resource "aws_secretsmanager_secret" "app_secrets" {
  for_each = {
    # Add secrets (sensitive values)
    # "DB_PASSWORD-LAB"     = "db-password"
     "AAI_API_KEY_LABB"     = "aai-api-key"
     "AAI_HOSTNAME_LABB"    = "aai-hostname"
     "AAI_WEBHOOK_URL_LABB" = "aai-webhook-url"
     "APNS_KEY_LABB"        = "apns-key"
     "JWT_SECRET_LABB"      = "jwt-secret"
     "AUTH_PASSWORD_LABB"   = "admin-dashboard-auth-password"
     "TWILIO_AUTH_TOKEN_LABB" = "twilio-auth-token"
     # Database and Redis connection strings with proper values
  } 

  name = "/mozart-lab/${each.key}"
  
  tags = {
    Name = "mozart-lab-${each.key}"
  }
}


#### S3 resources and policies

# S3 Bucket for Mozart application artifacts
resource "aws_s3_bucket" "mozart_artifacts" {
  bucket = "lab-mozart-assets-bucket" 

  tags = {
    Name        = "lab-mozart-assets-bucket"
    Application = "Mozart"
  }
}

resource "aws_s3_bucket_public_access_block" "mozart_artifacts_public_access" {
  bucket = aws_s3_bucket.mozart_artifacts.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_versioning" "mozart_artifacts_versioning" {
  bucket = aws_s3_bucket.mozart_artifacts.id
  versioning_configuration {
    status = "Enabled"
  }
}

# IAM Policy for Mozart services to access the S3 bucket
resource "aws_iam_policy" "mozart_s3_access_policy" {
  name        = "MozartS3AccessPolicy-lab" 
  description = "Allows Mozart services to access the S3 artifact bucket"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.mozart_artifacts.arn
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:DeleteObject",
          "s3:DeleteObjectVersion" # Required for versioned buckets
        ]
        Resource = [
          "${aws_s3_bucket.mozart_artifacts.arn}/*"
        ]
      }
    ]
  })
}

#######################################################

# Create SSM parameters for app configuration (non-sensitive)
resource "aws_ssm_parameter" "app_config" {
  for_each = {
    # Non-sensitive environment variables
    "AWS_REGION"           = "ap-southeast-2"
    "APNS_ENVIRONMENT"     = "lab"
    "REDIS_MAX_CONNECTION" = "50"
    "SMS_PROVIDER"         = "twilio"
    "EMAIL_PROVIDER"       = "aws"
    "AWS_S3_BUCKET_NAME"   = aws_s3_bucket.mozart_artifacts.id
    "TWILIO_ACCOUNT_SID"   = "AC91dd724da37a87d80a309b761d0128ad"
    "SMS_FROM_NUMBER"      = "+***********"
    "AWS_FROM_EMAIL"       = "<EMAIL>"
  }

  name        = "/mozart-lab/${each.key}"
  description = "Mozart LAB application configuration parameter"
  type        = "String"
  value       = each.value

  tags = {
    Name = "mozart-lab-${each.key}"
  }
}

###########################

locals {
  secret_arns = {
    AAI_API_KEY            = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/AAI_API_KEY_LABB-E7i9it"
    AAI_HOSTNAME           = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/AAI_HOSTNAME_LABB-15bCor"
    AAI_WEBHOOK_URL        = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/AAI_WEBHOOK_URL_LABB-jTOS0m"
    APNS_KEY               = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/APNS_KEY_LABB-H1DHkp"
    JWT_SECRET             = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/JWT_SECRET_LABB-eRv1Jk"
    AUTH_PASSWORD          = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/AUTH_PASSWORD_LABB-H1DHkp"
    TWILIO_AUTH_TOKEN      = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/TWILIO_AUTH_TOKEN_LABB-83PZHq"
  }
  ssm_parameters = {
    "DB_CONNECTION"           = "/mozart-lab/DB_CONNECTION"
    "REDIS_CONNECTION"     = "/mozart-lab/REDIS_CONNECTION"
  }  
}

data "aws_ssm_parameter" "app_config" {
  for_each = local.ssm_parameters
  name     = each.value
}

# Fetch each secret
data "aws_secretsmanager_secret_version" "secrets" {
  for_each  = local.secret_arns
  secret_id = each.value
}

# Extract values into a usable map
locals {
  secret_values = {
    for key, secret in data.aws_secretsmanager_secret_version.secrets :
    key => secret.secret_string
  }
}

module "sbc" {
  source         = "./modules/sbc"
  app_name       = "kamailio-lab"
}

module "mozart-voicemail" {
  source         = "./modules/mozart-voicemail"
  app_name       = "voicemail-lab"
  environment_variables = [
    {
      name  = "APP_ENV"
      value = "development"
    }
  ]
    secrets = concat(
    # Non-sensitive parameters from SSM Parameter Store
    [
      for key in keys(aws_ssm_parameter.app_config) : {
        name      = key
        valueFrom = aws_ssm_parameter.app_config[key].arn
      }
    ],
    [
      for key in keys(data.aws_ssm_parameter.app_config) : {
        name      = key
        valueFrom = data.aws_ssm_parameter.app_config[key].arn
      }  
    ],
    # Sensitive values from Secrets Manager
    [
      for key, arn in local.secret_arns : {
        name      = key
        valueFrom = arn
      }
    ]
  ) 
  
}

# module "admin-dashboard" {
#   source = "./modules/admin-dashboard"
# }