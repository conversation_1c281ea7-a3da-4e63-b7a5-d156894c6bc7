provider "aws" {
  profile = "Optus"
  region  = "ap-southeast-2"
  default_tags {
    tags = {
      o_b_bu         = "networks"
      o_b_pri-own    = "<PERSON> Lam"
      o_b_bus-own    = "<PERSON>"
      o_t_app-plat   = "networks"
      o_t_app        = "voicemail"
      o_t_env        = "poc"
      o_t_app-own    = "<PERSON>"
      o_t_tech-own   = "Serena Feng"
      o_b_cc         = "gk881infra"
      o_s_app-class  = "cat2"
      o_b_project    = "VMS"
      o_s_data-class = "conf_non_pii"
      o_t_app-role   = "app"
      o_a_avail      = "24x7"
      o_s_sra        = "00642"
      o_t_dep-mthd   = "hybrid"
      o_t_lifecycle  = "inbuild"
    }
  }
}

# S3 bucket for Terraform state
resource "aws_s3_bucket" "tf_state" {
  bucket = var.bucket_name

  tags = {
    Name        = "Terraform State Bucket"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_versioning" "enabled" {
  bucket = aws_s3_bucket.tf_state.id

  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "default" {
  bucket = aws_s3_bucket.tf_state.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "block" {
  bucket = aws_s3_bucket.tf_state.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# DynamoDB table for state locking
resource "aws_dynamodb_table" "tf_locks" {
  name           = var.dynamodb_table_name
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "LockID"

  attribute {
    name = "LockID"
    type = "S"
  }

  tags = {
    Name        = "Terraform Lock Table"
    Environment = var.environment
  }
}
