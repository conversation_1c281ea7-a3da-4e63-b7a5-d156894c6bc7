provider "aws" {
  profile = "Optus"
  region  = "ap-southeast-2"
}

data "aws_availability_zones" "available" {
  state = "available"
}
 

locals {
  selected_azs = slice(data.aws_availability_zones.available.names, 0, 2)
  tags = {
    o_b_bu         = "networks"    
    o_b_pri-own    = "Alfred Lam"   
    o_b_bus-own    = "<PERSON>" 
    o_t_app-plat   = "networks"    
    o_t_app        = "voicemail"   
    o_t_env        = "poc"          
    o_t_app-own    = "<PERSON>" 
    o_t_tech-own   = "Serena Feng"  
    o_b_cc         = "gk881infra"   
    o_s_app-class  = "cat2"         
    o_b_project    = "VMS"          
    o_s_data-class = "conf_non_pii"
    o_t_app-role   = "app"         
    o_a_avail      = "24x7"        
    o_s_sra        = "00642"       
    o_t_dep-mthd   = "hybrid"      
    o_t_lifecycle  = "inbuild"     
  }
}

resource "aws_vpc" "lab" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_support   = true
  enable_dns_hostnames = true

  tags = merge(
   local.tags,
   {
    Name = "optus-vpc"
   }
 )  
}


resource "aws_subnet" "public" {
  count                   = 2
  vpc_id                  = aws_vpc.lab.id
  cidr_block              = "10.0.${count.index}.0/24"
#  availability_zone       = data.aws_availability_zones.available.names[count.index]
  availability_zone       = local.selected_azs[floor(count.index / 1)]
  map_public_ip_on_launch = true

  tags = merge(
   local.tags,
   {
    Name = "optus-public-subnet-${count.index}"
   }
  )  
}

resource "aws_subnet" "private" {
  count             = 10
  vpc_id            = aws_vpc.lab.id
  cidr_block        = "10.0.${count.index + 10}.0/24"
#  availability_zone = data.aws_availability_zones.available.names[count.index]
  availability_zone       = local.selected_azs[floor(count.index / 5)]

  
  tags = merge(
  local.tags, 
   {
    Name = "optus-private-subnet-${count.index}"
   }
  )
}

resource "aws_subnet" "database" {
  count             = 2
  vpc_id            = aws_vpc.lab.id
  cidr_block        = "10.0.${count.index + 20}.0/24"
#  availability_zone = data.aws_availability_zones.available.names[count.index]
  availability_zone       = local.selected_azs[floor(count.index / 1)]
  
  
  tags = merge(
   local.tags, 
   {
    Name = "optus-database-subnet-${count.index}"
   }
  )
}

#resource "aws_subnet" "elasticache" {
#  count             = 2
#  vpc_id            = aws_vpc.lab.id
#  cidr_block        = "10.0.${count.index + 30}.0/24"
#  availability_zone = data.aws_availability_zones.available.names[count.index]

#  tags = {
#    Name = "mozart-elasticache-subnet-${count.index}"
#  }
#}

resource "aws_internet_gateway" "lab" {
  vpc_id = aws_vpc.lab.id

  tags = {
    Name = "optus-igw"
  }
}

resource "aws_route_table" "public" {
  vpc_id = aws_vpc.lab.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.lab.id
  }

  tags = {
    Name = "optus-public-rt"
  }
}

resource "aws_route_table_association" "public" {
  count          = 2
  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
}

resource "aws_eip" "nat" {
  domain = "vpc"

  tags = {
    Name = "optus-nat-eip"
  }
}

resource "aws_nat_gateway" "lab" {
  allocation_id = aws_eip.nat.id
  subnet_id     = aws_subnet.public[0].id

  tags = {
    Name = "optus-nat"
  }
}

resource "aws_route_table" "private" {
  vpc_id = aws_vpc.lab.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.lab.id
  }

  tags = {
    Name = "optus-private-rt"
  }
}

resource "aws_route_table_association" "private" {
  count          = 10
  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private.id
}

resource "aws_route_table_association" "database" {
  count          = 2
  subnet_id      = aws_subnet.database[count.index].id
  route_table_id = aws_route_table.private.id
}

#resource "aws_route_table_association" "elasticache" {
#  count          = 2
#  subnet_id      = aws_subnet.elasticache[count.index].id
#  route_table_id = aws_route_table.private.id
#}

