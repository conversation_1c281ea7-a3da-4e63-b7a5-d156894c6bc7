###############################################################################
# BUILD STAGE – compile Kamailio with every module
###############################################################################
FROM debian:bookworm AS build

ARG KAM_VERSION=6.0.2

RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
        ca-certificates \
        curl \
        build-essential \
        clang \
        flex \
        bison \
        git \
        pkg-config \
        cmake \
        libssl-dev \
        libcurl4-openssl-dev \
        libpcre2-dev \
        libxml2-dev \
        libsystemd-dev \
        libunistring-dev \
        libevent-dev \
        libpq-dev \
        libmariadb-dev-compat \
        libhiredis-dev \
        libjansson-dev && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /usr/src
RUN curl -fsSL "https://github.com/kamailio/kamailio/archive/refs/tags/${KAM_VERSION}.tar.gz" \
    | tar -xzf - && \
    mv "kamailio-${KAM_VERSION}" kamailio
WORKDIR /usr/src/kamailio

# build *everything* – core + all modules
RUN make include_modules="all" cfg && \
    make -j$(nproc) Q=0 && \
    make install && \
    ldconfig

###############################################################################
# RUNTIME STAGE – tiny image with libpq + Kamailio
###############################################################################
FROM debian:bookworm-slim

# minimal runtime libs for TLS & Postgres
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
        libpq5 ca-certificates postgresql-client && \
    rm -rf /var/lib/apt/lists/*

# copy freshly built Kamailio binaries
COPY --from=build /usr/local/ /usr/local/

# copy default configs (customise as you like)
COPY --from=build /usr/local/etc/kamailio/ /usr/local/etc/kamailio/

# simple kamctlrc tuned for Postgres, override via env if needed
RUN sed -i 's/^DBENGINE=.*/DBENGINE=PGSQL/' /usr/local/etc/kamailio/kamctlrc

###############################################################################
# ENTRYPOINT WRAPPER – waits for Postgres, creates schema once
###############################################################################
# COPY docker-entrypoint.sh /usr/local/bin/
# RUN chmod +x /usr/local/bin/docker-entrypoint.sh

EXPOSE 5060/udp 5060/tcp
STOPSIGNAL SIGQUIT
# ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["kamailio","-DD","-E","-f","/usr/local/etc/kamailio/kamailio.cfg"]
