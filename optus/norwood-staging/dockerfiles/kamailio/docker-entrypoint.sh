#!/usr/bin/env bash
set -euo pipefail

# ---------------------------------------------------------------------------
# Configurable via environment or docker-compose.yml
# ---------------------------------------------------------------------------
DBHOST="${DBHOST:-postgres}"
DBPORT="${DBPORT:-5432}"
DBNAME="${DBNAME:-kamailio}"
DBUSER="${DBUSER:-kamailio}"
DBPW="${DBPW:-kamailio}"

export PGPASSWORD="$DBPW"

echo "Waiting for Postgres at $DBHOST:$DBPORT ..."
until psql -h "$DBHOST" -U "$DBUSER" -p "$DBPORT" -d "$DBNAME" -c '\q' 2>/dev/null; do
  sleep 2
done
echo "Postgres is up – continuing."

SCHEMA_PRESENT=$(psql -h "$DBHOST" -U "$DBUSER" -p "$DBPORT" -d "$DBNAME" -tA \
  -c "SELECT 1 FROM pg_tables WHERE schemaname='public' AND tablename='version';")

if [[ -z "$SCHEMA_PRESENT" ]]; then
  echo "Initialising Kamailio database schema in $DBNAME ..."
  yes | kamdbctl create
else
  echo "Kamailio database already initialised."
fi

exec "$@"
