# -----------------------------------------------------------------------------
# Debian Buster FreeSWITCH build, hardened to remove critical CVEs (no Azure SDK)
# -----------------------------------------------------------------------------
FROM debian:buster AS builder

# -----------------------------------------------------------------------------
# Switch Buster repos to the archive mirror (regular mirrors were removed July 2025)
# -----------------------------------------------------------------------------
RUN set -eux; \
    sed -i 's|http://deb.debian.org/debian|http://archive.debian.org/debian|g' /etc/apt/sources.list; \
    sed -i 's|http://deb.debian.org/debian-security|http://security-archive.debian.org/debian-security|g' /etc/apt/sources.list || true; \
    echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/99no-check-valid; \
    apt-get -o Acquire::Check-Valid-Until=false update

ARG SIGNALWIRE_TOKEN
ARG GIT_TOKEN

ENV DEBIAN_FRONTEND=noninteractive \
    LANG=C.UTF-8 \
    PATH=/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/bin

# -----------------------------------------------------------------------------
# 1. Base packages & build tool‑chain
# -----------------------------------------------------------------------------
RUN set -eux; \
    apt-get update; \
    apt-get install -y --no-install-recommends \
        curl ca-certificates gnupg2 wget git unzip xz-utils \
        build-essential cmake automake autoconf libtool libtool-bin pkg-config \
        nasm yasm libssl-dev lsb-release python3 python3-pip python3-venv \
        liblz4-tool python3-dev libffi-dev python3-setuptools; \
    rm -rf /var/lib/apt/lists/*

# -----------------------------------------------------------------------------
# 2. Upgrade Python cryptography ➜ 3.3.2 (CVE‑2020‑36242)
RUN python3 -m pip install --no-cache-dir --upgrade pip wheel && \
    python3 -m pip install --no-cache-dir 'cryptography==3.3.2' && \
    apt-get purge -y --autoremove libffi-dev python3-setuptools

# -----------------------------------------------------------------------------
# 3. Build & install libuv 1.48.0 (CVE-2024-24806)
# -----------------------------------------------------------------------------
RUN set -eux; \
    curl -L https://dist.libuv.org/dist/v1.48.0/libuv-v1.48.0.tar.gz | tar -xz -C /tmp; \
    cd /tmp/libuv-v1.48.0; sh autogen.sh; ./configure --prefix=/usr; make -j"$(nproc)"; make install; \
    cd / && rm -rf /tmp/libuv-v1.48.0;



# -----------------------------------------------------------------------------
# 4. Build & install zlib 1.3.1 (CVE-2023-45853)
# -----------------------------------------------------------------------------
RUN set -eux; \
    curl -L https://zlib.net/zlib-1.3.1.tar.gz | tar -xz -C /tmp; \
    cd /tmp/zlib-1.3.1; ./configure; make -j"$(nproc)"; make install; \
    cd / && rm -rf /tmp/zlib-1.3.1; \
    dpkg --force-depends --purge zlib1g-dev || true libmpg123-dev

# -----------------------------------------------------------------------------
# 5. SignalWire repo & FreeSWITCH build dependencies
# -----------------------------------------------------------------------------
RUN set -eux; \
    wget --http-user=signalwire --http-password=${SIGNALWIRE_TOKEN} -O /usr/share/keyrings/signalwire-freeswitch-repo.gpg \
         https://freeswitch.signalwire.com/repo/deb/debian-release/signalwire-freeswitch-repo.gpg; \
    echo "deb [signed-by=/usr/share/keyrings/signalwire-freeswitch-repo.gpg] https://freeswitch.signalwire.com/repo/deb/debian-release/ buster main" > /etc/apt/sources.list.d/freeswitch.list; \
    echo "deb-src [signed-by=/usr/share/keyrings/signalwire-freeswitch-repo.gpg] https://freeswitch.signalwire.com/repo/deb/debian-release/ buster main" >> /etc/apt/sources.list.d/freeswitch.list; \
    # authenticate APT to SignalWire repo
    echo "machine freeswitch.signalwire.com login signalwire password ${SIGNALWIRE_TOKEN}" \
         > /etc/apt/auth.conf; chmod 0600 /etc/apt/auth.conf; \

    sed -Ei 's/^# deb-src/deb-src/' /etc/apt/sources.list; \
    apt-get update; \
    
    apt-get install -y --no-install-recommends \
        libavformat-dev liblua5.1-0-dev luarocks libpcre3-dev libcurl4-openssl-dev \
        libmicrohttpd-dev libjpeg-dev libsqlite3-dev libz-dev libswscale-dev \
        libspeexdsp-dev libedit-dev libtiff-dev haveged uuid-dev libldns-dev libreadline-dev libncurses-dev \
        libopus-dev libsndfile-dev libshout3-dev libmpg123-dev libmp3lame-dev libasound2 \
        libluajit-5.1-dev libyaml-dev python-dev libluajit-5.1 \
        libyaml-dev \
        libsrtp2-dev libopencore-amrnb-dev libopencore-amrnb0 \
        libopencore-amrwb-dev libopencore-amrwb0 libvo-amrwbenc-dev libvo-amrwbenc0 \
        signalwire-client-c; \
    rm -rf /var/lib/apt/lists/*
# ─── install Lua helper modules ───────────────────────────────────────────────
RUN set -eux; \
      luarocks install luasocket   && \
      luarocks install dkjson      && \
      luarocks install lua-cjson   && \
      luarocks install xml2lua; \
      # modules are now in /usr/local/share/lua/5.1; remove the installer itself
      dpkg --force-depends --purge luarocks || true
# -----------------------------------------------------------------------------
# 6. Clone FreeSWITCH and prune unwanted modules
# -----------------------------------------------------------------------------
WORKDIR /usr/local/src
RUN git clone https://${GIT_TOKEN}@github.com/norwoodsystems/freeswitch-core -b voicemail freeswitch
# prune modules that would pull extra deps
RUN sed -i '/mod_v8/d;/mod_java/d;/mod_event_zmq/d;/mod_pgsql/d;/mod_mariadb/d' /usr/local/src/freeswitch/modules.conf

# -----------------------------------------------------------------------------
# 7. Build spandsp 3.x (required for fax)
RUN git clone https://github.com/freeswitch/spandsp.git /usr/local/src/spandsp && \
    cd /usr/local/src/spandsp && git checkout e59ca8f || true && \
    ./bootstrap.sh && ./configure && make -j"$(nproc)" && make install && ldconfig

# 8. Build sofia‑sip ≥ 1.13.6 (required by mod_sofia)
RUN git clone https://github.com/freeswitch/sofia-sip.git /usr/local/src/sofia-sip && \
    cd /usr/local/src/sofia-sip && git checkout v1.13.16 || true && \
    ./bootstrap.sh && ./configure && make -j"$(nproc)" && make install && ldconfig

# 9. Build FreeSWITCH
# -----------------------------------------------------------------------------
RUN set -eux; \
    cd /usr/local/src/freeswitch; \
    JOBS=$(nproc); ./bootstrap.sh -j "$JOBS"; \
    PKG_CONFIG_PATH=/usr/local/lib/pkgconfig ./configure --with-lws=yes --with-extra=yes; \
    make -j"$(nproc)"; make install

# -----------------------------------------------------------------------------
# 10. Static ffmpeg 5.1.4 (no libaom/libmysofa/lapack)
# -----------------------------------------------------------------------------
RUN set -eux; \
    cd /tmp; curl -L https://ffmpeg.org/releases/ffmpeg-5.1.4.tar.xz | tar -xJ; \
    cd ffmpeg-5.1.4; ./configure --prefix=/usr/local --disable-shared --enable-static \
        --disable-libmysofa --disable-libaom --disable-doc \
        --disable-debug --disable-avdevice --disable-postproc --disable-avfilter --disable-network; \
    make -j"$(nproc)"; make install; \
    cd / && rm -rf /tmp/ffmpeg-5.1.4*

# -----------------------------------------------------------------------------
# 11. Purge vulnerable packages
# -----------------------------------------------------------------------------
RUN set -eux; \
    dpkg --force-depends --purge python3-dev libffi-dev python3-setuptools python-dev python2.7-dev libpython2.7 python2.7 libaom* liblapack* libmysofa* zeromq* libuv1 libuv1-dev cmake haveged libasound2 libasound2-data libluajit-5.1-dev \
         python3-cryptography ca-certificates-java openjdk-11-jre-headless default-jre-headless || true; \
    apt-get autoremove -y --purge; \
    apt-get update; apt-get install -y --no-install-recommends libkeyutils1; \
    apt-get clean

# -----------------------------------------------------------------------------
# 12. Final CVE clean-up (run APT first, then force-purge zlib)
# -----------------------------------------------------------------------------
RUN set -eux; \
    # 1) Let APT do its normal housekeeping **while zlib1g is still present**
    apt-get autoremove -y --purge; apt-get clean; \
    \
    # 2) Provide SONAME link to our /usr/local copy
    ln -sf /usr/local/lib/libz.so.1 /lib/x86_64-linux-gnu/libz.so.1; \
    \
    # 3) Now forcibly purge the vulnerable packages (no more APT afterwards)
    dpkg --force-depends --purge zlib1g zlib1g-dev e2fsprogs libext2fs2 || true

# -----------------------------------------------------------------------------
# 13. “strip” stage  – final runtime image with aggressive CVE pruning
# -----------------------------------------------------------------------------
FROM debian:buster-slim AS strip         

# Switch to archive mirrors (Debian 10 is discontinued, but we need apt-clean)
RUN set -eux; \
    sed -i 's|http://deb.debian.org/debian|http://archive.debian.org/debian|g' /etc/apt/sources.list; \
    sed -i 's|http://deb.debian.org/debian-security|http://security-archive.debian.org/debian-security|g' /etc/apt/sources.list || true; \
    echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/99no-check-valid

ENV PATH=/usr/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
WORKDIR /usr/local/freeswitch

# ——— bring in everything from the builder we still need ————————————————
COPY --from=builder /usr /usr
COPY --from=builder /lib /lib
COPY --from=builder /usr/local /usr/local

# -----------------------------------------------------------------------------
# 13-A.  Strategy B – strip every Python wheel except ‘cryptography’
#         (handles both /usr/local/.../site-packages and /usr/lib/.../dist-packages)
# -----------------------------------------------------------------------------
RUN set -eux; \
    pyver=$(python3 -c 'import sys;print("{}.{}".format(*sys.version_info[:2]))'); \
    for site in \
        "/usr/local/lib/python${pyver}/site-packages" \
        "/usr/lib/python${pyver}/site-packages" \
        "/usr/lib/python${pyver}/dist-packages" \
    ; do \
        [ -d "$site" ] || continue; \
        echo "pruning $site"; \
        find "$site" -maxdepth 1 -mindepth 1 \
             ! -name 'cryptography' \
             ! -name 'cryptography-*' \
             -exec rm -rf '{}' '+'; \
        rm -rf "$site"/{pip*,setuptools*,wheel*} || true; \
        find "$site" -name '*.pyc' -delete || true; \
    done

# -----------------------------------------------------------------------------
# 13-B.  Remove residual Python-3 wheels (Werkzeug/Flask/Celery … CVEs)
# -----------------------------------------------------------------------------
RUN set -eux; \
    # 1) purge every Python-3 package that may have slipped through
    dpkg --force-depends --purge \
        python3 python3.?-minimal python3.? \
        python3-venv python3-pip python3-wheel \
        libpython3.?* python3-distutils || true; \
    \
    # 2) clean dangling deps
    apt-get -o Acquire::Check-Valid-Until=false autoremove -y --purge || true; \
    \
    # 3) wipe any wheel directories that were copied from the builder
    find /usr       -type d -name 'site-packages' -path '*python3*' -prune -exec rm -rf '{}' '+' || true; \
    find /usr/local -type d -name 'site-packages' -path '*python3*' -prune -exec rm -rf '{}' '+' || true

# -----------------------------------------------------------------------------
# 13-C.  High-risk native packages we can live without
#   • e2fsprogs / libext2fs2  (CVE-2022-1304)
#   • gcc-8 tool-chain        (CVE-2018-12886)
#   • perl-modules-5.28       (CVE-2020-16156 / 2023-31484)
# -----------------------------------------------------------------------------
RUN set -eux; \
    dpkg --force-depends --purge e2fsprogs libext2fs2 || true; \
    dpkg --force-depends --purge gcc-8 g++-8 cpp-8 libstdc++6-8-dev \
                              libgcc-8-dev libasan5 libubsan1 || true; \
    dpkg --force-depends --purge perl-modules-5.28 || true; \
    # leave perl-base (required by dpkg & adduser), leave python-minimal, leave systemd
    apt-get -o Acquire::Check-Valid-Until=false autoremove -y --purge || true; \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/*

# ─── 13-D.  Remove leftover wheels that the scanner still sees ────────────────
RUN set -eux; \
    # 1) nuke any cryptography / pip wheels that slipped through
    find /usr /usr/local -type d \( -name 'cryptography*' -o -name 'pip*' \) -prune -exec rm -rf '{}' '+' || true; \
    # 2) remove their metadata directories
    find /usr /usr/local -type d -name '*.dist-info' -prune \
         \( -name 'cryptography-*' -o -name 'pip-*' \) \
         -exec rm -rf '{}' '+' || true; \
    # 3) final sweep of stray .py / .pyc we don’t need
    find /usr /usr/local -name '*.py'  -o -name '*.pyc' | grep -E '(cryptography|pip)' | xargs -r rm -f

# ─── 13-E.  Bring back libcom-err (required by libkrb5/gnutls) ────────────────
RUN set -eux; \
    apt-get -o Acquire::Check-Valid-Until=false update; \
    apt-get -y --no-install-recommends install libcom-err2; \
    apt-get clean; \
    rm -rf /var/lib/apt/lists/*

# -----------------------------------------------------------------------------
# 13-F.  Point libz.so.1 to the self-built 1.3.1 (already copied from builder)
# -----------------------------------------------------------------------------
RUN ln -sf /usr/local/lib/libz.so.1 /lib/x86_64-linux-gnu/libz.so.1 || true

# ----------------------------------------------------------------------------
# Copy voicemail configuration & entrypoint
# ----------------------------------------------------------------------------
COPY ./conf     /usr/local/freeswitch/conf
COPY ./scripts  /usr/local/freeswitch/scripts
COPY ./sounds   /usr/local/freeswitch/sounds
COPY ./storage  /usr/local/freeswitch/storage
COPY ./entrypoint.sh    /usr/local/freeswitch/
RUN chmod +x /usr/local/freeswitch/entrypoint.sh

# ----------------------------------------------------------------------------
# Runtime user & permissions
# ----------------------------------------------------------------------------
RUN set -eux; \
    groupadd -r freeswitch && useradd -r -g freeswitch -d /usr/local/freeswitch -s /sbin/nologin freeswitch; \
    chown -R freeswitch:freeswitch /usr/local/freeswitch; \
    chmod -R ug=rwX,o= /usr/local/freeswitch; \
    ldconfig

ENTRYPOINT ["/usr/local/freeswitch/entrypoint.sh"]
CMD ["/usr/local/freeswitch/bin/freeswitch"]
