#!/bin/sh

# Save the variable value to a file
echo "$GOOGLE_APPLICATION_API">>/usr/local/freeswitch/freeswitch-api-7867f30c80d7.json

# # Start the Python script in the background
# python3 /usr/local/freeswitch/rest/refreshxml_deamon.py &

# Start Freeswitch as the main process of the container
# exec freeswitch -nonat -conf /usr/local/freeswitch/conf -log /usr/local/freeswitch/log -db /usr/local/freeswitch/db -scripts /usr/local/freeswitch/scripts -storage /usr/local/freeswitch/storage
freeswitch -nonat -conf /usr/local/freeswitch/conf -log /usr/local/freeswitch/log -db /usr/local/freeswitch/db -scripts /usr/local/freeswitch/scripts -storage /usr/local/freeswitch/storage
