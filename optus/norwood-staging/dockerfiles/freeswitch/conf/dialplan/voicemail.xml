<include>
  <context name="voicemail">
    
    <!--Extension where users would normally dial in to access their voicemail msg's.
    The dialstring format for users to access their voicemail msg's is:
    ${useraccountcode}-vmaccess .
    Note: The system will ask the user for their PIN number which is quite normal for
    obvious security reasons
    -->
    <!-- <extension name="Voicemail_Access_For_User">
      <condition field="destination_number" expression="^(.*)-vmaccess$">
	      <action application="jitterbuffer" data="60"/>
        <action application="answer" />
        <action application="export" data="userbox=$1" />
        <action application="voicemail" data="check hosted $${domain} ${userbox}" />
      </condition>
    </extension> -->
    
    <!--The extension where users dial in to a remote voicemail access from a DID-->
    <!-- <extension name="Voicemail_Access_from_DID">
      <condition field="destination_number" expression="^vmain$">
        <action application="answer" />
        <action application="voicemail" data="check hosted $${domain}" />
	
      </condition>
    </extension> -->
    
    <!--The extension where all systems will redirect the calls to voicemail.
    The dialstring format for the system to redirect calls is is:
    ${useraccountcode}-voicemail 
    -->
<!--  Caller ID Check method  -->
<!-- check for a value in an user record  -->
<!-- First Extension: Checks for Caller ID -->
    <extension name="voicemail-lua-implementation">
      <condition field="destination_number" expression="^(.*)-voicemail$">
	      <action application="jitterbuffer" data="60"/>
	      <!-- <action application="jitterbuffer" data="100:200:20"/> -->
	      <!-- <action application="jitterbuffer" data="100"/> -->
        <!-- force SRTP only on this call -->
        <action application="set" data="rtp_enable_srtp=true"/>
        <action application="set" data="rtp_secure_media=required"/>
        <action application="set" data="voicemail_greeting_number=none"/>
        <action application="lua" data="optus-voicemail.lua $1"/> 
      </condition>
    </extension>
<!-- <extension name="voicemail_check_owner">
    <condition field="destination_number" expression="^(.*)-voicemail$">
        <action application="set_user" data="$1@*************"/>
        <action application="set" data="stored_user_number=$1"/>
        <action application="log" data="INFO 1 - outbound_caller_id_name:${outbound_caller_id_name}"/>
        <action application="set" data="stripped_caller_id_number=${replace(${caller_id_number},\+,)}"/>
        <action application="set" data="stripped_outbound_caller_id_name=${replace(${outbound_caller_id_name},\+,)}"/>
        <action application="set" data="stripped_caller_id_number=${regex(m:${caller_id_number}:(\+)?([0-9]*):$2)}"/>
        <action application="set" data="stripped_outbound_caller_id_name=${regex(m:${outbound_caller_id_name}:(\+)?([0-9]*):$2)}"/>

        <action application="log" data="INFO stripped_caller_id_number:${stripped_caller_id_number}"/>
        <action application="log" data="INFO stripped_outbound_caller_id_name:${stripped_outbound_caller_id_name}"/>
        <condition field="${stripped_caller_id_number}" expression="^${stripped_outbound_caller_id_name}$">
            <action application="log" data="INFO CallerID: ${caller_id_number}"/>
            <action application="log" data="INFO Stored Number: ${stored_user_number}"/>
            <action application="log" data="INFO outbound_caller_id_name:${outbound_caller_id_name}"/>
            <action application="answer"/>
            <action application="ivr" data="voicemail_menu"/>
            <action application="hangup"/>
        </condition>
    </condition>
</extension> -->

<!-- Second Extension: Used to leave a voicemail if caller is not the owner -->
<!-- <extension name="voicemail_for_non_owner">
    <condition field="destination_number" expression="^(.*)-voicemail$">
        <action application="set_user" data="$1@*************"/>
        <action application="log" data="INFO CallerID: ${caller_id_number}"/>
        <action application="log" data="INFO Called Number: $1"/>
        <action application="log" data="INFO outbound_caller_id_name:${outbound_caller_id_name}"/>
        <action application="jitterbuffer" data="60"/>
        <action application="set" data="record_waste_resources=true"/>
        <action application="answer" />
        <action application="set" data="skip_instructions=true" />
        <action application="voicemail" data="hosted ************* $1" />
    </condition>
</extension> -->
    <!-- <extension name="External Transfering">
      <condition field="destination_number" expression="^T(.447\d{8,}|.614\d{8}|.1\d{10,}|.3538\d{8,})$">
        <action application="bridge" data="sofia/gateway/byrds/$1"/>
      </condition>
    </extension> -->
    <!-- <extension name="Aida">
      <condition field="destination_number" expression="^(.\d{8,13})$">
	      <action application="jitterbuffer" data="100:200:20"/>
        <action application="lua" data="aida.lua $1"/> 
      </condition>
    </extension> -->
    <!-- <extension name="IVR-Menu">
      <condition field="destination_number" expression="^(888)$">
          <action application="lua" data="ivr-menu.lua $1"/> 
      </condition>
    </extension>
    <extension name="999">
      <condition field="destination_number" expression="^(999)$">
          <action application="lua" data="welcome.lua $1"/> 
      </condition>
    </extension> -->

    <!-- <extension name="Voicemail_Access_For_User">
      <condition field="destination_number" expression="^(.*)-vmaccess$">
	      <action application="jitterbuffer" data="60"/>
        <action application="set" data="playback_delimiter=!"/>
        <action application="export" data="userbox=$1" />
        <action application="lua" data="tui.lua" />
      </condition>
    </extension> -->
  </context>
</include>
