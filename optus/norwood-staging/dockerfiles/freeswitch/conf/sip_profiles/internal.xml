<profile name="internal">
  <!--
      This is a sofia sip profile/user agent.  This will service exactly one ip and port.
      In FreeSWITCH you can run multiple sip user agents on their own ip and port.

      When you hear someone say "sofia profile" this is what they are talking about.
  -->

  <!-- http://wiki.freeswitch.org/wiki/Sofia_Configuration_Files -->
  <!--aliases are other names that will work as a valid profile name for this profile-->
  <aliases>
    <!--
        <alias name="default"/>
    -->
  </aliases>
  <!-- Outbound Registrations -->
  <gateways>
    <gateway name="byrds">
        <param name="proxy" value="$${gateway}:55060" />
        <!-- <param name="proxy" value="************:55060" /> -->
        <param name="register" value="false" />
        <param name="caller-id-in-from" value="true" />
        <param name="username" value="not-used" />
        <param name="password" value="not-used" />
    </gateway> 
  </gateways>

  <domains>
    <!-- indicator to parse the directory for domains with parse="true" to get gateways-->
    <domain name="all" parse="true"/>
    <!-- indicator to parse the directory for domains with parse="true" to get gateways and alias every domain to this profile -->
    <!--<domain name="all" alias="true" parse="true"/>-->
    <!-- <domain name="all" alias="true" parse="false"/> -->
  </domains>

  <settings>


    <!-- inject delay between dtmf digits on send to help some slow interpreters (also per channel with rtp_digit_delay var -->
    <!-- <param name="rtp-digit-delay" value="40"/>-->

    <!--
        When calls are in no media this will bring them back to media
        when you press the hold button.
    -->
    <!--<param name="media-option" value="resume-media-on-hold"/> -->

    <!--
        This will allow a call after an attended transfer go back to
        bypass media after an attended transfer.
    -->
    <!--<param name="media-option" value="bypass-media-after-att-xfer"/>-->

    <!-- Can be set to "_undef_" to remove the User-Agent header -->
    <!-- <param name="user-agent-string" value="FreeSWITCH Rocks!"/> -->

    <param name="debug" value="0"/>
    <!-- If you want FreeSWITCH to shutdown if this profile fails to load, uncomment the next line. -->
    <!-- <param name="shutdown-on-fail" value="true"/> -->
    <param name="sip-trace" value="no"/>
    <param name="sip-capture" value="no"/>

    <!-- Use presence_map.conf.xml to convert extension regex to presence protos for routing -->
    <!-- <param name="presence-proto-lookup" value="true"/> -->


    <!-- Don't be picky about negotiated DTMF just always offer 2833 and accept both 2833 and INFO -->
    <!--<param name="liberal-dtmf" value="true"/>-->


    <!--
        Sometimes, in extremely rare edge cases, the Sofia SIP stack may stop
        responding. These options allow you to enable and control a watchdog
        on the Sofia SIP stack so that if it stops responding for the
        specified number of milliseconds, it will cause FreeSWITCH to crash
        immediately. This is useful if you run in an HA environment and
        need to ensure automated recovery from such a condition. Note that if
        your server is idle a lot, the watchdog may fire due to not receiving
        any SIP messages. Thus, if you expect your system to be idle, you
        should leave the watchdog disabled. It can be toggled on and off
        through the FreeSWITCH CLI either on an individual profile basis or
        globally for all profiles. So, if you run in an HA environment with a
        master and slave, you should use the CLI to make sure the watchdog is
        only enabled on the master.
        If such crash occurs, FreeSWITCH will dump core if allowed. The
        stacktrace will include function watchdog_triggered_abort().
    -->
    <param name="watchdog-enabled" value="no"/>
    <param name="watchdog-step-timeout" value="30000"/>
    <param name="watchdog-event-timeout" value="30000"/>

    <param name="log-auth-failures" value="true"/>
    <param name="forward-unsolicited-mwi-notify" value="false"/>

    <param name="context" value="voicemail"/>
    <param name="rfc2833-pt" value="101"/>
    <!-- port to bind to for sip traffic -->
    <param name="sip-port" value="$${internal_sip_port}"/>
    <param name="dialplan" value="XML"/>
    <param name="dtmf-duration" value="2000"/>
    <param name="inbound-codec-prefs" value="$${global_codec_prefs}"/>
    <param name="outbound-codec-prefs" value="$${global_codec_prefs}"/>
    <param name="rtp-timer-name" value="soft"/>
    <!-- ip address to use for rtp, DO NOT USE HOSTNAMES ONLY IP ADDRESSES -->
    <param name="rtp-ip" value="$${local_ip_v4}"/>
    <!-- ip address to bind to, DO NOT USE HOSTNAMES ONLY IP ADDRESSES -->
    <param name="sip-ip" value="$${local_ip_v4}"/>
    <param name="hold-music" value="$${hold_music}"/>
    <param name="apply-nat-acl" value="nat.auto"/>
    <!-- param name="apply-nat-acl" value="localnet.auto"/ -->
    <!-- param name="apply-nat-acl" value="rfc1918.auto"/ -->

    <!-- (default true) set to false if you do not wish to have called party info in 1XX responses -->
    <!-- <param name="cid-in-1xx" value="false"/> -->

    <!-- extended info parsing -->
    <!-- <param name="extended-info-parsing" value="true"/> -->

    <!--<param name="aggressive-nat-detection" value="true"/>-->
    <!--
        There are known issues (asserts and segfaults) when 100rel is enabled.
        It is not recommended to enable 100rel at this time.
    -->
    <!--<param name="enable-100rel" value="true"/>-->

    <!-- uncomment if you don't wish to try a next SRV destination on 503 response -->
    <!-- RFC3263 Section 4.3 -->
    <!--<param name="disable-srv503" value="true"/>-->

    <!-- Enable Compact SIP headers. -->
    <!--<param name="enable-compact-headers" value="true"/>-->
    <!--
        enable/disable session timers
    -->
    <param name="enable-timer" value="true"/>
    <!--<param name="minimum-session-expires" value="120"/>-->
    <param name="apply-inbound-acl" value="domains"/>
    <!--
        This defines your local network, by default we detect your local network
        and create this localnet.auto ACL for this.
    -->
    <param name="local-network-acl" value="localnet.auto"/>
    <!--<param name="apply-register-acl" value="domains"/>-->
    <!--<param name="dtmf-type" value="info"/>-->


    <!-- 'true' means every time 'first-only' means on the first register -->
    <!--<param name="send-message-query-on-register" value="true"/>-->

    <!-- 'true' means every time 'first-only' means on the first register -->
    <!--<param name="send-presence-on-register" value="first-only"/> -->


    <!-- Caller-ID type (choose one, can be overridden by inbound call type and/or sip_cid_type channel variable -->
    <!-- Remote-Party-ID header -->
    <!--<param name="caller-id-type" value="rpid"/>-->

    <!-- P-*-Identity family of headers -->
    <!--<param name="caller-id-type" value="pid"/>-->

    <!-- neither one -->
    <!--<param name="caller-id-type" value="none"/>-->



    <param name="record-path" value="$${recordings_dir}"/>
    <param name="record-template" value="${caller_id_number}.${target_domain}.${strftime(%Y-%m-%d-%H-%M-%S)}.wav"/>
    <!--enable to use presence -->
    <param name="manage-presence" value="true"/>
    <!-- send a presence probe on each register to query devices to send presence instead of sending presence with less info -->
    <!--<param name="presence-probe-on-register" value="true"/>-->
    <!--<param name="manage-shared-appearance" value="true"/>-->
    <!-- used to share presence info across sofia profiles -->
    <!-- Name of the db to use for this profile -->
    <param name="dbname" value="share_presence"/>
    <param name="presence-hosts" value="$${domain}"/>
    <param name="presence-privacy" value="$${presence_privacy}"/>
    <!-- ************************************************* -->

    <!-- This setting is for AAL2 bitpacking on G726 -->
    <!-- <param name="bitpacking" value="aal2"/> -->
    <!--max number of open dialogs in proceeding -->
    <!--<param name="max-proceeding" value="1000"/>-->
    <!--session timers for all call to expire after the specified seconds -->
    <!--<param name="session-timeout" value="1800"/>-->
    <!-- Can be 'true' or 'contact' -->
    <param name="multiple-registrations" value="true"/>
    <!--set to 'greedy' if you want your codec list to take precedence -->
    <param name="inbound-codec-negotiation" value="generous"/>
    <!-- if you want to send any special bind params of your own -->
    <!--<param name="bind-params" value="transport=udp"/>-->
    <!--<param name="unregister-on-options-fail" value="true"/>-->
    <!-- Send an OPTIONS packet to all registered endpoints -->
    <!--<param name="all-reg-options-ping" value="true"/>-->
    <!-- Send an OPTIONS packet to NATed registered endpoints. Can be 'true' or 'udp-only'. -->
    <!--<param name="nat-options-ping" value="true"/>-->
    <!--<param name="sip-options-respond-503-on-busy" value="true"/>-->
    <!--<param name="sip-messages-respond-200-ok" value="true"/>-->
    <!--<param name="sip-subscribe-respond-200-ok" value="true"/>-->

    <!-- TLS: disabled by default, set to "true" to enable -->
    <param name="tls" value="$${internal_ssl_enable}"/>
    <!-- Set to true to not bind on the normal sip-port but only on the TLS port -->
    <param name="tls-only" value="false"/>
    <!-- additional bind parameters for TLS -->
    <param name="tls-bind-params" value="transport=tls"/>
    <!-- Port to listen on for TLS requests. (5061 will be used if unspecified) -->
    <param name="tls-sip-port" value="$${internal_tls_port}"/>
    <!-- Location of the agent.pem and cafile.pem ssl certificates (needed for TLS server) -->
    <!--<param name="tls-cert-dir" value=""/>-->
    <!-- Optionally set the passphrase password used by openSSL to encrypt/decrypt TLS private key files -->
    <param name="tls-passphrase" value=""/>
    <!-- Verify the date on TLS certificates -->
    <param name="tls-verify-date" value="true"/>
    <!-- TLS verify policy, when registering/inviting gateways with other servers (outbound) or handling inbound registration/invite requests how should we verify their certificate -->
    <!-- set to 'in' to only verify incoming connections, 'out' to only verify outgoing connections, 'all' to verify all connections, also 'subjects_in', 'subjects_out' and 'subjects_all' for subject validation. Multiple policies can be split with a '|' pipe -->
    <param name="tls-verify-policy" value="all"/>
    <!-- Certificate max verify depth to use for validating peer TLS certificates when the verify policy is not none -->
    <param name="tls-verify-depth" value="2"/>
    <!-- If the tls-verify-policy is set to subjects_all or subjects_in this sets which subjects are allowed, multiple subjects can be split with a '|' pipe -->
    <param name="tls-verify-in-subjects" value=""/>
    <!-- TLS version default: tlsv1,tlsv1.1,tlsv1.2 -->
    <param name="tls-version" value="$${sip_tls_version}"/>

    <!-- TLS ciphers default: ALL:!ADH:!LOW:!EXP:!MD5:@STRENGTH  -->
    <param name="tls-ciphers" value="$${sip_tls_ciphers}"/>

    <!-- turn on auto-flush during bridge (skip timer sleep when the socket already has data)
         (reduces delay on latent connections default true, must be disabled explicitly)-->
    <!--<param name="rtp-autoflush-during-bridge" value="false"/>-->

    <!--If you don't want to pass through timestamps from 1 RTP call to another (on a per call basis with rtp_rewrite_timestamps chanvar)-->
    <!--<param name="rtp-rewrite-timestamps" value="true"/>-->
    <!--<param name="pass-rfc2833" value="true"/>-->
    <!--If you have ODBC support and a working dsn you can use it instead of SQLite-->
    <!--<param name="odbc-dsn" value="dsn:user:pass"/>-->

    <!-- Or, if you have PGSQL support, you can use that -->
    <!--<param name="odbc-dsn" value="pgsql://hostaddr=127.0.0.1 dbname=freeswitch user=freeswitch password='' options='-c client_min_messages=NOTICE' application_name='freeswitch'" />-->

    <!--Uncomment to set all inbound calls to no media mode-->
    <!--<param name="inbound-bypass-media" value="true"/>-->

    <!--Uncomment to set all inbound calls to proxy media mode-->
    <!--<param name="inbound-proxy-media" value="true"/>-->

    <!-- Let calls hit the dialplan before selecting codec for the a-leg -->
    <param name="inbound-late-negotiation" value="true"/>

    <!-- Allow ZRTP clients to negotiate end-to-end security associations (also enables late negotiation) -->
    <param name="inbound-zrtp-passthru" value="true"/>

    <!-- this lets anything register -->
    <!--  comment the next line and uncomment one or both of the other 2 lines for call authentication -->
    <param name="accept-blind-reg" value="true"/>

    <!-- accept any authentication without actually checking (not a good feature for most people) -->
    <param name="accept-blind-auth" value="true"/>

    <!-- suppress CNG on this profile or per call with the 'suppress_cng' variable -->
    <!-- <param name="suppress-cng" value="true"/> -->

    <!--TTL for nonce in sip auth-->
    <param name="nonce-ttl" value="60"/>
    <!--Uncomment if you want to force the outbound leg of a bridge to only offer the codec
        that the originator is using-->
    <!--<param name="disable-transcoding" value="true"/>-->
    <!-- Handle 302 Redirect in the dialplan -->
    <!--<param name="manual-redirect" value="true"/> -->
    <!-- Disable Transfer -->
    <!--<param name="disable-transfer" value="true"/> -->
    <!-- Disable Register -->
    <!--<param name="disable-register" value="true"/> -->
    <!-- Used for when phones respond to a challenged ACK with method INVITE in the hash -->
    <param name="NDLB-broken-auth-hash" value="true"/>
    <!-- add a ;received="<ip>:<port>" to the contact when replying to register for nat handling -->
    <param name="NDLB-received-in-nat-reg-contact" value="true"/>
    <param name="auth-calls" value="$${internal_auth_calls}"/>
    <!-- Force the user and auth-user to match. -->
    <param name="inbound-reg-force-matching-username" value="true"/>
    <!-- on authed calls, authenticate *all* the packets not just invite -->
    <param name="auth-all-packets" value="false"/>

    <!-- external_sip_ip
         Used as the public IP address for SDP.
         Can be an one of:
         ip address            - "***********"
         a stun server lookup  - "stun:stun.server.com"
         a DNS name            - "host:host.server.com"
         auto                  - Use guessed ip.
         auto-nat              - Use ip learned from NAT-PMP or UPNP
    -->
    <param name="ext-rtp-ip" value="$${external_rtp_ip}"/>
    <!-- param name="ext-rtp-ip" value="auto-nat"/ -->
    <param name="ext-sip-ip" value="$${external_sip_ip}"/>
    <!-- param name="ext-sip-ip" value="auto-nat"/ -->


    <param name="aggressive-nat-detection" value="true"/>
    <param name="enable-timer" value="false"/>

    <!-- rtp inactivity timeout -->
    <param name="rtp-timeout-sec" value="300"/>
    <param name="rtp-hold-timeout-sec" value="1800"/>
    <!-- VAD choose one (out is a good choice); -->
    <!-- <param name="vad" value="in"/> -->
    <!-- <param name="vad" value="out"/> -->
    <!-- <param name="vad" value="both"/> -->
    <!--<param name="alias" value="sip:**********:5555"/>-->
    <!--
        These are enabled to make the default config work better out of the box.
        If you need more than ONE domain you'll need to not use these options.

    -->
    <!--all inbound reg will look in this domain for the users -->
    <param name="force-register-domain" value="$${domain}"/>
    <!--force the domain in subscriptions to this value -->
    <param name="force-subscription-domain" value="$${domain}"/>
    <!--all inbound reg will stored in the db using this domain -->
    <param name="force-register-db-domain" value="$${domain}"/>


    <!-- uncomment for sip over websocket support -->
    <!--<param name="ws-binding"  value=":5066"/>-->

    <!-- uncomment for sip over secure websocket support -->
    <!-- You need wss.pem in $${certs_dir} for wss -->
    <!--<param name="wss-binding" value=":7443"/>-->
    

    <!--<param name="delete-subs-on-register" value="false"/>-->

    <!-- launch a new thread to process each new inbound register when using heavier backends -->
    <!-- <param name="inbound-reg-in-new-thread" value="true"/> -->

    <!-- enable rtcp on every channel also can be done per leg basis with rtcp_audio_interval_msec variable set to passthru to pass it across a call-->
    <!--<param name="rtcp-audio-interval-msec" value="5000"/>-->
    <!--<param name="rtcp-video-interval-msec" value="5000"/>-->

    <!--force suscription expires to a lower value than requested-->
    <!--<param name="force-subscription-expires" value="60"/>-->

    <!-- add a random deviation to the expires value of the 202 Accepted -->
    <!--<param name="sip-subscription-max-deviation" value="120"/>-->

    <!-- disable register and transfer which may be undesirable in a public switch -->
    <!--<param name="disable-transfer" value="true"/>-->
    <!--<param name="disable-register" value="true"/>-->

    <!--
        enable-3pcc can be set to either 'true' or 'proxy', true accepts the call
        right away, proxy waits until the call has been answered then sends accepts
    -->
    <!--<param name="enable-3pcc" value="true"/>-->

    <!-- use at your own risk or if you know what this does.-->
    <param name="NDLB-force-rport" value="true"/>
    <!--
        Choose the realm challenge key. Default is auto_to if not set.

        auto_from  - uses the from field as the value for the sip realm.
        auto_to    - uses the to field as the value for the sip realm.
        <anyvalue> - you can input any value to use for the sip realm.

        If you want URL dialing to work you'll want to set this to auto_from.

        If you use any other value besides auto_to or auto_from you'll
        loose the ability to do multiple domains.

        Note: comment out to restore the behavior before 2008-09-29
    -->
    <param name="challenge-realm" value="auto_from"/>
    <!--<param name="disable-rtp-auto-adjust" value="true"/>-->
    <!-- on inbound calls make the uuid of the session equal to the sip call id of that call -->
    <!--<param name="inbound-use-callid-as-uuid" value="true"/>-->
    <!-- on outbound calls set the callid to match the uuid of the session -->
    <!--<param name="outbound-use-uuid-as-callid" value="true"/>-->
    <!-- set to false disable this feature -->
    <!--<param name="rtp-autofix-timing" value="false"/>-->

    <!-- set this param to false if your gateway for some reason hates X- headers that it is supposed to ignore-->
    <!--<param name="pass-callee-id" value="false"/>-->

    <!-- clear clears them all or supply the name to add or the name
         prefixed with ~ to remove valid values:

           clear
           CISCO_SKIP_MARK_BIT_2833
           SONUS_SEND_INVALID_TIMESTAMP_2833

    -->
    <!--<param name="auto-rtp-bugs" data="clear"/>-->

    <!-- the following can be used as workaround with bogus SRV/NAPTR records -->
    <!--<param name="disable-srv" value="false" />-->
    <!--<param name="disable-naptr" value="false" />-->

    <!-- The following can be used to fine-tune timers within sofia's transport layer
         Those settings are for advanced users and can safely be left as-is -->

    <!-- Initial retransmission interval (in milliseconds).
         Set the T1 retransmission interval used by the SIP transaction engine.
         The T1 is the initial duration used by request retransmission timers A and E (UDP) as well as response retransmission timer G.   -->
    <!-- <param name="timer-T1" value="500" /> -->

    <!--  Transaction timeout (defaults to T1 * 64).
         Set the T1x64 timeout value used by the SIP transaction engine.
         The T1x64 is duration used for timers B, F, H, and J (UDP) by the SIP transaction engine.
         The timeout value T1x64 can be adjusted separately from the initial retransmission interval T1. -->
    <!-- <param name="timer-T1X64" value="32000" /> -->


    <!-- Maximum retransmission interval (in milliseconds).
         Set the maximum retransmission interval used by the SIP transaction engine.
         The T2 is the maximum duration used for the timers E (UDP) and G by the SIP transaction engine.
         Note that the timer A is not capped by T2. Retransmission interval of INVITE requests grows exponentially
         until the timer B fires.  -->
    <!-- <param name="timer-T2" value="4000" /> -->

    <!--
        Transaction lifetime (in milliseconds).
        Set the lifetime for completed transactions used by the SIP transaction engine.
        A completed transaction is kept around for the duration of T4 in order to catch late responses.
        The T4 is the maximum duration for the messages to stay in the network and the duration of SIP timer K. -->
    <!-- <param name="timer-T4" value="4000" /> -->

    <!-- Turn on a jitterbuffer for every call -->
    <!-- <param name="auto-jitterbuffer-msec" value="60"/> -->


    <!-- By default mod_sofia will ignore the codecs in the sdp for hold/unhold operations
         Set this to true if you want to actually parse the sdp and re-negotiate the codec during hold/unhold.
         It's probably not what you want so stick with the default unless you really need to change this.
    -->
    <!--<param name="renegotiate-codec-on-hold" value="true"/>-->

  </settings>
</profile>
