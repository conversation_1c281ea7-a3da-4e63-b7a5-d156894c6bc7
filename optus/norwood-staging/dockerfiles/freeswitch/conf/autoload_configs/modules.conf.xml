
<configuration name="modules.conf" description="Modules">
  <modules>
    <!-- Loggers (I'd load these first) -->
    <load module="mod_console"/>
    <load module="mod_logfile"/>
    <!-- XML Interfaces -->
    <load module="mod_xml_curl"/>
    <load module="mod_xml_rpc"/>
    <load module="mod_xml_cdr"/>
    <load module="mod_curl"/>
    <!-- Languages -->
    <load module="mod_v8"/>
    <!-- <load module="mod_perl"/> -->
    <load module="mod_python"/>
    <!-- <load module="mod_java"/> -->
    <load module="mod_lua"/>

    <!-- Event Handlers -->
    <load module="mod_cdr_csv"/>
    <load module="mod_event_socket"/>

    <!-- Endpoints -->
    <load module="mod_sofia"/>
    <load module="mod_loopback"/>

    <!-- Applications -->
    <load module="mod_commands"/>
    <load module="mod_db"/>
    <load module="mod_dptools"/>
    <load module="mod_voicemail"/>
    <load module="mod_voicemail_api"/>
    <load module="mod_hash" />
    <load module="mod_httapi"/>
    <load module="mod_http_cache"/>
    <!-- Dialplan Interfaces -->
    <load module="mod_dialplan_xml"/>

    <!-- Codec Interfaces -->
    <!-- Spandsp includes G722, G726 and GSM -->
    <load module="mod_spandsp"/>
    <load module="mod_speex"/>
    <!-- load module="mod_opus"/ -->

    <!-- File Format Interfaces -->
    <load module="mod_sndfile"/>
    <load module="mod_native_file"/>
    <load module="mod_shout"/>
    <load module="mod_local_stream"/>
    <load module="mod_tone_stream"/>

    <!-- Say -->
    <load module="mod_say_en"/>
    <!-- Is mode_shout supported ??? -->
    <load module="mod_unimrcp"/>
    <!-- load module="mod_portaudio"/ -->
    <!-- load module="mod_portaudio_stream"/ -->
    <!-- load module="mod_dingaling"/ -->

    <!-- Third party modules -->
    <!--<load module="mod_nibblebill"/>-->
    <!--<load module="mod_callcenter"/>-->
    <load module="mod_aai_transcription"/>
    <!-- <load module="mod_audio_fork"/> -->
    <load module="mod_google_tts"/>
    <load module="mod_azure_tts"/>
    <!-- <load module="mod_dialogflow"/> -->

  </modules>
</configuration>
