<configuration name="acl.conf" description="Network Lists">
  <network-lists>
    <!-- 
	 These ACL's are automatically created on startup.

	 rfc1918.auto  - RFC1918 Space
	 nat.auto      - RFC1918 Excluding your local lan.
	 localnet.auto - ACL for your local lan.
	 loopback.auto - ACL for your local lan.
    -->

    <list name="lan" default="allow">
      <node type="deny" cidr="************/24"/>
      <node type="allow" cidr="$${domain_ip}/32"/>
      <node type="allow" cidr="$${domain}/32"/>
      <node type="allow" cidr="************/24"/>
      <node type="allow" cidr="*************/32"/>
      <node type="allow" cidr="$${gateway}/32"/>
    </list>

    <!--
	This will traverse the directory adding all users 
	with the cidr= tag to this ACL, when this ACL matches
	the users variables and params apply as if they 
	digest authenticated.
    -->
    <list name="domains" default="deny">
      <!-- domain= is special it scans the domain from the directory to build the ACL -->
      <node type="allow" domain="$${domain_ip}"/>
      <node type="allow" domain="$${domain}"/>
      <node type="allow" domain="$${gateway}"/>
      <!-- use cidr= if you wish to allow ip ranges to this domains acl. -->
      <!-- <node type="allow" cidr="***********/24"/> -->
    </list>
    <list name="socket_acl" default="deny">
      <node type="allow" cidr="0.0.0.0/0"/>
    </list>
  </network-lists>
</configuration>

