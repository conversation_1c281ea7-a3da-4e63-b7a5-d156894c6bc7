<include> <!--This line will be ignored it's here to validate the xml and is optional -->
	<macro name="queue_position">
		<input pattern="^(\d+)$">
			<match>
				<action function="play-file" data="ivr/ivr-you_are_number.wav"/>
				<action function="say" data="$1" method="pronounced" type="items"/>
				<action function="play-file" data="ivr/ivr-in_line.wav"/>
			</match>
		</input>
	</macro>

	<macro name="has_called_conf">
		<input pattern="^(\d+)$">
			<match>
				<action function="play-file" data="$1"/>
                <action function="sleep" data="100"/>
				<action function="play-file" data="conference/conf-has_joined.wav"/>
			</match>
		</input>
	</macro>
	
	<macro name="has_left_conf">
		<input pattern="^(\d+)$">
			<match>
				<action function="play-file" data="$1"/>
                <action function="sleep" data="100"/>
				<action function="play-file" data="conference/conf-has_left.wav"/>
			</match>
		</input>
	</macro>

    <macro name="enter_dest_number">
      <input pattern="^(.*)$">
        <match>
          <action function="sleep" data="1000"/>
          <action function="play-file" data="ivr/ivr-enter_destination_telephone_number.wav"/>
          <action function="sleep" data="1000"/>
        </match>
      </input>
    </macro>

    <macro name="enter_src_number">
      <input pattern="^(.*)$">
        <match>
          <action function="sleep" data="1000"/>
          <action function="play-file" data="ivr/ivr-enter_source_telephone_number.wav"/>
          <action function="sleep" data="1000"/>
        </match>
      </input>
    </macro>

    <macro name="call_forward_set">
      <input pattern="^(\d+):(\d+)$">
        <match>
          <action function="sleep" data="1000"/>
          <action function="play-file" data="ivr/ivr-extension_number.wav"/>
          <action function="sleep" data="400"/>
          <action function="say" data="$1" method="iterated" type="number"/>
          <action function="sleep" data="400"/>
          <action function="play-file" data="digits/2.wav"/>                                                                                                
          <action function="sleep" data="1000"/>
          <action function="play-file" data="ivr/ivr-extension_number.wav"/>
          <action function="sleep" data="400"/>
          <action function="say" data="$2" method="iterated" type="number"/>
          <action function="sleep" data="1000"/>
          <action function="play-file" data="ivr/ivr-call_forwarding_has_been_set.wav"/>
          <action function="sleep" data="1500"/>
        </match>
      </input>
    </macro>

    <macro name="call_forward_cancel">
      <input pattern="^(\d+)$">
        <match>
          <action function="sleep" data="1000"/>
          <action function="play-file" data="ivr/ivr-extension_number.wav"/>
          <action function="sleep" data="400"/>
          <action function="say" data="$1" method="iterated" type="number"/>
          <action function="play-file" data="ivr/ivr-call_forwarding_has_been_cancelled.wav"/>
          <action function="sleep" data="1500"/>
        </match>
      </input>
    </macro>

    <macro name="screen_confirm">
      <input pattern="^(.*)$">
        <match>
          <action function="sleep" data="500"/>
          <action function="play-file" data="ivr/ivr-call_from.wav"/>
          <action function="sleep" data="250"/>
          <!-- Note, be sure to pass in the full path to the file or else!! -->
          <action function="play-file" data="$1"/>
          <action function="sleep" data="500"/>
          <action function="play-file" data="ivr/ivr-accept_reject_voicemail.wav"/>
          <action function="sleep" data="1500"/>
          <action function="play-file" data="ivr/ivr-call_from.wav"/>
          <action function="sleep" data="250"/>
          <action function="play-file" data="$1"/>
          <action function="sleep" data="500"/>
          <action function="play-file" data="ivr/ivr-accept_reject_voicemail.wav"/>
          <action function="sleep" data="1500"/>
        </match>
      </input>
    </macro>

</include>
