<include>

  <macro name="directory_intro">
    <input pattern="^(last_name)" break_on_match="false">
      <match>
        <action function="speak-text" data="Please enter the first few digit of the person last name"/>
      </match>
    </input>
    <input pattern="^(first_name)" break_on_match="false">
      <match>
        <action function="speak-text" data="Please enter the first few digit of the person first name"/>
      </match>
    </input>
    <input pattern="^(last_name):([0-9#*])$" break_on_match="false">
      <match>
        <action function="speak-text" data="to search by first name, press $2"/>
      </match>
    </input>
    <input pattern="^(first_name):([0-9#*])$" break_on_match="false">
      <match>
        <action function="speak-text" data="to search by last name, press $2"/>
      </match>
    </input>
  </macro>

  <macro name="directory_min_search_digits">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="You need to specify a minimum the first $1 letters of the person name, try again."/>
      </match>
    </input>
  </macro>

  <macro name="directory_result_count">
    <input pattern="^0$" break_on_match="true">
      <match>
        <action function="speak-text" data="Your search match no user on this system, try again."/>
      </match>
    </input>
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="$1 result match your search"/>
      </match>
    </input>
  </macro>

  <macro name="directory_result_count_too_large">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="Your search returned too many result, please try again"/>
      </match>
    </input>
  </macro>

  <macro name="directory_result_last">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="No more result"/>
      </match>
    </input>
  </macro>

  <macro name="directory_result_item">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="Result number $1"/>
      </match>
    </input>
  </macro>

  <macro name="directory_result_menu">
    <input pattern="^([0-9#*]),([0-9#*]),([0-9#*]),([0-9#*])$">
      <match>
        <action function="speak-text" data="To select this entry press $1, for the next entry press $2, for the previous entry press $3, to make a new search press $4"/>
      </match>
    </input>
  </macro>

  <macro name="directory_result_at">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="at extension $1"/>
      </match>
    </input>
  </macro>

  <macro name="directory_result_say_name">
    <input pattern="^(.*)$">
      <match>
        <action function="speak-text" data="$1"/>
      </match>
    </input>
  </macro>

</include>
