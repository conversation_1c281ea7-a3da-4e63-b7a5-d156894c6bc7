local mozart = {}
local json = require("dkjson")
local mime = require("mime")
local http = require("socket.http")
local ltn12 = require("ltn12")
local xml2lua = require("xml2lua")
local handler_class = require("xmlhandler.tree")
local handler = handler_class:new()

-- local https = require("ssl.https")
-- local json = require("cjson")

-- Server configuration
local mozart_voicemails_endpoint = os.getenv("MOZART_SERVER") .. "/api/v1/admin/voicemails"
local mozart_greetings_endpoint = os.getenv("MOZART_SERVER") .. "/api/v1/admin/greetings"
local mozart_config_endpoint = os.getenv("MOZART_SERVER") .. "/directory"
local mozart_config_parameter_endpoint = os.getenv("MOZART_SERVER") .. "/users/modify"


local mozart_user_pswrd_headers = {
    ["userid"] = "proton",
    ["password"] = "proton123"
}

local called_number = ""
local global_greetings = nil


--- MARK: Greeting endpoints
-- Function to upload greeting to the server
function mozart.uploadGreeting(acct, filename)
    acct_no_plus = string.gsub(acct, "%+", "")
    local encoded_audio = encodeAudioToBase64(filename)
    if not encoded_audio then
        freeswitch.consoleLog("err", "Failed to encode the greeting file.\n")
        return
    end

    local url =  mozart_greetings_endpoint .. "?acct=" .. acct_no_plus

    local data = '{"file_extension": "wav", "encoded_audio": "' .. encoded_audio .. '"}'

    local response_body = {}
    freeswitch.consoleLog("debug", "uploadGreeting url:" .. url .. "\n")
    local specific_headers = {
        ["Content-Type"] = "application/json",
        ["Content-Length"] = tostring(#data)
    }
    -- Merge headers
    local headers = mergeHeaders(mozart_user_pswrd_headers, specific_headers)

    local res, code, response_headers, status = http.request{
        url = url,
        method = "POST",
        headers = headers,
        source = ltn12.source.string(data),
        sink = ltn12.sink.table(response_body)
    }

    if code == 200 or code == 201 then
        freeswitch.consoleLog("info", "Greeting uploaded successfully.\n")
    else
        freeswitch.consoleLog("err", "Failed to upload greeting. Status: " .. tostring(status) .. "\n")
    end

    -- Print the response
    if response_body then
        freeswitch.consoleLog("info", "Response: " .. table.concat(response_body) .. "\n")
    end
end

-- function mozart.getGreetings(account)
--     local response = {}
--     acct_no_plus = string.gsub(account, "%+", "")

--     freeswitch.consoleLog("info", "Get greetings for:" .. acct_no_plus .. "\n")
--     -- Prepare the request URL and headers
--     local url = mozart_greetings_endpoint .. "?acct=" .. acct_no_plus

--     freeswitch.consoleLog("info", "HTTP request - url:" .. url .. "\n")
--     -- Perform HTTP GET request
--     local res, code, headers, status = http.request{
--         url = url,
--         method = "GET",
--         headers = mozart_user_pswrd_headers,
--         sink = ltn12.sink.table(response)
--     }

--     -- Check if request was successful
--     if code == 200 then
--         local responseBody = table.concat(response)
--         local greetings = json.decode(responseBody)
--         if greetings and greetings.data and greetings.data.greetings then
--             return greetings.data.greetings
--         else
--             freeswitch.consoleLog("warning", "No greetings found.\n")
--             return nil
--         end
--     else
--         freeswitch.consoleLog("warning", "Failed to fetch greetings. HTTP Code: " .. tostring(code) .. "\n")
--         return nil
--     end
-- end

-- Function to delete a voicemail
function mozart.deleteFirstGreeting(greetings, account)
    account_no_plus = string.gsub(account, "%+", "")
    local response = {}

    if not greetings or #greetings == 0 then
        freeswitch.consoleLog("warning", "No greetings - nothing to delete.\n")
        return
    end
    -- Get the first greeting
    local firstGreeting = greetings[1]
    local greeting_id = firstGreeting.id

    -- Prepare the request URL and headers
    local url = mozart_greetings_endpoint .. "/" .. greeting_id .. "?acct=" .. account_no_plus

    freeswitch.consoleLog("info", "HTTP request - url:" .. url .. ",headers:" .. "\n")
    -- Perform HTTP GET request
    local res, code, headers, status = http.request{
        url = url,
        method = "DELETE",
        headers = mozart_user_pswrd_headers,
        sink = ltn12.sink.table(response)
    }
    freeswitch.consoleLog("INFO", "Delete greeting response: " .. table.concat(response) .. "code:" .. code .."\n")
    return code == 200
end
-- Function to delete a voicemail
function mozart.deleteGreeting(greeting_id, account)
    account_no_plus = string.gsub(account, "%+", "")
    local response = {}

    -- Prepare the request URL and headers
    local url = mozart_greetings_endpoint .. "/" .. greeting_id .. "?acct=" .. account_no_plus

    freeswitch.consoleLog("info", "HTTP request - url:" .. url .. ",headers:" .. "\n")
    -- Perform HTTP GET request
    local res, code, headers, status = http.request{
        url = url,
        method = "DELETE",
        headers = mozart_user_pswrd_headers,
        sink = ltn12.sink.table(response)
    }
    freeswitch.consoleLog("INFO", "Delete greeting response: " .. table.concat(response) .. "code:" .. code .."\n")
    return code == 200
end

--- MARK: Voicemail endpoints
-- Function to delete a voicemail
function mozart.deleteVoicemail(account, voicemail_id)
    account_no_plus = string.gsub(account, "%+", "")
    local response = {}

    -- Prepare the request URL and headers
    local url = mozart_voicemails_endpoint .. "/" .. voicemail_id .. "?acct=" .. account_no_plus

    freeswitch.consoleLog("info", "HTTP request - url:" .. url .. ",headers:" .. "\n")
    -- Perform HTTP GET request
    local res, code, headers, status = http.request{
        url = url,
        method = "DELETE",
        headers = mozart_user_pswrd_headers,
        sink = ltn12.sink.table(response)
    }
    freeswitch.consoleLog("INFO", "Delete voicemail response: " .. table.concat(response) .. "code:" .. code .."\n")
    return code == 200
end

function mozart.getVoicemails(account)
    local response_body = {}

    freeswitch.consoleLog("info", "Get voicemails for:" .. account .. "\n")
    -- Prepare the request URL and headers
    local url = mozart_voicemails_endpoint .. "?acct=" .. account

    freeswitch.consoleLog("info", "HTTP request - url:" .. url .. ",headers:" .. "\n")
    -- Perform HTTP GET request
    local res, code, headers, status = http.request{
        url = url,
        method = "GET",
        headers = mozart_user_pswrd_headers,
        sink = ltn12.sink.table(response_body)
    }

    -- Check if request was successful
    if code == 200 then
        local response_text = table.concat(response_body)
        freeswitch.consoleLog("info", "HTTP response: " .. response_text .. "\n")

        -- Decode JSON response
        local decoded, pos, err = json.decode(response_text, 1, nil)
        if err then
            freeswitch.consoleLog("err", "Error decoding JSON: " .. err .. "\n")
            return
        end
        return decoded
    end
    return
end

-- Function to update a voicemail (mark as read or saved)
function mozart.updateVoicemail(account, voicemail_id, isRead, isSaved)
    local url = mozart_voicemails_endpoint .. "/" .. voicemail_id .. "?acct=" .. account
    local response = {}
    local data = json.encode({
        voicemail = {
            isRead = isRead,
            isRecored = false,
            isSaved = isSaved
        }
    })
    local specific_headers = {
        ["Content-Type"] = "application/json",
        ["Content-Length"] = tostring(#data)
    }
    -- Merge headers
    local headers = mergeHeaders(mozart_user_pswrd_headers, specific_headers)

    local _, status_code = http.request{
        method = "PATCH",
        url = url,
        headers = headers,
        source = ltn12.source.string(data),
        sink = ltn12.sink.table(response)
    }

    freeswitch.consoleLog("INFO", "Update voicemail response: " .. table.concat(response) .. "\n")
    return status_code == 200
end

-- Function to download a file from a URL and save it locally
function mozart.downloadFile(url, filename)
    local file = io.open(filename, "wb")
    if not file then
        freeswitch.consoleLog("error", "Failed to open file for writing: " .. filename .. "\n")
        return false
    end

    local response, code, headers, status = http.request{
        url = url,
        sink = ltn12.sink.file(file)
    }

    if code == 200 then
        freeswitch.consoleLog("info", "Downloaded file to " .. filename .. "\n")
        return true
    else
        freeswitch.consoleLog("error", "Failed to download file. HTTP code: " .. tostring(code) .. "\n")
        return false
    end
end
function mozart.downloadFileCurl(url, filename)
   local command = "curl -o " .. filename .. " '" .. url .. "'"
   freeswitch.consoleLog("warning", "downloadFileCurl -cmd:" .. command .. "\n")
   local result = os.execute(command)
   if result == 0 then
       freeswitch.consoleLog("info", "Downloaded file to " .. filename .. "\n")
       return true
   else
       freeswitch.consoleLog("error", "Failed to download file. Command: " .. command .. "\n")
       return false
   end
end

function mozart.deleteGreetings_vr(greetings, account)
if greetings then
    for _, greeting in ipairs(greetings) do
        if greeting.type == "custom" and greeting.isActive then
            -- Try deleting this greeting
            local deleted = mozart.deleteGreeting_vr(greeting.id, account)
            if deleted then
                freeswitch.consoleLog("info", "Deleted greeting: " .. greeting.id .. "\n")
            end
        end
    end
end
end
function mozart.getGreetings(account)
    local response = {}
    local acct_no_plus = string.gsub(account, "%+", "")
    
    freeswitch.consoleLog("info", "Get greetings for: " .. acct_no_plus .. "\n")

    local url = mozart_greetings_endpoint .. "?acct=" .. acct_no_plus
    freeswitch.consoleLog("info", "HTTP request - url: " .. url .. "\n")

    local res, code, headers, status = http.request{
        url = url,
        method = "GET",
        headers = mozart_user_pswrd_headers,
        sink = ltn12.sink.table(response)
    }

    if code == 200 then
        local responseBody = table.concat(response)
        local ok, greetings = pcall(json.decode, responseBody)
        if ok and greetings and greetings.data and greetings.data.greetings then
            for i, greeting in ipairs(greetings.data.greetings) do
                freeswitch.consoleLog("info", string.format(
                    "Greeting[%d]: id=%s, type=%s, active=%s, audioURL=%s\n",
                    i,
                    greeting.id or "nil",
                    greeting.type or "nil",
                    tostring(greeting.isActive),
                    greeting.audioURL or "nil"
                ))
            end
            return greetings.data.greetings
        else
            freeswitch.consoleLog("warning", "Unexpected JSON or no greetings.\n")
            freeswitch.consoleLog("debug", "Raw response: " .. responseBody .. "\n")
            return nil
        end
    else
        freeswitch.consoleLog("warning", "Failed to fetch greetings. HTTP Code: " .. tostring(code) .. "\n")
        if response and #response > 0 then
            freeswitch.consoleLog("debug", "Response body: " .. table.concat(response) .. "\n")
        end
        return nil
    end
end

function mozart.deleteGreeting_vr(greetingId, acct)
    freeswitch.consoleLog("error", "deleteGreeting_vr - id:" .. tostring(greetingId) .. ",acct:" .. tostring(acct) .. "\n")
    if not greetingId or not acct then
        freeswitch.consoleLog("error", "Missing greetingId or acct for delete\n")
        return false
    end

    local acct_no_plus = string.gsub(acct, "%+", "")
    local response = {}

    local delete_url = string.format(
        "http://mozart-alb-1142825309.ap-southeast-2.elb.amazonaws.com/api/v1/admin/greetings/%s?acct=%s",
        greetingId,
        acct_no_plus
    )

    freeswitch.consoleLog("info", "Deleting greeting ID: " .. greetingId .. "\n")
    freeswitch.consoleLog("info", "DELETE URL: " .. delete_url .. "\n")

    local res, code, headers, status = http.request{
        url = delete_url,
        method = "DELETE",
        headers = {
            ["userid"] = "proton",
            ["password"] = "proton123",
            ["Content-Length"] = "0"  -- Important: DELETE may fail without this
        },
        source = ltn12.source.string(""),
        sink = ltn12.sink.table(response)
    }

    if code == 200 or code == 204 then
        freeswitch.consoleLog("info", "Greeting deleted successfully. Code: " .. tostring(code) .. "\n")
        return true
    else
        freeswitch.consoleLog("error", "Failed to delete greeting. HTTP Code: " .. tostring(code) .. "\n")
        if response and #response > 0 then
            freeswitch.consoleLog("debug", "Response body: " .. table.concat(response) .. "\n")
        end
        return false
    end
end

-- Modified function to download the first greeting instead of streaming
function mozart.downloadFirstGreeting(account, save_path)
    local greetings = mozart.getGreetings(account)
    freeswitch.consoleLog("warning", "User:" .. account .. " has: " .. #greetings .. " greeting(s).\n")
    if not greetings then
        freeswitch.consoleLog("warning", "No greetings found for account: " .. account .. "\n")
        return
    end

    local firstGreeting = greetings[1]
    if firstGreeting and firstGreeting.audioURL then
        local filename = save_path .. "/greeting_1" .. "." .. firstGreeting.fileExtension
        if mozart.downloadFileCurl(firstGreeting.audioURL, filename) then
            freeswitch.consoleLog("info", "First greeting downloaded and saved as: " .. filename .. "\n")
        end
    else
        freeswitch.consoleLog("warning", "No audio URL found for the first greeting.\n")
    end
end
function mozart.fetchUserConfig(called_number)
   local response = {}
   local url = mozart_config_endpoint .. "/" .. called_number
   freeswitch.consoleLog("info", "HTTP request - url:" .. url .. "\n")
   local res, code, headers, status = http.request{
       url = url,
       method = "GET",
       headers = mozart_user_pswrd_headers,
       sink = ltn12.sink.table(response)
   }

   if code == 200 then
        local xml_data = table.concat(response)
        freeswitch.consoleLog("warning", "Config - response 200 - xml_data: \n" .. xml_data .. "\n")

        -- Reinitialize handler (important for multiple calls)
        handler = handler_class:new()
        local parser = xml2lua.parser(handler)

        local ok, err = pcall(function()
            parser:parse(xml_data)
        end)

        if not ok then
            freeswitch.consoleLog("err", "XML parsing failed: " .. tostring(err) .. "\n")
            return nil
        end

        -- Confirm structure
        if not handler.root or not handler.root.include or not handler.root.include.user then
            freeswitch.consoleLog("err", "Parsed XML does not contain expected include.user path\n")
            for k, v in pairs(handler.root or {}) do
                freeswitch.consoleLog("info", "Root key: " .. tostring(k) .. "\n")
            end
            return nil
        end
        for k, v in pairs(handler.root) do
            freeswitch.consoleLog("info", "Root key: " .. tostring(k) .. "\n")
        end
       -- Accessing the parsed XML structure
       local user_data = handler.root.include.user
       freeswitch.consoleLog("warning", "Config - user_data: " .. tostring(user_data) .. "\n")
       if not user_data then
           freeswitch.consoleLog("err", "Invalid XML format. No user data found.\n")
           return nil
       end

       --    The following Accessing parameters and Accesssing variables code is just for debugging and logging names and values

    --    -- Accessing parameters
    --    if user_data.params and user_data.params.param then
    --        for _, param in ipairs(user_data.params.param) do
    --            local name = param._attr.name
    --            local value = param._attr.value
    --            freeswitch.consoleLog("info", "Param - Name: " .. name .. ", Value: " .. value .. "\n")
    --        end
    --    else
    --        freeswitch.consoleLog("warning", "No parameters found.\n")
    --    end

    --    -- Accessing variables
    --    if user_data.variables and user_data.variables.variable then
    --        for _, variable in ipairs(user_data.variables.variable) do
    --            local name = variable._attr.name
    --            local value = variable._attr.value
    --            freeswitch.consoleLog("info", "Variable - Name: " .. name .. ", Value: " .. value .. "\n")
    --        end
    --    else
    --        freeswitch.consoleLog("warning", "No variables found.\n")
    --    end

       return user_data
   else
       freeswitch.consoleLog("err", "Failed to fetch user config. HTTP Code: " .. tostring(code) .. "\n")
       return nil
   end
end

-- Function to get the value of a specific param or variable
function mozart.getConfigAttributeValue(data, attrType, attrName)
 
   if not data or not attrType or not attrName then
      freeswitch.consoleLog("ERR", "getAttributeValue - Invalid arguments\n")
      return nil
   end

   if data and data.param then
      for _, param in ipairs(data.param) do
         if param._attr.name == attrName then 
          local value = param._attr.value
          freeswitch.consoleLog("INFO", "getAttributeValue - type:" ..attrType .. ",name:" .. attrName .. ",Found value:" .. param._attr.value .. "\n")
          return param._attr.value
         end
      end
  else
      freeswitch.consoleLog("warning", "No parameters found.\n")
  end

  freeswitch.consoleLog("ERR", "getAttributeValue - Attribute not found\n")
  return nil
end

function mozart.fetchAndSaveUserConfig(called_number, user_config_folder)
   local response = {}
   local url = mozart_config_endpoint .. "/" .. called_number
   freeswitch.consoleLog("info", "HTTP request - url:" .. url .. "\n")
   local res, code, headers, status = http.request{
       url = url,
       method = "GET",
       headers = mozart_user_pswrd_headers,
       sink = ltn12.sink.table(response)
   }

   if code == 200 then
       local user_config_file = user_config_folder .. "/directory/vmusers/" .. called_number .. ".xml"
       local file = io.open(user_config_file, "w")
       if file then
           file:write(table.concat(response))
           file:close()
           freeswitch.consoleLog("info", "User config saved to " .. user_config_file .. "\n")
           return user_config_file
       else
           freeswitch.consoleLog("err", "Failed to save user config file\n")
           return nil
       end
       -- local response_text = table.concat(response)
       -- freeswitch.consoleLog("warning", "Config data for:" .. called_number .. " is: \n" .. response_text .. "\n")
       -- return user_config_file
   else
       freeswitch.consoleLog("err", "Failed to fetch user config. HTTP Code: " .. tostring(code) .. "\n")
       return nil
   end
end
function mozart.updateConfigParameter(account, param, value)
    if value == nil or value == "" then
        -- Variable is either nil or an empty string
        freeswitch.consoleLog("INFO", "Variable is nil or empty\n")
        return false
    else 
        account_no_plus = string.gsub(account, "%+", "")
        local response = {}

        -- Prepare the request URL and headers
        local url = mozart_config_parameter_endpoint .. "?acct=" .. account_no_plus .. "&" .. param .. "=" .. value

        freeswitch.consoleLog("info", "HTTP request - url:" .. url .. "\n")
        -- Perform HTTP GET request
        local res, code, headers, status = http.request{
            url = url,
            -- method = "GET",
            headers = mozart_user_pswrd_headers,
            sink = ltn12.sink.table(response)
        }
        freeswitch.consoleLog("INFO", "Update param:" .. param .. " with value:" .. value .. ",response code:" .. code .."\n")
        return code == 200
    end
end

-- Function to update a specific XML parameter
function mozart.updateXMLParam(filename, param_name, new_value)
    -- Read the existing XML file
    local file = io.open(filename, "r")
    if not file then
        freeswitch.consoleLog("err", "Failed to open file: " .. filename .. "\n")
        return false
    end
    local content = file:read("*a")
    file:close()

    -- Parse the XML
    local parser = xml2lua.parser(handler)
    parser:parse(content)

    -- Update the parameter in the XML structure
    local user = handler.root.include.user
    local updated = false

    -- Update in 'params' section
    for _, param in ipairs(user.params.param) do
        if param._attr.name == param_name then
            param._attr.value = new_value
            updated = true
            break
        end
    end

    -- Update in 'variables' section if not found in params
    if not updated then
        for _, variable in ipairs(user.variables.variable) do
            if variable._attr.name == param_name then
                variable._attr.value = new_value
                updated = true
                break
            end
        end
    end

    if updated then
        -- Serialize back to XML
        local updated_xml = xml2lua.toXml(handler.root, "include")

        -- Save the updated XML back to the file
        local file = io.open(filename, "w")
        if file then
            file:write(updated_xml)
            file:close()
            freeswitch.consoleLog("info", "Updated " .. param_name .. " in " .. filename .. "\n")
            return true
        else
            freeswitch.consoleLog("err", "Failed to save updated file: " .. filename .. "\n")
            return false
        end
    else
        freeswitch.consoleLog("err", "Parameter " .. param_name .. " not found in " .. filename .. "\n")
        return false
    end
end

function mozart.mergeHeaders(global, specific)
   local merged = {}
   -- Copy global headers
   for k, v in pairs(global) do
       merged[k] = v
   end
   -- Add/Override with specific headers
   for k, v in pairs(specific) do
       merged[k] = v
   end
   return merged
end

function mozart.slamDownNotification(called_number, calling_number)
   acct_no_plus = string.gsub(called_number, "%+", "")
   local url =  mozart_voicemails_endpoint .. "?acct=" .. acct_no_plus

   -- JSON data to send
   local data = {
      voicemail = {
         duration = 0,
         toId = called_number,
         fromId = calling_number
      }
   }
   -- Encode the data to JSON format
   local payload = json.encode(data)

   local response_body = {}
   freeswitch.consoleLog("debug", "Upload slamdown url:" .. url .. "\n")
   local specific_headers = {
         ["Content-Type"] = "application/json",
         ["Content-Length"] = tostring(#payload)
   }
   -- Merge headers
   local headers = mozart.mergeHeaders(mozart_user_pswrd_headers, specific_headers)
    freeswitch.consoleLog("debug", "Upload slamdown payload:" .. payload .. "\n")

   local res, code, response_headers, status = http.request{
         url = url,
         method = "POST",
         headers = headers,
         source = ltn12.source.string(payload),
         sink = ltn12.sink.table(response_body)
   }

   if code == 200 or code == 201 then
         freeswitch.consoleLog("info", "Slam down uploaded successfully.\n")
   else
         freeswitch.consoleLog("err", "Failed to upload slam down notificatoin. Status: " .. tostring(status) .. "\n")
   end

   -- Print the response
   if response_body then
         freeswitch.consoleLog("info", "Response: " .. table.concat(response_body) .. "\n")
   end
end

function mozart.playCustomGreeting(greetings)
    if not greetings or #greetings == 0 then
        freeswitch.consoleLog("warning", "No greetings available to play.\n")
        return
    end
    -- Get the first greeting
    local firstGreeting = greetings[1]

    if firstGreeting.audioURL then
        freeswitch.consoleLog("info", "Playing first greeting from URL: " .. firstGreeting.audioURL .. "\n")
        session:streamFile(firstGreeting.audioURL)
    else
        freeswitch.consoleLog("warning", "No audio URL found for the first greeting ID: " .. firstGreeting.id .. "\n")
    end
end

return mozart
