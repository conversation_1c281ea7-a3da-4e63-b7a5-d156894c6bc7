
package.path = package.path .. ";/usr/local/freeswitch/scripts/?.lua"
local mozart = require("mozart")

local new_messages = { "/path/to/new_msg1.wav", "/path/to/new_msg2.wav" }
local saved_messages = { "/path/to/saved_msg1.wav", "/path/to/saved_msg2.wav" }
local storage_dir = "/path/to/voicemail/storage"
local user_vm_folder = "/path/to/voicemail/storage"
local http = require("socket.http")
local ltn12 = require("ltn12")
local json = require("dkjson")
local mime = require("mime")
local xml2lua = require("xml2lua")
local handler = require("xmlhandler.tree")
-- local https = require("ssl.https")
-- local json = require("cjson")

-- Server configuration
local mozart_voicemails_endpoint = os.getenv("MOZART_SERVER") .. "/api/v1/admin/voicemails"
local mozart_greetings_endpoint = os.getenv("MOZART_SERVER") .. "/api/v1/admin/greetings"
local mozart_config_endpoint = os.getenv("MOZART_SERVER") .. "/directory"
local mozart_config_parameter_endpoint = os.getenv("MOZART_SERVER") .. "/users/modify"


local mozart_user_pswrd_headers = {
    ["userid"] = "proton",
    ["password"] = "proton123"
}

local called_number = ""
local global_greetings = nil
local user_config_data = nil

local captured_dtmf = nil

---MARK: Merge HTTP headers 
function mergeHeaders(global, specific)
    local merged = {}
    -- Copy global headers
    for k, v in pairs(global) do
        merged[k] = v
    end
    -- Add/Override with specific headers
    for k, v in pairs(specific) do
        merged[k] = v
    end
    return merged
end

-- Function to read file and encode it in base64
function encodeAudioToBase64(filename)
    local file = io.open(filename, "rb") -- Read in binary mode
    if not file then
        freeswitch.consoleLog("err", "Could not open the file: " .. filename .. "\n")
        return nil
    end

    local content = file:read("*all") -- Read entire file
    file:close()

    -- Encode to base64
    local encoded = mime.b64(content)
    return encoded
end

-- Play voicemail from URL
function playVoicemail(session, url)
  if session:ready() then
      freeswitch.consoleLog("info", "Streaming voicemail directly from URL: " .. url .. "\n")
      session:streamFile(url)
  else
      freeswitch.consoleLog("warning", "Session not ready for playback.\n")
  end
end

-- Process and sort voicemails
function sortVoicemails(voicemails)
  local new_voicemails = {}
  local saved_voicemails = {}

  for _, voicemail in ipairs(voicemails) do
      if not voicemail.isRead then
          table.insert(new_voicemails, voicemail)
      elseif voicemail.isSaved then
          table.insert(saved_voicemails, voicemail)
      end
  end

  return new_voicemails, saved_voicemails
end

--- MARK: Config endpoints
-- Function to fetch user config from HTTP server
-- function fetchAndSaveUserConfig(called_number, user_config_folder)
--     local response = {}
--     local url = mozart_config_endpoint .. "/" .. called_number
--     freeswitch.consoleLog("info", "HTTP request - url:" .. url .. "\n")
--     local res, code, headers, status = http.request{
--         url = url,
--         method = "GET",
--         headers = mozart_user_pswrd_headers,
--         sink = ltn12.sink.table(response)
--     }

--     if code == 200 then
--         local user_config_file = user_config_folder .. "/directory/vmusers/" .. called_number .. ".xml"
--         local file = io.open(user_config_file, "w")
--         if file then
--             file:write(table.concat(response))
--             file:close()
--             freeswitch.consoleLog("info", "User config saved to " .. user_config_file .. "\n")
--             return user_config_file
--         else
--             freeswitch.consoleLog("err", "Failed to save user config file\n")
--             return nil
--         end
--         -- local response_text = table.concat(response)
--         -- freeswitch.consoleLog("warning", "Config data for:" .. called_number .. " is: \n" .. response_text .. "\n")
--         -- return user_config_file
--     else
--         freeswitch.consoleLog("err", "Failed to fetch user config. HTTP Code: " .. tostring(code) .. "\n")
--         return nil
--     end
-- end

--- MARK: Other methods
local function reloadxml()
    -- Execute the 'reloadxml' command
    api = freeswitch.API() -- Create an API object
    local result = api:execute("reloadxml")
    
    -- Check the result for success or failure
    if result:match("OK") then
        -- Handle successful reload
        freeswitch.consoleLog("debug", "XML configuration successfully reloaded.\n")
    else
        -- Handle failed reload
        freeswitch.consoleLog("debug", "Failed to reload XML configuration.\n")
    end
    
end 

local function setDefaultGreeting(config_file)
    local is_custom_greeting = session:getVariable("voicemail_greeting_number")
    -- freeswitch.consoleLog("debug", "voicemail_greeting_number:" .. is_custom_greeting .. "\n")
    if is_custom_greeting ~= nil  then
        freeswitch.consoleLog("debug", "Reading an user config file to set default greeting\n")
        local file = io.open(config_file, "r") -- open the file to read
        if not file then
            freeswitch.consoleLog("debug", "File not found or cannot be opened: " .. tostring(config_file) .. "\n")
            return false
        end

        local content = file:read("*a") -- read the entire file content
        file:close() -- close the file after reading its content

        -- local patternToRemove = "%s*<param name=\"voicemail_greeting_number\" value=\"1\"/>"
        local patternToRemove = "(%s-)(<param%s+name=\"voicemail_greeting_number\"%s+value=\"%d\")"
        -- local pattern = patternToRemove .. "[^\n]*\n?"
        local pattern = patternToRemove .. "[^\n]*"
        freeswitch.consoleLog("debug", "About to remove pattern:" .. patternToRemove .. "\n")
        content = content:gsub(pattern, "")

        -- Remove the line with the given pattern
        patternToRemove = "(%s-)(<variable%s+name=\"voicemail_greeting_number\"%s+value=\"%d\")"
        -- pattern = patternToRemove .. "[^\n]*\n?"
        pattern = patternToRemove .. "[^\n]*"
        freeswitch.consoleLog("debug", "About to remove pattern:" .. patternToRemove .. "\n")
        content = content:gsub(pattern, "")

        -- Open the file to write
        file = io.open(config_file, "w")
        if not file then
            freeswitch.consoleLog("debug", "Failed to open the file for writing: " .. tostring(config_file))
            return false
        end

        file:write(content) -- write the new content to the file
        file:close() -- close the file after writing
        return true
    else
        freeswitch.consoleLog("debug", "Default greeting was already set.\n")
        return false
    end
end

local function revertToDefaultGreeting(greetings, account)
    -- deleteFirstGreeting(greetings, account)
    mozart.deleteGreetings_vr(greetings,account)
    global_greetings = mozart.getGreetings(account)
end

    -- Function to insert a line before the tui-password line
local function save_custom_greeting_setting_in_config(filePath)

    local is_custom_greeting = session:getVariable("voicemail_greeting_number")
    if is_custom_greeting == nil  then
        local file = io.open(filePath, "r") -- Open the file for reading
        if not file then
            return false, "Could not open file for reading."
        end

        freeswitch.consoleLog("debug", "Set custom greeting - add 2 lines in config file\n")
        local content = file:read("*a") -- Read the entire file content
        file:close() -- Close the file

        -- Pattern to find the tui-password line and its leading whitespace
        local patternToFind = "(%s-)(<param%s+name=\"%s-tui%-password\"%s+value=\"%d%d%d%d\")"
        -- local patternToFind = "(<param%s+name=\"%s-tui%-password\"%s+value=\"%d%d%d%d\")"
            
        -- The line to insert
        local lineToInsert = '<param name="voicemail_greeting_number" value="1"/>'

        -- Replace the pattern with the new line inserted before it, preserving the indentation
        local modifiedContent = content:gsub(patternToFind, function(whitespace, tuiPasswordLine)
            return whitespace .. lineToInsert .. "" .. whitespace .. tuiPasswordLine
        end)

        -- Pattern to find the tui-password line and its leading whitespace
        patternToFind = "(%s-)(<variable%s+name=\"%s-tui%-password\"%s+value=\"%d%d%d%d\")"
        -- local patternToFind = "(<param%s+name=\"%s-tui%-password\"%s+value=\"%d%d%d%d\")"
            
        -- The line to insert
        local lineToInsert = '<variable name="voicemail_greeting_number" value="1"/>'

        -- Replace the pattern with the new line inserted before it, preserving the indentation
        local modifiedContent = modifiedContent:gsub(patternToFind, function(whitespace, tuiPasswordLine)
            return whitespace .. lineToInsert .. "" .. whitespace .. tuiPasswordLine
        end)

        -- Open the file for writing
        file = io.open(filePath, "w")
        if not file then
            return false, "Could not open file for writing."
        end

        -- Write the modified content back to the file
        file:write(modifiedContent)
        file:close() -- Close the file
        return true
    else
        freeswitch.consoleLog("debug", "Custom greeting was already SET - leave it!\n")
        return false
    end   
end
    
local function addDefaultTuiPin(filename)
    freeswitch.consoleLog("debug", "Add default TUI pin in config file:" .. filename .. "\n")
    -- Read the entire file into a string
    local file = io.open(filename, "r")
    local content = file:read("*all")
    file:close()

    -- Define the patterns to find and the lines to insert
    local patternForTuiPassword = "(<param%s+name=\"%s-vm%-hlr\"%s+value=\".-\"%s*/>)"
    local lineToInsertForTuiPassword = '%1\n      <param name="tui-password" value="1234"/>'

    local patternForVariable = "(%s*)(<variable%s+name=\"%s-accountcode\"%s+value=\".-\"%s*/>)"
    local lineToInsertForVariable = '%1<variable name="tui-password" value="1234"/>\n%1%2'

    -- Replace the patterns with the new line inserted after/before it
    content = content:gsub(patternForTuiPassword, lineToInsertForTuiPassword)
    content = content:gsub(patternForVariable, lineToInsertForVariable)

    -- Write the modified content back to the file
    file = io.open(filename, "w")
    file:write(content)
    -- file:write(modifiedContent)
    file:close()
end

function record_transfer_msg_to_stop_hash_star(filename, option)
    -- Configuration - Tune these values based on your environment
    freeswitch.consoleLog("warning", "record_transfer_msg_to_stop_hash_star - START\n")

    local config = {
        max_len_secs = 20,          -- Absolute maximum recording time
        initial_silence = 3000,     -- Milliseconds before considering it "silence" (3s)
        silence_threshold = 250,    -- RMS threshold for silence (0-4000)
        min_audio_length = 2000,    -- Minimum recording length in ms (2s)
        stop_keys = "*"           -- Keys to stop recording
    }

    -- 1. Play prompts
    if option == "2" then
        session:streamFile("voicemail/optus-continue-recording-msg.wav") --  "You currently recording the message. Continue recording after the beep then press #"
    else
        session:streamFile("voicemail/609.wav") --  "Begin recording your comment after the beep and then press # when your done"
    end

    session:streamFile("tone_stream://%(1000,0,1000)")
    session:sleep(200)

    -- 2. Set audio processing parameters
    session:setVariable("RECORD_SILENCE_THRESHOLD", config.silence_threshold)
    session:setVariable("RECORD_SILENCE_SECS", config.initial_silence/1000)
    session:setVariable("RECORD_MIN_SECS", config.min_audio_length/1000)
    session:setVariable("RECORD_STEREO", "false") -- Mono for voicemail

    -- 3. Start recording with enhanced parameters
    local record_cmd = string.format(
        "%s %d %d %d %s",
        filename,
        config.max_len_secs * 1000,  -- Convert to ms
        config.silence_threshold,
        config.initial_silence,
        config.stop_keys
    )

    freeswitch.consoleLog("INFO", "record_transfer_msg_to_stop_hash_star - Starting recording: "..record_cmd.."\n")
    
    -- 4. Execute with error handling
    local success = session:execute("record", record_cmd)
    
end

function transferMessage_Menu(voicemail)
  
    local max_attempts = 3
    local attempt = 0
    local option = 0
    local filename = voicemail.storageKey:match("/([^/]+)$") or voicemail.storageKey
    -- Extract the filename after last '/'
    -- local filename = filename:match("([^/]+)$")
    local fname = filename:gsub("%.wav$", "_transfer.wav")
    local transfer_message = user_vm_folder .. "/" .. fname 
    freeswitch.consoleLog("warning","transfer_message:" .. transfer_message .. "\n")

  
    repeat
        -- session:streamFile("voicemail/609.wav") --  "Begin recording your comment after the beep and then press # when your done"

        -- session:streamFile("voicemail/2149.wav") --  "You are in the copy menu"
        -- session:streamFile("voicemail/900.wav") --  "to replay your comment press 1"
        -- session:streamFile("voicemail/608.wav") --  "to continue recording your comment press 2"
        -- session:streamFile("voicemail/796.wav") --  "for delivery options press 5"
        -- session:streamFile("voicemail/625.wav") --  "to delete your comment press 7"
        -- session:streamFile("voicemail/372.wav") --  "to send this message press 9"
        -- session:streamFile("voicemail/744.wav") --  "to return to the previous menu press *"

        freeswitch.consoleLog("warning","transferMessage_Menu - attempt:" .. attempt .. ", transfer_message:" .. transfer_message .. "\n")

        -- session:streamFile("voicemail/609.wav") --  "Begin recording your comment after the beep and then press # when your done"
        -- session:streamFile("tone_stream://%(1000,0,1000)")

        -- record_transfer_msg_to_stop_hash_star(transfer_message, option)
        recording_file_1("voicemail/609.wav", transfer_message, false)
               
        repeat
            attempt = attempt + 1
            freeswitch.consoleLog("warning","transferCopy_Menu - attempt:" .. attempt .. "\n")
            option = transferCopy_Menu(transfer_message, option)
            
            if option == "1" then               -- replay press 1
                session:streamFile("voicemail/your_message_is.wav")
                session:streamFile(transfer_message)
            elseif option == "2" then           -- to continue recording press 2
                recording_file_1("voicemail/optus-continue-recording-msg.wav",transfer_message, true)
                break
            elseif option == "5" then           -- for delivery options press 5
                deliveryOptions_Menu(transfer_message)
            elseif option == "7" then           -- to delete your comment press 7
                deleteFile(transfer_message)
            elseif option == "9" then           -- to send this message press 9
                mozart.sendMessage_api("not ready yet")
                return
            elseif option == "*" then           -- to cancel press *
                return
            end

            if attempt >= max_attempts then
                -- -- Maximum attempts reached, save the greeting and exit
                -- session:sleep(300)
                -- session:streamFile("voicemail/ttsmaker-tui-greeting-saved.wav")
                -- session:sleep(500)
                return
            end
        until option == "2"  or option == "5" or option == "7" or option == "*"
 
    until option == "9" or option == "*" 
end

-- Function to return a call to the voicemail caller
function returnCall(fromId)
    if fromId and fromId ~= "" then
        local dialstring = "sofia/gateway/byrds/" .. fromId
        freeswitch.consoleLog("INFO", "Returning call to: " .. fromId .. "\n")
        session:execute("bridge", dialstring)
    else
        freeswitch.consoleLog("ERR", "Caller ID not found or invalid.\n")
        -- session:streamFile("voicemail/vm-nobody-avail.wav")
    end
end

function getFilesInDirectoryOrderedByTimeAscending(directory)
    local files = {}
    -- local p = io.popen('ls "'..directory..'"')  
    local p = io.popen('ls -t "'..directory..'" | tac') -- 'ls' list files in ascending order based on modification time (with the oldest file first)

    for file in p:lines() do
        table.insert(files, file)
    end
    p:close()
    return files
end

function getFilesInDirectoryOrderedByTimeDescending(directory)
    local files = {}
    local p = io.popen('ls -t "'..directory..'"')  -- 'ls' command opens directory
    for file in p:lines() do
        table.insert(files, file)
    end
    p:close()
    return files
end

function getNewMessages(directory)
    local files = {}

    -- 'ls -t' list files in desending order based on modification time (with the oldest file first)
    -- 'tac' reverses the order of the files so that the newest file is first (asending order)
    -- 'grep -v "_saved\."' excludes files that have been saved
    -- local p = io.popen('ls "'..directory..'"')  
    -- local p = io.popen('ls -t "'..directory..'" | grep -v "_saved\." | tac')
    -- local p = io.popen('ls -t "'..directory..'" | grep -v -e "_saved\\." -e "_recorded\\." -e "greeting_1\\." -e "\\\\.json$" | tac')
    local cmd = ('ls -t "%s" | grep -v -e "_saved\\." -e "_recorded\\."'
           .. ' -e "greeting_1\\." -e "\\\\.json$" | tac'):format(directory)
    local p   = io.popen(cmd)

    for file in p:lines() do
        table.insert(files, file)
    end
    p:close()
    return files
end

function getSavedMessages(directory)
    local files = {}
    -- 'ls -t' list files in desending order based on modification time (with the oldest file first)
    -- 'tac' reverses the order of the files so that the newest file is first (asending order)
    -- 'grep "_saved\."' includes files that have been saved (i.e. files that have "_saved." in the filename)
    -- local p = io.popen('ls "'..directory..'"')  
    local p = io.popen('ls -t "'..directory..'" | tac | grep "_saved\\."')

    for file in p:lines() do
        table.insert(files, file)
    end
    p:close()
    return files
end

function getRecordedMessages(directory)
    local files = {}
    -- 'ls -t' list files in desending order based on modification time (with the oldest file first)
    -- 'tac' reverses the order of the files so that the newest file is first (asending order)
    -- 'grep "_recorded\."' includes files that have been saved (i.e. files that have "_recorded." in the filename)
    -- local p = io.popen('ls "'..directory..'"')  
    local p = io.popen('ls -t "'..directory..'" | tac | grep "_recorded\\."')

    for file in p:lines() do
        table.insert(files, file)
    end
    p:close()
    return files
end

function splitFilename(filename)
    local name, ext = filename:match("(.-)(%.wav)$")
    return name, ext
end

function renameFile(filename, pattern)
    local name, extension = splitFilename(filename)

    -- Rename a file
    local old_name = filename
    local new_name = name .. pattern .. extension
    local status, err = os.rename(old_name, new_name)

    if not status then
        freeswitch.consoleLog("error","Error renaming file: " .. err .. "\n")  
    -- else
    --     freeswitch.consoleLog("debug","File renamed: " .. new_name .. "\n")  
    end
end

function deleteFile(filename)
    local status, err = os.remove(filename)
    if not status then
        freeswitch.consoleLog("error","Error deleting file: " .. err .. "\n") 
    -- else
    --     freeswitch.consoleLog("debug","File deleted: " .. filename .. "\n") 
    end
end


local function record_voicemail_message(filename)
    local max_len_secs = 20 -- maximum length of recording in seconds
    local silence_threshold = 300 -- threshold to consider it silence
    local silence_secs = 3 -- seconds of silence before considering the recording done

    session:sleep(300)
    -- Play the prompt to the user
    session:streamFile("voicemail/optus-record-your-message.wav")
    -- session:streamFile("voicemail/vm-record-greeting.wav")
    session:streamFile("tone_stream://%(1000,0,1000)")
    session:sleep(100)
    session:recordFile(filename, max_len_secs, silence_threshold, silence_secs)
    session:streamFile("voicemail/optus-message-recorded.wav")
    session:sleep(400 )
end

local function on_dtmf_input(s, type, obj, arg)
    if type == "dtmf" and obj.digit == "#" then
        freeswitch.consoleLog("INFO", "DTMF # received — stopping recording.\n")
        return "break"  -- This stops session:recordFile()
    end
    return "continue"
end

local function record_greeting_on_dtmf_input(filename)
    session:streamFile("voicemail/ttsmaker-tui-record-greeting.wav")
    session:streamFile("tone_stream://%(1000,0,1000)")

    session:setInputCallback("onMenuDtmf", "")

    -- Start recording
    session:recordFile(filename, 20, 250, 3)

    -- Cleanup
    session:setInputCallback("", "")  -- Unset the callback
    session:streamFile("voicemail/ttsmaker-tui-greeting-saved.wav")
end

local function record_greeting_on_dtmf_input_and_record(filename)
 -- Play prompt & beep
  session:streamFile("voicemail/ttsmaker-tui-record-greeting.wav")
  session:flushDigits()
  session:setInputCallback("onMenuDtmf", "")
  session:streamFile("tone_stream://%(1000,0,1000)")

  -- Build record command, with '#' as fifth arg
  local max_ms = 20 * 1000
  local silence_th = 0
  local silence_ms = 0
  local stop_keys = "#"        -- ← key terminator
  local cmd = string.format("%s %d %d %d %s",
                            filename,
                            max_ms,
                            silence_th,
                            silence_ms,
                            stop_keys)

    -- session:setVariable("record_terminators", "#")   -- let record own '#'
    session:setVariable("execute_on_dtmf", "")       -- clear any dialplan hook
    session:setVariable("api_on_dtmf", "")        
    cmd = string.format("%s %d 0 0 #", filename, max_ms)
  freeswitch.consoleLog("INFO", "▶ Recording via record(): " .. cmd .. "\n")
    session:execute("record", cmd)

  -- Cleanup and confirm
  session:setInputCallback("", "")
  session:streamFile("voicemail/ttsmaker-tui-greeting-saved.wav")
end

local function record_greeting_deep_seek(filename, max_duration_sec)
   -- Configure DTMF settings
    session:setVariable("playback_terminators", "#")  -- Only terminate on #
    session:setVariable("record_terminator_used", "true")
    session:setVariable("record_append", "false")

    -- Play prompt
  session:streamFile("voicemail/ttsmaker-tui-record-greeting.wav")
  session:streamFile("tone_stream://%(1000,0,1000)")

    -- Start recording with explicit DTMF break
    local recording_cmd = "record " .. filename .. " " .. max_duration_sec .. " 500 0"
    session:execute("record", recording_cmd)

    -- Verify if stopped by # (versus timeout)
    local terminator = session:getVariable("record_terminator")
    if terminator == "#" then
        session:streamFile("voicemail/ttsmaker-tui-greeting-saved.wav")
        return true
    else
        -- Delete incomplete recording if timeout reached
        deleteFile(filename)
        session:streamFile("voicemail/ttsmaker-tui-recording-timeout.wav")
        return false
    end
end

local function record_greeting_recordFile(filename)
    local max_len_secs = 20 -- maximum length of recording in seconds
    local silence_threshold = 300 -- threshold to consider it silence
    local silence_secs = 4 -- seconds of silence before considering the recording done

    freeswitch.consoleLog("warning","START record_greeting: " .. filename .. "\n") 
    -- Play the prompt to the user
    session:streamFile("voicemail/ttsmaker-tui-record-greeting.wav")
    session:streamFile("tone_stream://%(1000,0,1000)") -- Pure tone beep
    session:sleep(200)
    session:recordFile(filename, max_len_secs, silence_threshold, silence_secs)
    session:streamFile("voicemail/ttsmaker-tui-greeting-saved.wav")
    session:sleep(500 )
end

local function record_greeting_origin(filename)
    local max_len_secs = 20 -- maximum length of recording in seconds
    local silence_threshold = 300 -- threshold to consider it silence
    local silence_secs = 2 -- seconds of silence before considering the recording done

    -- Play the prompt to the user
    session:streamFile("voicemail/ttsmaker-tui-record-greeting.wav")
    -- session:streamFile("voicemail/vm-record-greeting.wav")
    session:streamFile("tone_stream://%(1000,0,1000)") -- Pure tone beep
    session:sleep(200)
    session:recordFile(filename, max_len_secs, silence_threshold, silence_secs)
    session:streamFile("voicemail/ttsmaker-tui-greeting-saved.wav")
end


local function record_greeting(filename)
    local max_len_secs = 20        -- maximum length of recording in seconds
    local silence_threshold = 200  -- lower threshold to detect silence more accurately
    local silence_secs = 3         -- shorter silence duration before stopping
    local stop_key = "#"           -- key to manually stop recording

    freeswitch.consoleLog("warning", "START record_greeting: " .. filename .. "\n")

    -- Play the prompt to the user
    session:streamFile("voicemail/ttsmaker-tui-record-greeting.wav")
    session:streamFile("tone_stream://%(1000,0,1000)") -- Pure tone beep
    session:sleep(200)

    -- Start recording with DTMF termination option
    -- session:execute("set", "record_stereo=true") -- Optional: Record in stereo
    session:execute("set", "record_append=false") -- Optional: Avoid appending to existing files

    -- Improved recording with DTMF stop
    local record_cmd = string.format("%s %d %d %d %s", filename, max_len_secs, silence_threshold, silence_secs, stop_key)
    freeswitch.consoleLog("info", "Recording with command: " .. record_cmd .. "\n")

    session:execute("record", record_cmd)

    -- After recording, play confirmation
    session:streamFile("voicemail/ttsmaker-tui-greeting-saved.wav")
    session:sleep(500)
end

local function record_greeting_with_hash(filename)
    local max_len_secs = 60       -- maximum length of recording in seconds
    local silence_threshold = 200 -- lower threshold to detect quieter audio
    local silence_secs = 2        -- reduce the duration of silence required
    local digit_timeout = 5000    -- timeout for digit input

    freeswitch.consoleLog("warning", "START record_greeting: " .. filename .. "\n")

    -- Play the prompt to the user
    session:streamFile("voicemail/ttsmaker-tui-record-greeting.wav")
    session:streamFile("tone_stream://%(1000,0,1000)")
    session:sleep(200)

    -- Start recording with digit termination option
    session:execute("set", "playback_terminators=#")
    session:recordFile(filename, max_len_secs, silence_threshold, silence_secs)

    -- Check if recording was interrupted by pressing '#'
    if session:ready() then
        local digit = session:getDigits(1, "#", digit_timeout)
        if digit == "#" then
            freeswitch.consoleLog("info", "Recording stopped by user pressing #\n")
        end
    end

    session:streamFile("voicemail/ttsmaker-tui-greeting-saved.wav")
    session:sleep(500)
    freeswitch.consoleLog("warning", "Recording completed: " .. filename .. "\n")
end


local function record_greeting_to_stop_hash_star(filename)
    -- Configuration - Tune these values based on your environment
    local config = {
        max_len_secs = 30,          -- Absolute maximum recording time
        initial_silence = 3000,     -- Milliseconds before considering it "silence" (3s)
        silence_threshold = 250,    -- RMS threshold for silence (0-4000)
        min_audio_length = 2000,    -- Minimum recording length in ms (2s)
        beep_file = "tone_stream://%(1000,0,1000)", -- Pure tone beep
        stop_keys = "*",           -- Keys to stop recording
        chunk_size = 20             -- Audio chunk size in ms
    }

    -- 1. Play prompts
    session:streamFile("voicemail/ttsmaker-tui-record-greeting.wav")
    session:streamFile("tone_stream://%(1000,0,1000)")
    session:sleep(200)

    -- 2. Set audio processing parameters
    session:setVariable("RECORD_SILENCE_THRESHOLD", config.silence_threshold)
    session:setVariable("RECORD_SILENCE_SECS", config.initial_silence/1000)
    session:setVariable("RECORD_MIN_SECS", config.min_audio_length/1000)
    session:setVariable("RECORD_STEREO", "false") -- Mono for voicemail

    -- 3. Start recording with enhanced parameters
    local record_cmd = string.format(
        "%s %d %d %d %s",
        filename,
        config.max_len_secs * 1000,  -- Convert to ms
        config.silence_threshold,
        config.initial_silence,
        config.stop_keys
    )

    freeswitch.consoleLog("INFO", "Starting recording with: "..record_cmd.."\n")
    
    -- 4. Execute with error handling
    local success = session:execute("record", record_cmd)
    
    -- -- 5. Verify recording
    -- local file_info = io.popen("soxi "..filename.." 2>&1"):read("*a")
    -- if not success or file_info:match("error") then
    --     freeswitch.consoleLog("ERR", "Recording failed: "..(file_info or "unknown error"))
    --     session:streamFile("voicemail/ttsmaker-tui-recording-failed.wav")
    --     return false
    -- end

    -- 6. Post-recording processing
    -- session:streamFile("voicemail/ttsmaker-tui-greeting-saved.wav")
    -- return true
end

function recording_file(prompt, filename)
    -- Configuration - Tune these values based on your environment
    local config = {
        max_len_secs = 20,          -- Absolute maximum recording time
        initial_silence = 3000,     -- Milliseconds before considering it "silence" (3s)
        silence_threshold = 250,    -- RMS threshold for silence (0-4000)
        min_audio_length = 2000,    -- Minimum recording length in ms (2s)
        stop_keys = "*"             -- Keys to stop recording
    }

    -- 1. Play prompts
    session:streamFile(prompt)
    session:streamFile("tone_stream://%(1000,0,1000)")
    -- 2. Set audio processing parameters
    session:setVariable("RECORD_SILENCE_THRESHOLD", config.silence_threshold)
    session:setVariable("RECORD_SILENCE_SECS", config.initial_silence/1000)
    session:setVariable("RECORD_MIN_SECS", config.min_audio_length/1000)
    session:setVariable("RECORD_STEREO", "false") -- Mono for voicemail

    -- 3. Start recording with enhanced parameters
    local record_cmd = string.format(
        "%s %d %d %d %s",
        filename,
        config.max_len_secs * 1000,  -- Convert to ms
        config.silence_threshold,
        config.initial_silence,
        config.stop_keys
    )
    session:sleep(300) -- delay until beep is played; sometimes beep is heard within the message!!
    -- 4. Execute with error handling
    freeswitch.consoleLog("INFO", "recording_file - START cmd: "..record_cmd.."\n")   
    local success = session:execute("record", record_cmd)
    freeswitch.consoleLog("INFO", "recording_file - END:" .. tostring(success) .. "\n")     
end

function recording_file_1(prompt, filename, appending)
  -- 1) Play your instruction prompt + a beep
  session:streamFile(prompt)
  session:streamFile("tone_stream://%(1000,0,1000)")
  session:flushDigits()                                 -- clear any stray DTMF
  session:sleep(200)                                    -- delay recording until last prompt is played

  -- 2) Clear any competing DTMF hooks so `record` can own the key
--   session:setVariable("execute_on_dtmf", "")               -- disable call-transfer/etc.
--   session:setVariable("api_on_dtmf", "")                   -- disable any API hooks

  -- 3) Tell the record app to use `#` as its terminator
--   session:setVariable("record_terminators", "#")
  session:setVariable("playback_terminators", "#")

  -- 4) Set your silence and length parameters
  session:setVariable("RECORD_SILENCE_THRESHOLD", 250)     -- your RMS threshold
  session:setVariable("RECORD_SILENCE_SECS",    2000/1000) -- initial silence before auto-stop
  session:setVariable("RECORD_MIN_SECS",        2000/1000) -- minimum recording length
  session:setVariable("RECORD_STEREO",         "false")   -- mono
  if appending == true then
    session:setVariable("RECORD_APPEND","true")   -- append recording
  end 

  -- 5) Kick off the record—no need to repeat the stop key in the command
  local max_ms = 20 * 1000
  local cmd = string.format("%s %d 250 2000", filename, max_ms)
--   local max_ms = 20 * 1000
--   local cmd = string.format("%s 20 250 3", filename)
  freeswitch.consoleLog("INFO", "▶ recording_file START: " .. cmd .. "\n")
  session:execute("record", cmd)
  if appending == true then
    session:unsetVariable("RECORD_APPEND")  
  end 
  freeswitch.consoleLog("INFO", "▶ recording_file END\n")
end
function recording_file_2(prompt, filename)
  -- 1) play your prompt + beep
  session:streamFile(prompt)
  session:streamFile("tone_stream://%(1000,0,1000)")
  session:flushDigits()

  -- 2) clear any DTMF hooks (so '#' isn’t swallowed by a transfer or API callback)
  session:setVariable("execute_on_dtmf",   "")
  session:setVariable("api_on_dtmf",       "")

  -- 3) tell record to use '#' as its terminator
  session:setVariable("playback_terminators", "#")

  -- 4) set silence and length params
  session:setVariable("RECORD_SILENCE_THRESHOLD", 250)    -- energy threshold
  session:setVariable("RECORD_SILENCE_SECS",     3)      -- silence-secs
  session:setVariable("RECORD_MIN_SECS",         2)      -- minimum write
  session:setVariable("RECORD_STEREO",           "false")

  -- 5) do the actual record (max 20s, threshold 250, 3s silence)
  local cmd = string.format("%s %d %d", filename, 20, 250)
  freeswitch.consoleLog("INFO", "▶ recording_file START: " .. cmd .. "\n")
  session:execute("record", cmd)
  freeswitch.consoleLog("INFO", "▶ recording_file END\n")
end

function recording_file_DS(prompt, filename)
    -- Configuration - Tuned for voicemail recording
    local config = {
        max_len_secs = 30,          -- Maximum recording time (30s)
        silence_threshold = 200,     -- RMS threshold (0-4000)
        silence_secs = 3,            -- Silence duration to stop (seconds)
        min_secs = 2,                -- Minimum recording length (seconds)
        terminator = "#",            -- Primary stop key
        fallback_terminator = "*",   -- Fallback stop key
        beep = "tone_stream://%(1000,0,1000)",  -- Pre-recording beep
        chunk_size = 10              -- Write audio in 10s chunks
    }

    -- 1. Configure recording environment
    session:setVariable("playback_terminators", config.terminator..config.fallback_terminator)
    session:setVariable("record_terminator_used", "true")
    session:setVariable("record_append", "false")
    session:setVariable("record_sample_rate", "8000")  -- Narrowband
    session:setVariable("recording_final_timeout", "500") -- Final silence timeout

    -- 2. Play prompts with interruptible playback
    session:streamFile(prompt)
    session:streamFile(config.beep)

    -- 3. Build record command with explicit parameters
    local record_cmd = string.format(
        "%s %d %d %d %s",
        filename,
        config.max_len_secs * 1000,  -- max duration (ms)
        config.silence_threshold,    -- silence threshold
        config.silence_secs * 1000, -- silence duration (ms)
        config.terminator            -- terminator char
    )

    -- 4. Execute recording with error handling
    freeswitch.consoleLog("INFO", "Starting recording with cmd: "..record_cmd.."\n")
    local success = session:execute("record", record_cmd)
    
    -- 5. Verify recording completion
    local actual_terminator = session:getVariable("record_terminator") or "timeout"
    local recording_length = tonumber(session:getVariable("record_samples")) / 8000  -- secs
    
    freeswitch.consoleLog("INFO", "Recording finished: terminator=" .. actual_terminator .. ", length=" .. recording_length .. " s, success=" .. tostring(success) .. "\n")

    -- 6. Handle edge cases
    if actual_terminator == config.terminator then
        session:streamFile("voicemail/vm-saved.wav")
    elseif recording_length < config.min_secs then
        os.remove(filename)  -- Delete too-short recordings
        session:streamFile("voicemail/vm-too_short.wav")
        return false
    end
    
    return success
end
function recording_file_DS_1(prompt, filename)
    -- Configuration with aggressive DTMF detection
    local config = {
        max_duration = 20,          -- Maximum recording in seconds
        terminator = "#",           -- Primary stop key
        fallback_terminator = "*",  -- Secondary stop key
        silence_threshold = 250,    -- RMS threshold (0-4000)
        min_length = 3,             -- Minimum recording in seconds
        beep = "tone_stream://%(1000,0,1000)",  -- Pre-recording beep
        dtmf_type = "rfc2833",      -- Force reliable DTMF mode
        dtmf_duration = 200         -- Minimum DTMF duration in ms
    }

    -- 1. Configure DTMF detection aggressively
    session:setVariable("dtmf_type", config.dtmf_type)
    session:setVariable("dtmf_duration", config.dtmf_duration)
    -- session:setVariable("playback_terminators", config.terminator .. config.fallback_terminator)
    session:setVariable("playback_terminators", config.terminator .. config.fallback_terminator)
    session:setVariable("record_terminator_used", "true")

    -- 2. Play prompts with built-in interruption
    session:streamFile(prompt)
    session:streamFile(config.beep)

    -- 3. Build record command with explicit termination
    local record_cmd = string.format(
        "%s %d %d %d %s",
        filename,
        config.max_duration * 1000,
        config.silence_threshold,
        config.min_length * 1000,
        config.terminator
    )

    -- 4. Execute with real-time monitoring
    freeswitch.consoleLog("INFO", "Starting recording with cmd: "..record_cmd.."\n")
    
    -- Start recording in non-blocking mode
    session:execute("record", record_cmd)
    
    -- Monitor for termination
    local start_time = os.time()
    while (os.time() - start_time) < config.max_duration do
        local terminator = session:getVariable("record_terminator")
        if terminator == config.terminator or terminator == config.fallback_terminator then
            freeswitch.consoleLog("INFO", "Recording stopped by DTMF: "..terminator.."\n")
            return true
        end
        session:sleep(100)  -- Check every 100ms
    end

    -- 5. Final verification
    local final_terminator = session:getVariable("record_terminator") or "timeout"
    local duration = tonumber(session:getVariable("record_samples") or 0) / 8000  -- seconds

    freeswitch.consoleLog("INFO", "Final status - Terminator::" .. final_terminator .. ", Duration:" .. duration .. " s\n")

    -- Handle results
    if final_terminator == config.terminator then
        return true
    elseif duration >= config.min_length then
        session:streamFile("voicemail/vm-timeout.wav")
        return true  -- Accept timeout if minimum length met
    else
        os.remove(filename)
        return false
    end
end

-- Function to review the greeting
local function review_greeting(filename)
    session:sleep(500)
    session:streamFile(filename)
    session:sleep(500)
    -- Provide options to the user
    local digits = session:playAndGetDigits(1, 1, 1, 5000, "#", "voicemail/ttsmaker-tui-greeting-options.wav", "", "[1-3#]")

    -- return tonumber(digits)
    return digits
end

-- local function onMenuDtmf(session, type, obj, arg)
--   if type == "dtmf" then
--     captured_dtmf = obj.digit
--     session:breakAudio()  -- This is key to interrupting playback
--   freeswitch.consoleLog("warning","onMenuDtmf - DTMF:" .. obj.digit .. "\n") 
--     return "break"   -- stop the current streamFile()
--   end
--   return "continue"
-- end

-- Callback function for DTMF detection
function onMenuDtmf(s, type, obj, arg)
    if type == "dtmf" then
        captured_dtmf = obj.digit
        freeswitch.consoleLog("info", "DTMF captured: " .. obj.digit .. "\n")
        return "break" -- This will interrupt the current streamFile
    end
    return ""
end

-- local function playInterruptible(prompts)
--   captured_dtmf = nil
--   session:flushDigits()                 -- drop buffered DTMF
--   session:setInputCallback("onMenuDtmf", "")

--   freeswitch.consoleLog("warning","playInterruptible - number of prompts:" .. #prompts .. "\n") 

--   for _, file in ipairs(prompts) do
--     if file ~= nil then
--         session:streamFile(file)
--     end 
--     -- session:execute("playback", file)
--     if captured_dtmf then break end
--   end

--   session:setInputCallback("", "")       -- remove callback
--   return captured_dtmf
-- end

local function playInterruptible(prompts)
    captured_dtmf = nil
    
    -- Flush any existing DTMF digits
    session:flushDigits()
    
    -- Set the input callback - note: use string reference, not quoted function name
    session:setInputCallback("onMenuDtmf", "")
    
    freeswitch.consoleLog("info", "playInterruptible - number of prompts: " .. #prompts .. "\n")
    
    for i, file in ipairs(prompts) do
        if not session:ready() then
            break
        end
        
        freeswitch.consoleLog("info", "Playing file: " .. file .. "\n")
        
        -- Stream the file
        session:streamFile(file)
        
        -- Check if DTMF was captured during playback
        if captured_dtmf then
            freeswitch.consoleLog("info", "Playback interrupted by DTMF: " .. captured_dtmf .. "\n")
            -- Remove the callback
            session:setInputCallback("", "")
            return captured_dtmf
        end
    end
    
    -- Remove the callback
    session:setInputCallback("", "")
    
    return captured_dtmf
end


function transferCopy_Menu(filename, option)
    session:streamFile(filename)
    session:sleep(500)

    local prompts = {
        "voicemail/2149.wav",  -- You are in the copy menu
        "voicemail/900.wav",   -- to replay your comment press 1
        "voicemail/608.wav",   -- to continue recording your comment press 2
        "voicemail/796.wav",   -- for delivery options press 5
        "voicemail/625.wav",   -- to delete your comment press 7
        "voicemail/372.wav",   -- to send this message press 9
        "voicemail/744.wav",   -- to return to the previous menu press *
    }
    local prompts_after_delete = {
        "voicemail/609.wav",   -- to replay your comment press 1
        "voicemail/796.wav",   -- for delivery options press 5
        "voicemail/372.wav",   -- to send this message press 9
        "voicemail/744.wav",   -- to return to the previous menu press *
    }

    local valid_digits = "[12579*#]"
    local prompts_to_play = prompts
    if option == "7" then   -- returned from deleting comment
        prompts_to_play = prompts_after_delete
        valid_digits = "[159*#]"
    end

    -- interruptible playback
    local digit = playInterruptible(prompts_to_play)

    -- if they never pressed in-flight, let them after
    if not digit then
        digit = session:playAndGetDigits(
            1,               -- min digits
            1,               -- max digits
            1,               -- max tries
            3000,            -- timeout ms
            "",              -- no pre-prompt
            "",              -- no invalid prompt
            "",              -- no failure prompt (we already played it)
            "."              -- valid keys
        )
    end

    return digit
end

function deliveryOptions_Menu(filename)
    local max_attempts = 3
    local attempt = 0
    local option = 0

    local prompts = {
        "voicemail/495.wav",    -- for Future delivery press 4
        "voicemail/3549.wav",   -- to Send this message with current delivery options press 9
        "voicemail/912.wav",    -- to cancel press *
    }

    repeat
        attempt = attempt + 1
        -- interruptible playback
        local digit = playInterruptible(prompts)

        -- if they never pressed in-flight, let them after
        if not digit then
            digit = session:playAndGetDigits(
                1,               -- min digits
                1,               -- max digits
                1,               -- max tries
                3000,            -- timeout ms
                "#",              -- terminator
                "voicemail/98.wav",              -- no invalid prompt
                "",              -- no failure prompt (we already played it)
                "[49*#]"         -- valid keys
            )
        end
        
        if digit == "4" then               -- for Future delivery press 4
            futureDelivery_Menu(filename)
        elseif digit == "9" then           -- to Send this message with current delivery options press 9
            mozart.sendMessage_withCurrentDeliveryOptions_api("not ready yet")
            break
        elseif digit == "*" then           -- to cancel press *
            return
        end

        if attempt >= max_attempts then
            return
        end
    until option == "4"  or option == "9" or option == "*"

end

function sendMessage_with_current_delivery_options_Menu(filename)
   local max_attempts = 3
    local attempt = 0
    local option = 0

    local prompts = {
        "voicemail/3733.wav",    -- to send to a phone number press 1
        "voicemail/3201.wav",   -- to send to a group list press 2
        "voicemail/744.wav",    -- to return to the previous menu press *
    }

    repeat
        attempt = attempt + 1
        -- interruptible playback
        local digit = playInterruptible(prompts)

        -- if they never pressed in-flight, let them after
        if not digit then
            digit = session:playAndGetDigits(
                1,               -- min digits
                1,               -- max digits
                1,               -- max tries
                3000,            -- timeout ms
                "#",              -- terminator
                "voicemail/98.wav",              -- no invalid prompt
                "",              -- no failure prompt (we already played it)
                "[12*#]"         -- valid keys
            )
        end
        
        if digit == "1" then               -- to send to a phone number press 1
            -- TODO
            -- find calling number (voicemail.from)
                -- if prepaid: "you are not allowed to dial this number" 
                -- if postpaid -> mozart.sendMessage_api() .......
        elseif digit == "2" then           -- to send to a group list press 2
            break
        elseif digit == "*" then           -- to return to the previous menu press *
            return
        end

        if attempt >= max_attempts then
            return
        end
    until option == "1"  or option == "2" or option == "*"

end

function playDeliveryTime(hours, minutes)
  -- 1) “This message will be delivered in”
  session:streamFile("voicemail/optus-this-message-will-be-delivered-in.wav")

  -- 2) Hours
  session:execute("say", "en number pronounced " .. hours)
  if hours == 1 then
    session:streamFile("voicemail/hour.wav")
  else
    session:streamFile("voicemail/hours.wav")
  end

  -- 3) “and”
  session:streamFile("voicemail/and.wav")

  -- 4) Minutes
  session:execute("say", "en number pronounced " .. minutes)
  if minutes == 1 then
    session:streamFile("voicemail/minute.wav")
  else
    session:streamFile("voicemail/minutes.wav")
  end

  -- 5) Final punctuation or pause (optional)
--   session:streamFile("voicemail/pause-brief.wav")
end

-- Returns:
--  * true      → user pressed * (re-enter time)
--  * "4","9","*" → future-delivery submenu choice
--  * nil       → timeout/hangup
local function confirmDeliveryTime(hours, minutes)
    -- 1) play the chosen delivery time
    playDeliveryTime(hours, minutes)

    -- 2) prompt for “wrong time” override
    session:streamFile("voicemail/if-time-not-correct-press-star-now.wav")
    local opt = session:playAndGetDigits(
    1,1,1,         -- min, max digits, tries
    3000,          -- timeout ms
    "#",           -- terminator
    "",            -- no prompt
    "",            -- no failure prompt
    "\\*"          -- only * is valid
    )

    if opt == "*" then
    -- caller wants to re-enter
    return false
    end

    -- 3) Future Delivery submenu
    local prompts = {
    "voicemail/optus-you-have-selected-future-delivery.wav",
    "voicemail/596.wav",                                    -- to cancel future delivery press 4
    "voicemail/3549.wav",                                   -- to send this message with current delivery options press 9
    "voicemail/912"                                         -- to cancel press *
    }

    local digit = playInterruptible(prompts)
    if not digit then 
        -- 4) collect choice [4,9,*]
        digit = session:playAndGetDigits(
                1, 1, 3,      -- min=1, max=1, tries=3
                2000,         -- timeout
                "", "",       -- no extra prompts
                "voicemail/98.wav",
                "[49*]"
                )
    end 

    return digit  -- "4", "9", "*", or ""/nil
end

function futureDelivery_Menu(filename)
    local max_attempts = 3
    local attempt = 0
    local option = 0
    
    repeat
        session:streamFile("voicemail/1511.wav") -- "enter hours followed by minutes followed by #"

        repeat
            attempt = attempt + 1

            local raw = session:playAndGetDigits(
                1,               -- min digits before timeout
                4,               -- max digits
                1,               -- max tries
                3000,            -- timeout ms
                "#",             -- terminator
                "",              -- no interim prompt
                "",              -- no invalid prompt 
                "[\\d+#]"        -- valid keys
            )
            
            if raw == "" then
                break
            end
            -- strip the trailing '#'
            local digits = raw:gsub("#", "")
            -- must have at least HMM (3 digits) or HHMM (4)
            if #digits < 3 then
                -- too few digits; treat as bad hours
                session:streamFile("voicemail/1514.wav") -- "incorrect hours requested"
                -- return retry_menu()
                break
            end

            -- split hours vs minutes
            local hpart = digits:sub(1, #digits - 2)
            local mpart = digits:sub(-2)

            local hours   = tonumber(hpart)
            local minutes = tonumber(mpart)

            -- validate hours 0–23
            if not hours or hours < 0 or hours > 23 then
                session:streamFile("voicemail/1514.wav")
                -- return retry_menu()
                break
            else
                local result = confirmDeliveryTime(hours ,minutes)
                if result == false then 
                    break
                end

                if digit_1 == "4" then 
                    break
                elseif digit_1 == "9" then
                    sendMessage_with_current_delivery_options_Menu(filename) 
                elseif digit_1 == "*" then
                    return
                end
            end


            if attempt >= max_attempts then
                return
            end
        until option == "4"  or option == "9" or option == "*"
    until option == "4"  or option == "9" or option == "*"

end


function fileExists(filePath)
    local file = io.open(filePath, "r")
    if file then
        io.close(file)
        return true
    else
        return false
    end
end

function greeting_menu_setting(vm_folder, config_file)
    global_greetings = mozart.getGreetings(called_number)
    freeswitch.consoleLog("warning","User:" .. called_number .. "has: " .. #global_greetings .. " greetings\n") 
    user_config_data = mozart.fetchUserConfig(called_number)
    mozart.fetchAndSaveUserConfig(called_number, user_config_folder)
    reloadxml();
    -- Assuming you already have user_data from the previous function
    local param_greeting = mozart.getConfigAttributeValue(user_config_data.params, "param", "voicemail_greeting_number")
    freeswitch.consoleLog("warning", "param-voicemail_greeting_number: " .. tostring(param_greeting) .. "\n")
    freeswitch.consoleLog("warning","User:" .. called_number .. "has: " .. #global_greetings .. " greetings:" .. tostring(global_greetings[1]) .. "\n") 

    local exit_menu = false

    while not exit_menu do
        session:sleep(500)
        freeswitch.consoleLog("warning","START greeting_menu_setting:" .. vm_folder .. "," .. tostring(config_file) .. "\n") 
        local choice = session:playAndGetDigits(1, 1, 3, 3000, "", "voicemail/ttsmaker-tui-setup-greeting_menu.wav", "", "[1-2\\*#]")

        if choice == "1" then
            -- local success = setDefaultGreeting(config_file)
            -- freeswitch.consoleLog("warning", "1 - set default greeting - global_greetings size:" .. tostring(#global_greetings) .. "\n")
            revertToDefaultGreeting(global_greetings,called_number)
            -- local is_custom_greeting = session:setVariable("voicemail_greeting_number","0")
            session:streamFile("voicemail/ttsmaker-tui-default-greeting-set.wav")
            session:sleep(500)
            -- Loop back to the menu
        elseif choice == "2" then
            custom_greeting_menu(vm_folder, config_file)
        elseif choice == "*" then
            mainMenu(0)
            exit_menu = true  -- Exit the loop if main menu is called
        elseif choice == "#" then
            session:hangup()
            exit_menu = true  -- Exit the loop if call is hung up
        else
            -- Invalid input, maybe play a prompt and loop back to the main menu
            freeswitch.consoleLog("debug", "greeting_menu_setting - invalid entry\n")
            session:streamFile("voicemail/90.wav")
            exit_menu = true
        end
        session:sleep(300)
    end
end

-- Main menu for recording and reviewing the greeting
function record_custom_greeting_menu(path, config_file)
    local max_attempts = 3
    local attempt = 0
    local option = 0
    local greeting_file_name = path .. "/greeting_1.wav" 

    repeat
        -- record_greeting_origin(greeting_file_name)
        -- record_greeting_recordFile(greeting_file_name)
        -- record_greeting(greeting_file_name)
        -- record_greeting_with_hash(greeting_file_name)
        -- record_greeting_to_stop_hash_star(greeting_file_name)
        -- record_greeting_on_dtmf_input(greeting_file_name)
        -- record_greeting_on_dtmf_input_and_record(greeting_file_name)
        -- record_greeting_deep_seek(greeting_file_name, 20)                
        -- recording_file("voicemail/ttsmaker-tui-record-greeting.wav",greeting_file_name) 
        -- recording_file_DS_1("voicemail/ttsmaker-tui-record-greeting.wav",greeting_file_name) 
        recording_file_1("voicemail/ttsmaker-tui-record-greeting.wav",greeting_file_name, false) 
        repeat
            attempt = attempt + 1
        freeswitch.consoleLog("warning","record_custom_greeting_menu - attempt:" .. attempt .. ", greeting-file:" .. greeting_file_name .. "\n")
            option = review_greeting(greeting_file_name)
            
            if option == "1" then
            -- set custom greeting
            local success = save_custom_greeting_setting_in_config(config_file)
            session:setVariable("voicemail_greeting_number", "1")
            reloadxml()
            session:streamFile("voicemail/ttsmaker-tui-custom-greeting-set.wav")
            session:sleep(700)
            freeswitch.consoleLog("warning","uploadGreeting:" .. called_number .. "," .. greeting_file_name .. "\n")
            mozart.deleteFirstGreeting(global_greetings, called_number)
            mozart.uploadGreeting(called_number, greeting_file_name)
            return
            elseif option == "2" then
                -- User chose to re-record the greeting
                attempt = 0 -- Reset attempts for re-recording
                freeswitch.consoleLog("debug","re-record greeting - attempt:" .. attempt .. "\n")
                break
            elseif option == "*" then
                break
            elseif option == "#" then
                session:hangup()
                break
            end

            if attempt >= max_attempts then
                -- -- Maximum attempts reached, save the greeting and exit
                -- session:sleep(300)
                -- session:streamFile("voicemail/ttsmaker-tui-greeting-saved.wav")
                -- session:sleep(500)
                return
            end
        until option == "1" or option == "2" or option == "*" or option == "#"
 
    until option == "1" or option == "*" or option == "#"
end

function record_voicemail_message_menu(path)
    local tries = 0
    local max_tries = 3

    local uuid = session:getVariable("uuid")
    local vm_file_name = path .. "/msg_" .. uuid .. "_recorded.wav" 

    freeswitch.consoleLog("debug","record_voicemail - attempt:" .. tries .. ", voicemail-message-file:" .. vm_file_name .. "\n")

    
    recording_file_DS("voicemail/609.wav",vm_file_name)           

    local prompts = {
                    "voicemail/1494.wav",   -- 1 "to replay press 1"
                    "voicemail/699.wav",    -- 2 " to continue recording press 2"
                    "voicemail/796.wav",    -- 5  "for delivery options press 5"
                    "voicemail/903.wav",    -- 7  "to delete press 7"
                    "voicemail/372.wav",    -- 9  "to send this message press 9"
                    "voicemail/744.wav",    -- * "to return to the previous menu press *"
                    }
    repeat 
        local digit = playInterruptible(prompts)
        if not digit then
            digit = session:playAndGetDigits(1, 1, 3, 3000, "", "", "", ".")
        end
        if digit == "" then
            tries = tries + 1
            if tries == 2 then 
                session:streamFile("voicemail/98.wav")
            elseif tries > 2 then 
                session:streamFile("voicemail/96.wav")
                session:sleep(300)
                session:hangup()
                return
            end
        else
            tries =0
            if digit == "1" then
                session:streamFile(vm_file_name)
            elseif digit == "2" then 
                recording_file_DS("voicemail/609.wav",vm_file_name)           
            elseif digit == "5" then 
            elseif digit == "7" then 
                deleteFile(vm_file_name)           
            elseif digit == "9" then 
            elseif digit == "*" then
                freeswitch.consoleLog("debug","* is pressed - return to the previous menu\n")
                return 0 
            else
                -- Invalid input, maybe play a prompt and loop back to the main menu
                freeswitch.consoleLog("debug","voiceMailSettings - invalid entry\n")   
                session:streamFile("voicemail/90.wav")
            end
        end
    until digit == "*"

end

function custom_greeting_menu(vm_folder, config_file)
    local done = false
    
    while not done do
        -- Play the instruction file and get the user's choice
        freeswitch.consoleLog("warning","START custom_greeting_menu:" .. vm_folder .. "," .. tostring(config_file) .. "\n") 
        local digit = session:playAndGetDigits(1, 1, 3, 5000, "#", "voicemail/ttsmaker-tui-custom-greeting_menu.wav", "", "[1-2\\*#]")
        local greeting_file = vm_folder .. "/greeting_1.wav"

        if digit == "1" then
            -- Option 1: Listen to current greeting
            -- if fileExists(greeting_file) then
            --     session:streamFile(greeting_file)
            -- else
            --     -- If the greeting file doesn't exist, play a default or an error message
            --     session:streamFile("voicemail/default-greeting-1.wav")
            -- end
            if not global_greetings or #global_greetings == 0 then
                -- If the greeting file doesn't exist, play a default or an error message
                session:streamFile("voicemail/default-greeting-1.wav")
            else
                -- Get the first greeting
                mozart.playCustomGreeting(global_greetings)
            end
        elseif digit == "2" then
            -- record custom greeting
            freeswitch.consoleLog("warning","CALLING record_custom_greeting_menu:" .. vm_folder .. ",".. tostring(config_file) .. "\n")
            record_custom_greeting_menu(vm_folder, config_file)
            freeswitch.consoleLog("warning","FINISHED record_custom_greeting_menu:" .. vm_folder .. ",".. tostring(config_file) .. "\n")
            done = true
        -- elseif digit == "3" then
        --     -- set custom greeting
        --     local success = save_custom_greeting_setting_in_config(config_file)
        --     session:setVariable("voicemail_greeting_number", "1")
        --     reloadxml()
        --     session:streamFile("voicemail/ttsmaker-tui-custom-greeting-set.wav")
        --     session:sleep(500)
        --     done = true
        elseif digit == "*" then
            -- Option *: Return to the previous menu
            greeting_menu_setting(vm_folder, config_file)
            done = true
        elseif digit == "#" then
            -- Option #: Hang up
            session:hangup()
            done = true
        else
            -- Invalid input, play an error message and repeat the menu
            session:streamFile("voicemail/90.wav")
            done = true
        end
        session:sleep(300)
    end
end

-- function default_greeting_menu(vm_folder, config_file)
--     local done = false
--     while not done do
--         -- Play the instruction file and get the user's choice
--         local digit = session:playAndGetDigits(1, 1, 3, 5000, "#", "voicemail/ttsmaker-tui-default-greeting_menu.wav", "", "[1-2\\*#]")

--         if digit == "1" then
--             -- listen to the default greeting
--             session:streamFile("voicemail/default-greeting-1.wav")
--             session:sleep(200)
--         elseif digit == "2" then
--             -- set default greeting
--             local success = setDefaultGreeting(config_file)
--             if success == true then
--                 -- local is_custom_greeting = session:getVariable("voicemail_greeting_number")
--                 -- if is_custom_greeting ~= nil  then
--                 --     freeswitch.consoleLog("debug", "voicemail_greeting_number:" .. is_custom_greeting .. "\n")
--                 --     session:unsetVariable("voicemail_greeting_number")
--                 -- end
--                 reloadxml()
--             end
--             session:streamFile("voicemail/ttsmaker-tui-default-greeting-set.wav")
--             session:sleep(200)
--         elseif digit == "*" then
--             -- Option *: Return to the previous menu
--             greeting_menu_setting(vm_folder, config_file)
--             done = true
--         elseif digit == "#" then
--             -- Option #: Hang up
--             session:hangup()
--             done = true
--         else
--             -- Invalid input, play an error message and repeat the menu
--             session:streamFile("voicemail/90.wav")
--             done = true
--         end
--         session:sleep(300)
--     end
-- end


-- Function to replace the pattern
function replacePattern(contents, patternToFind, replacementValue)
    return contents:gsub(patternToFind, replacementValue)
end

function changeTuiPin(filename, value)

    -- Check if the value is valid (4 to 8 digits)
    if not value:match("^%d%d%d%d%d?%d?%d?%d?$") then
        freeswitch.consoleLog("warning", "PIN must be between 4 and 8 digits.\n")
        return false
    end

    -- Read the contents of the file
    local file = io.open(filename, "r") -- Open the file for reading
    if not file then
        freeswitch.consoleLog("warning","Could not open the file: " .. filename .. "\n")
        return false
    end

    local contents = file:read("*a") -- Read the entire contents of the file
    file:close() -- Close the file after reading
    
    -- Define the pattern you're looking for and the replacement
    local patternToFind = '<param name="tui%-password"%s*value="(%d%d%d%d%d?%d?%d?%d?)"'
    local replacementValue = '<param name="tui-password" value="' .. value .. '"'    
    -- Replace the pattern in the contents
    contents = replacePattern(contents, patternToFind, replacementValue)

    local patternToFindVar = '<variable name="tui%-password"%s*value="(%d%d%d%d%d?%d?%d?%d?)"'
    local replacementValueVar = '<variable name="tui-password" value="' .. value .. '"'    
    -- Replace the pattern in the contents
    contents = replacePattern(contents, patternToFindVar, replacementValueVar)
    
    -- Write the updated contents back to the file
    file = io.open(filename, "w") -- Open the file for writing
    if not file then
        freeswitch.consoleLog("warning", "Could not open the file " .. filename .. "\n")
        return false
    end
    file:write(contents) -- Write the new contents to the file
    file:close() -- Close the file after writing
    freeswitch.consoleLog("warning", "New tui-pin:" .. value .. " is saved in file:" .. filename .. "\n")
    freeswitch.consoleLog("warning", "Contents:\n" .. contents .. "\n")
    mozart.updateConfigParameter(called_number,"tui_pwd", value)
    return true
end

function changeTuiPin_mozart(value)
    local returned_value = mozart.updateConfigParameter(called_number,"tui_pwd", value)
    -- user_config_data = mozart.fetchUserConfig(called_number)
    return returned_value
end

function pinSettingMenu(pin, config_file)
    verifyCurrentPin(pin)
    session:sleep(300)
    -- changePin(pin, config_file)
    changePin_no_config_file(pin)
end

function verifyPin(pin)

    local pinFromConfig = pin 
    local max_attempts = 3
    local digit_timeout = 5000 -- timeout in milliseconds (5 seconds)
    local pin_entered
    local attempt = 0

    repeat
        session:sleep(500)
        pin_entered = session:playAndGetDigits(4, 8, max_attempts, digit_timeout, "#", "voicemail/ttsmaker-tui-enter-pin.wav", "", "\\d+")
        attempt = attempt + 1
        freeswitch.consoleLog("debug","New PIN entered:" .. pin_entered .. ",old_pin:" .. pinFromConfig .. "\n")
        if pin_entered == pinFromConfig then
            -- Correct PIN entered, continue with another menu
            break
        else
            -- Incorrect PIN, inform the user
            session:streamFile("voicemail/ttsmaker-tui-invalid-pin.wav")
            if attempt == max_attempts then
                -- Maximum attempts reached, disconnect the call
                session:hangup()
            end
        end
    until attempt >= max_attempts
end

function verifyCurrentPin(pin)

    local pinFromConfig = pin 
    local max_attempts = 3
    local digit_timeout = 5000 -- timeout in milliseconds (5 seconds)
    local pin_entered
    local attempt = 0

    repeat
        pin_entered = session:playAndGetDigits(4, 8, max_attempts, digit_timeout, "#", "voicemail/ttsmaker-tui-enter-current-pin.wav", "", "\\d+")
        attempt = attempt + 1
        freeswitch.consoleLog("debug","New PIN entered:" .. pin_entered .. "old_pin:" .. pinFromConfig .. "\n")
        if pin_entered == pinFromConfig then
            -- Correct PIN entered, continue with another menu
            break
        else
            -- Incorrect PIN, inform the user
            session:streamFile("voicemail/ttsmaker-tui-invalid-pin.wav")
            if attempt == max_attempts then
                -- Maximum attempts reached, disconnect the call
                session:hangup()
            end
        end
    until attempt >= max_attempts
end

function changePin(pin, config_file)

    local pinFromConfig = pin 
    local max_attempts = 3
    local digit_timeout = 5000 -- timeout in milliseconds (5 seconds)
    local pin_entered
    local attempt = 0

    repeat
        pin_entered = session:playAndGetDigits(4, 8, max_attempts, digit_timeout, "#", "voicemail/ttsmaker-tui-enter-new-pin.wav", "", "\\d+")
        attempt = attempt + 1
        
        if pin_entered == pinFromConfig then
            -- Incorrect PIN, inform the user
            session:streamFile("voicemail/ttsmaker-tui-pin-is-same.wav")
            if attempt == max_attempts then
                -- Maximum attempts reached, disconnect the call
                session:hangup()
            end
        else
            -- Save new PIN
            freeswitch.consoleLog("warning","New PIN entered-1:" .. pin_entered .. ", old_pin:" .. pinFromConfig .. "\n")
            if changeTuiPin_mozart(pin_entered) == true then
                session:setVariable("tui-password", pin_entered)    
                -- reloadxml()
                session:streamFile("voicemail/ttsmaker-tui-new-pin-saved.wav")
                session:sleep(300)
                break
            else
                freeswitch.consoleLog("warning","New entered pin is not saved\n")
                session:streamFile("voicemail/ttsmaker-tui-pin-not-valid.wav")
                session:sleep(300)
            end 
        end
    until attempt >= max_attempts
end
function changePin_no_config_file(pin)

    local pinFromConfig = pin 
    local max_attempts = 3
    local digit_timeout = 5000 -- timeout in milliseconds (5 seconds)
    local pin_entered
    local attempt = 0

    local prompts = {
                    "voicemail/766.wav",  -- "to replay press 7"
                    "voicemail/768.wav",   -- "to transfer this message press 4"
                    "voicemail/765.wav",   -- "to delete press 3"
                    "voicemail/839.wav",    -- "to return this call press 6"
                    "voicemail/1499.wav",   -- "to save press 5"
                    "voicemail/744.wav",   -- "to return to the previous menu press *"
                    }

    repeat
        -- pin_entered = session:playAndGetDigits(4, 8, max_attempts, digit_timeout, "#", "voicemail/ttsmaker-tui-enter-new-pin.wav", "", "\\d+")
        pin_entered = session:playAndGetDigits(4, 8, max_attempts, digit_timeout, "#", "voicemail/optus-pin-setup.wav", "", "\\d+")
        attempt = attempt + 1
        
        if pin_entered == pinFromConfig then
            -- Incorrect PIN, inform the user
            session:streamFile("voicemail/ttsmaker-tui-pin-is-same.wav")
            if attempt == max_attempts then
                -- Maximum attempts reached, disconnect the call
                session:hangup()
            end
        else
            -- Save new PIN
            freeswitch.consoleLog("warning","New PIN entered-2:" .. pin_entered .. ", old_pin:" .. tostring(pinFromConfig) .. "\n")
            if changeTuiPin_mozart(pin_entered) == true then
                session:setVariable("tui-password", pin_entered)    
                -- reloadxml()
                session:streamFile("voicemail/ttsmaker-tui-new-pin-saved.wav")
                session:sleep(300)
                break
            else
                freeswitch.consoleLog("warning","New entered pin is not saved\n")
                session:streamFile("voicemail/ttsmaker-tui-pin-not-valid.wav")
                session:sleep(300)
            end 
        end
    until attempt >= max_attempts
end

function changePin_userOptions(pin)

    local pinFromConfig = pin 
    local max_attempts = 3
    local digit_timeout = 2000 -- timeout in milliseconds (5 seconds)
    local pin_entered
    local attempt = 0

    repeat
        if attempt == 0 then
            session:streamFile("voicemail/651.wav")
        else 
            session:streamFile("voicemail/ttsmaker-tui-enter-new-pin.wav")
            session:streamFile("voicemail/3898.wav")
        end
        -- pin_entered = session:playAndGetDigits(4, 8, max_attempts, digit_timeout, "#", "voicemail/ttsmaker-tui-enter-new-pin.wav", "", "\\d+")
        pin_entered = session:playAndGetDigits(4, 10, max_attempts, digit_timeout, "#", "", "", "\\d+")
        attempt = attempt + 1   
        if pin_entered ~= "" then  
            -- Save new PIN
            -- freeswitch.consoleLog("warning","New PIN entered-2:" .. pin_entered .. ", old_pin:" .. tostring(pinFromConfig) .. "\n")
            if changeTuiPin_mozart(pin_entered) == true then
                session:streamFile("voicemail/119.wav")
                -- Split PIN into individual characters and pronounce each digit
                for digit in pin_entered:gmatch(".") do
                    if digit:match("%d") then
                        session:execute("say", "en number pronounced " .. digit)  -- Says "four", "three", etc.
                        session:sleep(200)  -- Optional: Add slight pause between digits (ms)
                    end
                end
                session:streamFile("voicemail/120.wav")
                break
            else
                freeswitch.consoleLog("warning","New entered pin is not saved\n")
                session:streamFile("voicemail/98.wav")
                session:sleep(300)
            end 
        end
    until attempt >= max_attempts
end

function changePlaybackPreferences_Menu()
    local tries = 0
    local max_tries = 3
    local prompts = {
                    "voicemail/774.wav",    -- 1 "change the order your messages are played in press 1"
                    "voicemail/1054.wav",   -- 3 " to stop messages being played with time stamps press 3"
                    "voicemail/1060.wav",   -- 3  "to enable your messages being played with time stamps press 3"
                    "voicemail/744.wav",    -- * "to return to the previous menu press *"
                    }
    repeat 
        local digit = playInterruptible(prompts)
        if not digit then
            digit = session:playAndGetDigits(1, 1, 3, 3000, "", "", "", ".")
        end
        if digit == "" then
            tries = tries + 1
            if tries == 2 then 
                session:streamFile("voicemail/98.wav")
            elseif tries > 2 then 
                session:streamFile("voicemail/96.wav")
                session:sleep(300)
                session:hangup()
                return
            end
        else
            tries =0
            if digit == "1" then
                -- digit = changeOrderMsgsPlayed_Menu()
            elseif digit == "3" then 
                -- digit = togglePlayingTimestamp_Menu()
            elseif digit == "*" then
                freeswitch.consoleLog("debug","* is pressed - return to the previous menu\n")
                return 0 
            else
                -- Invalid input, maybe play a prompt and loop back to the main menu
                freeswitch.consoleLog("debug","voiceMailSettings - invalid entry\n")   
                session:streamFile("voicemail/90.wav")
            end
        end
    until digit == "2" or digit == "4" or digit == "*"
 end

-- Helper function to play a message and get user input
function play_main_menu_with_messages(session)
    local timeout = 5000 -- milliseconds
    local valid_digits = "74365*"
    local prompt = "voicemail/optus-main-menu-with-messages.wav"

    local digit = session:playAndGetDigits(1, 1, 3, timeout, "#", prompt, "", valid_digits)

    return digit
end

function play_message_received_time(full_path)
    -- Extract timestamp if available (optional logic)
    local attr = assert(io.popen("stat -c %y '" .. full_path .. "'"))
    local timestamp = attr:read("*l")
    attr:close()

    -- Extract just the date and time portion (no fractional seconds or timezone)
    local date_str = timestamp:match("^(%d+%-%d+%-%d+ %d+:%d+:%d+)")
    freeswitch.consoleLog("info","Message received at:" .. date_str .. "\n") 
    -- Message received at:2025-05-01 03:03:40
    local year, month, day, hour, min, sec = date_str:match("(%d+)%-(%d+)%-(%d+) (%d+):(%d+):(%d+)")
    local msg_time = os.time({
        year = tonumber(year),
        month = tonumber(month),
        day = tonumber(day),
        hour = tonumber(hour),
        min = tonumber(min),
        sec = tonumber(sec)
    })
    session:execute("say", "en short_date_time pronounced " .. tostring(msg_time))    
end
function playVoicemailTime(timestamp)
    -- Remove 'T' and 'Z' from the timestamp
    local clean_timestamp = timestamp:gsub("T", " "):gsub("Z", "")

    -- Parse the date and time
    local year, month, day, hour, min, sec = clean_timestamp:match("(%d+)-(%d+)-(%d+) (%d+):(%d+):(%d+)")

    -- Convert to a table
    local time_table = {
        year = tonumber(year),
        month = tonumber(month),
        day = tonumber(day),
        hour = tonumber(hour),
        min = tonumber(min),
        sec = tonumber(sec),
        isdst = false
    }

    -- Convert to epoch time
    local epoch_time = os.time(time_table)

    -- Play the formatted time
    local formatted_time = os.date("%I:%M %p on %A, %B %d, %Y", epoch_time)
    freeswitch.consoleLog("INFO", "Playing voicemail received time: " .. formatted_time .. "\n")

    -- Execute the say command
    session:execute("say", "en short_date_time pronounced " .. epoch_time)
end
function playVoicemails(voicemails, new_message)

    for index, voicemail in ipairs(voicemails) do
        -- local full_path = user_vm_folder .. "/" .. msg_file
        -- freeswitch.consoleLog("info","New message[" .. i .. "] path: " .. full_path .. "\n")

       freeswitch.consoleLog("warning", "playVoicemails - new_message:" .. tostring(new_message) .. ", index:" .. index .. ",#voicemails:" .. #voicemails .. "\n")

        -- Play prompt: "New message received [timestamp]"
        if new_message then
            session:streamFile("voicemail/124.wav")     -- "new message"
        else 
            session:streamFile("voicemail/1476.wav")    -- "saved message"
        end
        -- Play date-time extracted from hte message file
        -- play_message_received_time(full_path)
        playVoicemailTime(voicemail.createdAt)      
        -- Play message
        session:streamFile(voicemail.url)
        -- Menu interaction
        local number_of_retries = 0
        local prompts = {
            "voicemail/766.wav",  -- "to replay press 7"
            "voicemail/768.wav",   -- "to transfer this message press 4"
            "voicemail/765.wav",   -- "to delete press 3"
            "voicemail/839.wav",    -- "to return this call press 6"
            "voicemail/1499.wav",   -- "to save press 5"
            "voicemail/744.wav",   -- "to return to the previous menu press *"
        }
        while session:ready() do
            -- session:sleep(500)
            if (number_of_retries >= 2) then
                session:streamFile("voicemail/98.wav")
            end

            -- interruptible playback
            local digit = playInterruptible(prompts)
            -- Accept all digits and then handle an invalid entry
            -- local digit = session:playAndGetDigits(1, 1, 3, 5000, "", "", "voicemail/98.wav", "\\d+|\\*") 
            if not digit then
                digit = session:playAndGetDigits(1, 1, 3, 2000, "", "", "", "\\d+|\\*") 
            end
            number_of_retries = number_of_retries + 1
            freeswitch.consoleLog("info","User pressed: " .. tostring(digit) .. "\n")
            if digit ~= "" then
                number_of_retries = 0
            end
            if digit == "" then
                if number_of_retries >= 3 then 
                    -- session:streamFile("voicemail/optus-thankyou-goodbye.wav")
                    session:streamFile("voicemail/96.wav")
                    session:sleep(300)
                    session:hangup()
                    return
                end
            elseif digit == "7" then -- Replay
                session:streamFile(voicemail.url)
            elseif digit == "4" then -- Transfer
                freeswitch.consoleLog("warning", "transfer message test - url:" .. voicemail.url .. ",storageKey:" .. tostring(voicemail.storageKey) .. "\n")
                transferMessage_Menu(voicemail)
                -- Add transfer logic here
            elseif digit == "3" then -- Delete
                -- Delete voicemail
                if mozart.deleteVoicemail(voicemail.toId, voicemail.id) then
                    freeswitch.consoleLog("INFO", "Voicemail deleted successfully.\n")
                else
                    freeswitch.consoleLog("ERR", "Failed to delete voicemail.\n")
                end
                -- table.remove(voicemails, index)
                session:streamFile("voicemail/vm-deleted.wav")
                freeswitch.consoleLog("warning","VM deleted" .. "\n") 
                session:sleep(500)
                break
            elseif digit == "6" then -- Return call
                session:streamFile("voicemail/optus-returning-the-call.wav")
                returnCall(voicemail.fromId)
                break
            elseif digit == "5" then -- Save
                -- Update voicemail (mark as saved )
                if mozart.updateVoicemail(voicemail.toId, voicemail.id, true, true) then
                    freeswitch.consoleLog("INFO", "Voicemail marked as saved successfully.\n")
                else
                    freeswitch.consoleLog("ERR", "Failed to update voicemail.\n")
                end
                session:streamFile("voicemail/vm-saved.wav")
                session:streamFile("voicemail/124.wav")     -- "new message"
                playVoicemailTime(voicemail.createdAt)
                -- Play message
                session:streamFile(voicemail.url)
                -- renameFile(full_path, "_saved")
                freeswitch.consoleLog("warning","VM saved" .. "\n") 
                session:sleep(300)
            elseif digit == "*" then
                session:speak("Returning to the main menu.")
                break
            else
                session:streamFile("voicemail/optus-that-number-is-not-valid.wav")  -- "invalid option"
                session:sleep(300)
            end
        end
    end
end

function mainMenu_with_new_and_saved_messages(new_voicemails, saved_voicemails)
        local count = #new_voicemails
        local count_saved = #saved_voicemails
        session:streamFile("voicemail/1581.wav")        -- "you have unplayed messages in your mail box"
        session:streamFile("voicemail/598.wav")         -- "you have"
        if count == 1 then
            session:streamFile("voicemail/4001.wav")    -- "one new message"
        else
            session:execute("say", "en number pronounced " .. count)
            session:streamFile("voicemail/1481.wav")    -- "new voice messages"
        end
        session:streamFile("voicemail/and.wav")
        session:execute("say", "en number pronounced " .. count_saved)
        if count_saved == 1 then 
            session:streamFile("voicemail/1476.wav")
        else
            session:streamFile("voicemail/1477.wav")
        end

        playVoicemails(new_voicemails, true)
        playVoicemails(saved_voicemails, false)

        freeswitch.consoleLog("info","mainMenu_with_new_and_saved_messages - EXIT\n")
end

function mainMenu_with_messages(voicemails, new_messages)
    local max_tries = 3
    local tries = 0

    local prompts = {
        "voicemail/752.wav",    -- "to play your messages press 7"
        "voicemail/754.wav",    -- "to record a message press 6"
        "voicemail/753.wav",    -- "to change your greeting press 3"
        "voicemail/756.wav",    -- "to access your user options press 8"
    }
    while session:ready() and tries < max_tries do
        session:sleep(500)

        -- interruptible playback
        local digit = playInterruptible(prompts)

        -- Prompt for user input with no prompt (we already played it)
        if not digit then
            digit = session:playAndGetDigits(
                1, 1, 1,    -- min_digits, max_digits, tries (only 1 try here)
                2000,       -- timeout
                "",         -- no prompt
                "",         -- invalid prompt
                "",         -- failure prompt (we'll handle this manually)
                "."
            )
        end
        if digit == "" then 
            tries = tries + 1
            if tries >= 2 then
                -- No input: play reminder and increment tries
                session:streamFile("voicemail/98.wav")
            end
        else
            tries = 0
            -- Input received: handle and exit loop
            freeswitch.consoleLog("info", "User pressed: " .. digit .. "\n")

            if digit == "7" then
                playVoicemails(voicemails, new_messages)
            elseif digit == "6" then
                record_voicemail_message_menu(user_vm_folder)
            elseif digit == "3" then
                greeting_menu_setting(user_vm_folder, user_config_file)
            elseif digit == "8" then
                freeswitch.consoleLog("debug","mainMenu_with_messages (" .. tostring(new_messages) .. ") - 8 is pressed\n") 
                digit = userOptions_Menu(user_vm_folder,user_config_file, tui_pin)
                freeswitch.consoleLog("debug","mainMenu_with_messages - userOptions_Menu - return:" .. tostring(digit) .. "\n") 
            else
                session:streamFile("voicemail/90.wav")
                session:sleep(300)
            end
            -- return -- exit after valid input
        end
    end

    -- Max tries reached with no input
    session:streamFile("voicemail/96.wav")  -- "thank you for calling, goodbye"
    session:sleep(300)
    session:hangup()
end

function mainMenu_with_saved_messages(voicemails)
    local count = #voicemails
    session:streamFile("voicemail/1475.wav")        -- "you have no new messages"
    session:streamFile("voicemail/and.wav")
    session:execute("say", "en number pronounced " .. count)
    if count == 1 then 
        session:streamFile("voicemail/1476.wav")    -- "saved message"
    else
        session:streamFile("voicemail/1477.wav")    -- "saved messages"
    end
    -- Menu interaction
    mainMenu_with_messages(voicemails, false)

    freeswitch.consoleLog("info","mainMenu_with_saved_messages - EXIT\n")
end

function mainMenu_with_new_messages(voicemails)
    local count = #voicemails
    session:streamFile("voicemail/1581.wav")
    session:streamFile("voicemail/598.wav")
    if count == 1 then
        session:streamFile("voicemail/4001.wav") -- "one new message"
    else
        session:execute("say", "en number pronounced " .. count)
        session:streamFile("voicemail/1481.wav") -- "new voice messages"
    end

    freeswitch.consoleLog("warning","mainMenu_with_new_messages:" .. #voicemails .. "\n") 
    playVoicemails(voicemails, true)
    -- Menu interaction
    mainMenu_with_messages(voicemails, true)
    freeswitch.consoleLog("info","mainMenu_with_new_messages - EXIT\n")
end

function mainMenu_no_messages()
    session:streamFile("voicemail/1475.wav")
    local max_tries = 3
    local tries = 0
    local prompts = {
        "voicemail/754.wav",    -- "to record a message press 6"
        "voicemail/753.wav",    -- "to change your greeting press 3"
        "voicemail/756.wav",    -- "to access your user options press 8"
    }

    while session:ready() and tries < max_tries do
        session:sleep(500)
    --   local result = session:playAndGetDigits(1, 1, 3, 5000, "", "voicemail/ttsmaker-tui-main_menu.wav", "", "[1-3#]")
     
        -- interruptible playback
        local digit = playInterruptible(prompts)
        if not digit then
            digit = session:playAndGetDigits(1, 1, 3, 3000, "", "", "", "\\d+|\\#")
        end
        freeswitch.consoleLog("debug","mainMenu - noMsgs - pressed:" .. digit .. "\n") 
        if digit == "" then
            -- Timeout occurred, hang up the session
            -- freeswitch.consoleLog("debug","mainMenu - timeout - hangup()\n")   
            session:streamFile("voicemail/98.wav")
            tries = tries + 1
        else
            if digit == "6" then 
                record_voicemail_message_menu(user_vm_folder)
            elseif digit == "3" then
                freeswitch.consoleLog("warning","CALLING greeting_menu_setting:" .. user_vm_folder .. "," .. tostring(user_config_file) .. "\n") 
                greeting_menu_setting(user_vm_folder,user_config_file)
            elseif digit == "8" then
                freeswitch.consoleLog("debug","mainMenu_no_messages - 8 is pressed\n") 
                voiceMailSettings(user_vm_folder,user_config_file, tui_pin)
            else
        --      -- Invalid input, maybe play a prompt and loop back to the main menu
        --      -- freeswitch.consoleLog("debug","mainMenu - invalid entry\n")   
                session:streamFile("voicemail/ttsmaker-tui-invalid-entry.wav")
            end
            return
        end
    end
    -- Max tries reached with no input
    session:streamFile("voicemail/96.wav")
    session:sleep(300)
    session:hangup()
end

function mainMenu(verify_pin)
   called_number = argv[1]

   freeswitch.consoleLog("debug","mainMenu - argv[1] - " .. called_number .. "\n")   
   storage_dir = session:getVariable('storage_dir')
   user_config_folder = session:getVariable('conf_dir')
   freeswitch.consoleLog("debug","storage_dir:" .. storage_dir .. "conf_dir:" .. user_config_folder .. "\n")   
   user_vm_folder = storage_dir .. "/voicemail/hosted/*************/" .. called_number
   freeswitch.consoleLog("debug","voicemails-folder:" .. user_vm_folder .. "\n")   

   local user_config_file =  user_config_folder .. "/directory/vmusers/" .. called_number .. ".xml"
   freeswitch.consoleLog("debug", "user_config_file: " .. tostring(user_config_file) .. "\n")

    user_config_data = mozart.fetchUserConfig(called_number)
    -- mozart.fetchAndSaveUserConfig(called_number, user_config_folder)
    -- reloadxml();
    -- Assuming you already have user_data from the previous function
    local tui_subscriber = mozart.getConfigAttributeValue(user_config_data.params, "param", "tui_subscriber")
    local user_custom_greeting = mozart.getConfigAttributeValue(user_config_data.params, "param", "voicemail_greeting_number")
    session:setVariable("tui_subscriber",tui_subscriber)

   --    my_globalvar = freeswitch.getGlobalVariable("varname")
    local tui_pin = mozart.getConfigAttributeValue(user_config_data.params, "param", "tui-password")

   if (tui_pin == nil or tui_pin == "") then
    --   addDefaultTuiPin(user_config_file)
    --   reloadxml()
    --   tui_pin = "1234"
    --   session:setVariable("tui-password", tui_pin)
    -- changePin(tui_pin, user_config_file) 
    changePin_no_config_file(pin)
   elseif (verify_pin == 1) then
      verifyPin(tui_pin)
   end

--    new_messages = getNewMessages(user_vm_folder)
--    for i, file in ipairs(new_messages) do
--       -- freeswitch.consoleLog("debug","new voicemails:" .. file .. "\n")   
--    end
--    saved_messages = getSavedMessages(user_vm_folder)
--    recorded_messages = getRecordedMessages(user_vm_folder)
    while session:ready() do
    local voicemails_json = mozart.getVoicemails(called_number)
    local new_voicemails, saved_voicemails = sortVoicemails(voicemails_json.data.voicemails)

    freeswitch.consoleLog("warning","User has:" .. #new_voicemails .. " new_message(s)," .. #saved_voicemails .. " saved_message(s)\n") 
--    for i, file in ipairs(saved_messages) do
--       -- freeswitch.consoleLog("debug","voicemails-saved:" .. file .. "\n")   
--    end
   -- files_order = getFilesInDirectoryOrderedByTimeAscending(user_vm_folder)
   -- for i, file in ipairs(files_order) do
   --     freeswitch.consoleLog("warning","voicemail(ascending):" .. file .. "\n")   
   -- end
   -- files_order_1 = getFilesInDirectoryOrderedByTimeDescending(user_vm_folder)
   -- for i, file in ipairs(files_order_1) do
   --     freeswitch.consoleLog("warning","voicemail(descending):" .. file .. "\n")   
   -- end
    if ((#new_voicemails == 0) and (#saved_voicemails == 0)) then 
        mainMenu_no_messages()
    elseif (#new_voicemails > 0 and #saved_voicemails > 0) then
        mainMenu_with_new_and_saved_messages(new_voicemails, saved_voicemails)
    elseif (#saved_voicemails > 0) then
        mainMenu_with_saved_messages(saved_voicemails)
    elseif (#new_voicemails > 0 and #saved_voicemails == 0) then
        mainMenu_with_new_messages(new_voicemails)
    else
    end
    end
    global_greetings = nil
end

function listenNewMessages(index)
    if index > #new_messages then
        session:streamFile("voicemail/vm-no_more_messages.wav")
        return mainMenu(0)
    end
    local file = user_vm_folder .. "/" .. new_messages[index]
    session:streamFile(file)
    session:sleep(500)
    local choice = session:playAndGetDigits(1, 1, 3, 5000, "", "voicemail/ttsmaker-tui-new-vm_menu.wav", "", "[1-3\\*#]")

    -- session:consoleLog("debug", "listenNewMessages Got DTMF CHOICE: ".. choice .."\n")
    if choice == "1" then
        -- save voicemail; we will rename file to: *_saved.wav
        renameFile(file, "_saved")   
        table.remove(new_messages, index)     
        session:sleep(200)
        session:streamFile("voicemail/vm-saved.wav")
        listenNewMessages(index + 1)
        freeswitch.consoleLog("debug","VM saved" .. "\n") 
        session:sleep(200)
    elseif choice == "2" then
        -- delete vm
        deleteFile(file)
        table.remove(new_messages, index)
        -- session:sleep(300)
        session:streamFile("voicemail/vm-deleted.wav")
        freeswitch.consoleLog("warning","VM deleted" .. "\n") 
        session:sleep(500)
        listenNewMessages(index)
    elseif choice == "3" then
        listenNewMessages(index + 1)
        session:sleep(200)
    elseif choice == "*" then
        mainMenu(0)
    elseif choice == "#" then
        session:hangup()
    else
            -- Invalid input, maybe play a prompt and loop back to the main menu
            -- freeswitch.consoleLog("debug","listenNewMessages - invalid entry\n")   
            session:streamFile("voicemail/90.wav")
    end
end

function listenSavedMessages(index)
    if index > #saved_messages then
        session:streamFile("voicemail/vm-no_more_messages.wav")
        return mainMenu(0)
    end
    local file = user_vm_folder .. "/" .. saved_messages[index]
    session:streamFile(file)
    session:sleep(500)
    local choice = session:playAndGetDigits(1, 1, 3, 5000, "", "voicemail/ttsmaker-tui-saved-vm_menu.wav", "", "[1-2\\*#]")
    -- session:consoleLog("debug", "listenSavedMessages Got DTMF CHOICE: ".. choice .."\n")
    if choice == "1" then
        -- delete vm
        deleteFile(file)
        table.remove(saved_messages, index)
        -- session:sleep(300)
        session:streamFile("voicemail/vm-deleted.wav")
        freeswitch.consoleLog("warning","VM deleted" .. "\n") 
        session:sleep(500)
        listenSavedMessages(index)
    elseif choice == "2" then 
        -- skip to next message
        listenSavedMessages(index + 1)
        session:sleep(200)
    elseif choice == "*" then
        mainMenu(0)
    elseif choice == "#" then
        session:hangup()
    else
        -- Invalid input, maybe play a prompt and loop back to the main menu
        freeswitch.consoleLog("debug","listenSavedMessages - invalid entry\n")   
        session:streamFile("voicemail/90.wav")
    end
end

function voiceMailSettings(vm_folder,config_file, pin)

    session:sleep(500)
    local choice = session:playAndGetDigits(1, 1, 3, 5000, "", "voicemail/ttsmaker-tui-vm-settings_menu.wav", "", "[1-2\\*#]")

    -- session:consoleLog("warning", "VM settings: ".. choice .."\n")
    if choice == "1" then
        -- change pin
        -- freeswitch.consoleLog("debug","1 is pressed - pinSettingMenu - pin:" .. pin .. "config:" .. tostring(config_file) .. "\n")   
        pinSettingMenu(pin, config_file)
        session:sleep(200)
    elseif choice == "2" then 
        -- setup greeting
        freeswitch.consoleLog("debug","2 is pressed - greeting_menu_setting\n")   
        greeting_menu_setting(vm_folder, config_file)
        session:sleep(200)
        mainMenu(0)
    elseif choice == "*" then
        freeswitch.consoleLog("debug","* is pressed - mainMenu\n")   
        mainMenu(0)
    elseif choice == "#" then
        freeswitch.consoleLog("debug","# is pressed - hangup\n")   
        session:hangup()
    else
        -- Invalid input, maybe play a prompt and loop back to the main menu
        freeswitch.consoleLog("debug","voiceMailSettings - invalid entry\n")   
        session:streamFile("voicemail/90.wav")
    end
    session:sleep(300)
end
function manageGroupList_Menu(vm_folder,config_file, pin)
    local tries = 0
    local max_tries = 3
    local prompts = {
        "voicemail/371.wav",    -- xx#  "Please enter a 1 or two digit group list number"
        "voicemail/3898.wav",   -- xx#  "followed by #"
        "voicemail/917.wav",    -- name#"record the group name and press #"
        "voicemail/3893.wav",   -- #    "to skip this step press #"
        "voicemail/522.wav",    -- 1    "listen to groups list summary (at least one list exists)"
        "voicemail/523.wav",    -- 2    "create one (not reached max lists)"
        "voicemail/524.wav",    -- 3    "delete one (at least one list exists)"
        "voicemail/916.wav",    -- 4    "modify (at least one list exists)""
    }
    repeat 
        local digit = playInterruptible(prompts)
        if not digit then
            digit = session:playAndGetDigits(1, 3, 3, 2000, "#", "", "", ".")
        end
        if digit == "" then
            tries = tries + 1
            if tries == 2 then 
                session:streamFile("voicemail/98.wav")
            elseif tries > 2 then 
                session:streamFile("voicemail/96.wav")
                session:sleep(300)
                session:hangup()
                return
            end
        else
            tries =0
            -- session:consoleLog("warning", "VM settings: ".. digit .."\n")
            if digit == "2" then
                -- change pin
                freeswitch.consoleLog("debug","1 is pressed - pinSettingMenu - pin:" .. pin .. "config:" .. tostring(config_file) .. "\n")   
                pinSettingMenu(pin, config_file)
                session:sleep(200)
            elseif digit == "4" then 
                -- setup greeting
                freeswitch.consoleLog("debug","2 is pressed - greeting_menu_setting\n")   
                modifyPersonalPreferences_Menu(vm_folder, config_file)
                session:sleep(200)
                -- mainMenu(0)
            elseif digit == "*" then
                freeswitch.consoleLog("debug","* is pressed - return to the previous menu\n")
                return   
            else
                -- Invalid input, maybe play a prompt and loop back to the main menu
                freeswitch.consoleLog("debug","voiceMailSettings - invalid entry\n")   
                session:streamFile("voicemail/90.wav")
            end
        end
    until digit == "2" or digit == "4" or digit == "*"
end
function modifyPersonalPreferences_Menu(vm_folder,config_file, pin)
    local tries = 0
    local max_tries = 3
    local prompts = {
        "voicemail/639.wav",    -- 1 "change your pin"
        "voicemail/1182.wav",   -- 2 "change playback preferences"
        "voicemail/1185.wav",   -- 3 "access your recorded name "
        "voicemail/744.wav",  -- * "to return to the previous menu press *""
    }
    repeat 
        local digit = playInterruptible(prompts)
        if not digit then
            digit = session:playAndGetDigits(1, 1, 3, 2000, "", "", "", ".")
        end
        if digit == "" then
            tries = tries + 1
            if tries == 2 then 
                session:streamFile("voicemail/98.wav")
            elseif tries > 2 then 
                session:streamFile("voicemail/96.wav")
                session:sleep(300)
                session:hangup()
                return
            end
        else
            tries =0
            -- session:consoleLog("warning", "VM settings: ".. digit .."\n")
            if digit == "1" then
                -- change pin
                -- freeswitch.consoleLog("debug","1 is pressed - pinSettingMenu - pin:" .. pin .. "config:" .. tostring(config_file) .. "\n")   
                -- pinSettingMenu(pin, config_file)
                changePin_userOptions(pin)
                session:sleep(200)
            elseif digit == "2" then 
                changePlaybackPreferences_Menu()
            elseif digit == "3" then 
            elseif digit == "*" then
                freeswitch.consoleLog("debug","* is pressed - return to the previous menu\n")
                return 0
            else
                -- Invalid input, maybe play a prompt and loop back to the main menu
                freeswitch.consoleLog("debug","voiceMailSettings - invalid entry\n")   
                session:streamFile("voicemail/90.wav")
            end
        end
    until digit == "2" or digit == "4" or digit == "*"
end

function userOptions_Menu(vm_folder,config_file, pin)
    local tries = 0
    local max_tries = 3
    local prompts = {
        "voicemail/755.wav",  -- 2 "manage group lists"
        "voicemail/573.wav",  -- 4 "modify personal preferences"
        "voicemail/744.wav",  -- * "to return to the previous menu press *""
    }
    repeat 
        local digit = playInterruptible(prompts)
        if not digit then
            digit = session:playAndGetDigits(1, 1, 3, 3000, "", "", "", ".")
        end
        if digit == "" then
            tries = tries + 1
            if tries == 2 then 
                session:streamFile("voicemail/98.wav")
            elseif tries > 2 then 
                session:streamFile("voicemail/96.wav")
                session:sleep(300)
                session:hangup()
                return
            end
        else
            tries =0
            if digit == "2" then
                digit = manageGroupList_Menu(vm_folder,config_file, pin)
            elseif digit == "4" then 
                digit = modifyPersonalPreferences_Menu(vm_folder, config_file, pin)
            elseif digit == "*" then
                freeswitch.consoleLog("debug","* is pressed - return to the previous menu\n")
                return 0 
            else
                -- Invalid input, maybe play a prompt and loop back to the main menu
                freeswitch.consoleLog("debug","voiceMailSettings - invalid entry\n")   
                session:streamFile("voicemail/90.wav")
            end
        end
    until digit == "2" or digit == "4" or digit == "*"
end

mainMenu(1)

-- -- Rename a file
-- local old_name = "/path/to/old_filename.wav"
-- local new_name = "/path/to/new_filename.wav"
-- local status, err = os.rename(old_name, new_name)
-- if not status then
--     print("Error renaming file: " .. err)
-- end

-- -- Delete a file
-- local file_to_delete = "/path/to/filename_to_delete.wav"
-- local status, err = os.remove(file_to_delete)
-- if not status then
--     print("Error deleting file: " .. err)
-- end

-- -- Move a file to another folder
-- local current_path = "/path/to/current/filename.wav"
-- local new_folder = "/path/to/new_directory/"
-- local status, err = os.rename(current_path, new_folder .. "filename.wav")
-- if not status then
--     print("Error moving file: " .. err)
-- end


