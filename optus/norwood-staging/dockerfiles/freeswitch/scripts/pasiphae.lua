-- The following is specifc to the Flowroute
-- documented at https://developer.flowroute.com/docs/messaging
url = "https://pasiphae.norwoodsystems.com/messages/voicemail"
 
action = event:getHeader("VM-Action")
if action ~= "leave-message" then return end
user = event:getHeader("VM-User")
domain = event:getHeader("VM-Domain")
caller_id_name = event:getHeader("VM-Caller-ID-Name")
caller_id_number = event:getHeader("VM-Caller-ID-Number")


file_path = event:getHeader("VM-File-Path")
flags = event:getHeader("VM-Flags")
folder = event:getHeader("VM-Folder")
uuid = event:getHeader("VM-UUID")

message_len = event:getHeader("VM-Message-Len")
timestamp = event:getHeader("VM-Timestamp")
api = freeswitch.API();
from = api:execute("user_data", user .. "@" .. domain ..  " var outbound_caller_id_number")
to = api:execute("user_data", user .. "@" .. domain .. " param vm-tmsi")
if to == "" then return end
 
if caller_id_name == ''
        or caller_id_name == 'UNKNOWN'
        or caller_id_name == 'UNASSIGNED'
        or caller_id_name == 'WIRELESS CALLER'
        or caller_id_name == 'TOLL FREE CALL'
        or caller_id_name == 'Anonymous'
        or caller_id_name == 'Unavailable'
        then caller_id_name = nil end
if caller_id_number == ''
        then caller_id_number = nil end
message = "Voicemail "
if caller_id_name
        then message = message .. "from " .. caller_id_name .. " (" ..  caller_id_number .. ")"
        elseif caller_id_number
        then message = message .. "from " .. caller_id_number end
message = message .. " at " .. os.date("%a %H:%M", timestamp)
message = message .. " length " .. message_len .. " seconds"
message = message .. " to box " .. user .."\n"
message = message .. " File path " .. file_path .. "\n"
message = message .. " Folder " .. folder .. " UUID " .. uuid\n"
message = message .. "."
-- Add JSON string escapes to the message
message = string.gsub(message, "([\\\"'])", "\\%1")
-- Send the text
data = '{ "to": "' .. to .. '", "from": "' .. from .. '", "body": "' .. message .. '"}'
api:execute("curl", url .. " content-type application/json post '" .. data .. "'")

 
