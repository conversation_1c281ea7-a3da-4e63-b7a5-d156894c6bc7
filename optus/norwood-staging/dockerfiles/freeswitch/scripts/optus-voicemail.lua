package.path = package.path .. ";/usr/local/freeswitch/scripts/?.lua"
local mozart = require("mozart")
JSON = assert(loadfile "/usr/local/freeswitch/scripts/json.lua")() -- one-time load of the routines
JSON.strictTypes = true
function encodeparams(paramsT)
    function escape (s)
      s = string.gsub(
            s, 
            '([\r\n"#%%&+:;<=>?@^`{|}%\\%[%]%(%)$!~,/\'])', 
            function (c)
                    return '%'..string.format("%X", string.byte(c))
            end
    )
      s = string.gsub(s, "%s", "+")
      return s
    end

    function encode (t)
      local s = ""
      for k , v in pairs(t) do
            s = s .. "&" .. escape(k) .. "=" .. escape(v)
      end
      return string.sub(s, 2)     -- remove first `&'
    end
    
    if type(paramsT) == 'table' then
            return encode(paramsT)
    else
            local tmp = Utils:commaText(paramsT, '&'); 
            local myParamsT = {};
            for k, v in pairs(tmp) do
                    local pos = 0
                    pos = string.find(v, '=')
                    if not pos then return '' end
                    myParamsT[string.sub(v, 1, pos-1 )] = string.sub(v, pos+1 )
            end
            return encode(myParamsT)
    end
end

local char_to_hex = function(c)
    return string.format("%%%02X", string.byte(c))
  end
  
  local function urlencode(url)
    if url == nil then
      return
    end
    url = url:gsub("\n", "\r\n")
    url = url:gsub("([^%w ])", char_to_hex)
    url = url:gsub(" ", "+")
    return url
  end
  
  local hex_to_char = function(x)
    return string.char(tonumber(x, 16))
  end

--   function serializeXml(node, indent)
--     local output = {}
--     local indentStr = string.rep(" ", indent or 0)

--     if type(node) == "string" then
--         table.insert(output, indentStr .. node)
--     else
--         for tag, children in pairs(node) do
--             if type(children) == "table" then
--                 if children[1] then  -- It's a list of child nodes
--                     for _, child in ipairs(children) do
--                         table.insert(output, indentStr .. "<" .. tag)
--                         if child["@"] then
--                             for attr, value in pairs(child["@"]) do
--                                 table.insert(output, " " .. attr .. "=\"" .. value .. "\"")
--                             end
--                         end
--                         if child:value() then
--                             table.insert(output, ">" .. child:value() .. "</" .. tag .. ">")
--                         else
--                             table.insert(output, ">")
--                             table.insert(output, serializeXml(child, (indent or 0) + 2))
--                             table.insert(output, indentStr .. "</" .. tag .. ">")
--                         end
--                     end
--                 else  -- It's a single child node
--                     table.insert(output, indentStr .. "<" .. tag)
--                     if children["@"] then
--                         for attr, value in pairs(children["@"]) do
--                             table.insert(output, " " .. attr .. "=\"" .. value .. "\"")
--                         end
--                     end
--                     if children:value() then
--                         table.insert(output, ">" .. children:value() .. "</" .. tag .. ">")
--                     else
--                         table.insert(output, ">")
--                         table.insert(output, serializeXml(children, (indent or 0) + 2))
--                         table.insert(output, indentStr .. "</" .. tag .. ">")
--                     end
--                 end
--             end
--         end
--     end

--     return table.concat(output, "\n")
-- end

-- function readXMLfile(filename)

--   freeswitch.consoleLog("info", "Current Directory: " .. io.popen("pwd"):read('*l'))
--   package.path = package.path .. ";/usr/local/freeswitch/scripts/?.lua"
--   freeswitch.consoleLog("info", "package.path: " .. package.path)
--   local xml = require("xmlSimple").newParser()
--   if not xml then
--     freeswitch.consoleLog("debug","Failed to create xml parser\n")   
--     return
--   else
--     freeswitch.consoleLog("debug","XML parser is created\n")   
--   end
--   xmlDoc = xml:loadFile(filename)
  
--   if not xmlDoc then
--     freeswitch.consoleLog("debug","Failed to load XML file:" .. filename .. "\n")   
--     return
--   else
--     freeswitch.consoleLog("debug","XML file is loaded\n")   
--   end
  
-- -- -- Access the 'one' attribute of the 'test' tag
-- -- local oneAttr = xmlDoc.test["@one"]
-- -- freeswitch.consoleLog("debug","test.one=" .. oneAttr .. "\n")   

-- -- local xml = require("xmlSimple").newParser()

-- -- Accessing values
-- local userId = xmlDoc.include.user["@id"]
-- -- local tuiPasswordParam = xmlDoc.include.user.params.param[1]["@value"]
-- -- local voicemailGreetingNumberParam = xmlDoc.include.user.params.param[2]["@value"]
-- -- local tuiPasswordVar = xmlDoc.include.user.variables.variable[1]["@value"]
-- -- local voicemailGreetingNumberVar = xmlDoc.include.user.variables.variable[2]["@value"]

-- -- Logging the values
-- freeswitch.consoleLog("debug", "User ID: " .. userId .. "\n")
-- -- freeswitch.consoleLog("debug", "TUI Password (Param): " .. tuiPasswordParam .. "\n")
-- -- freeswitch.consoleLog("debug", "Voicemail Greeting Number (Param): " .. voicemailGreetingNumberParam .. "\n")
-- -- freeswitch.consoleLog("debug", "TUI Password (Var): " .. tuiPasswordVar .. "\n")
-- -- freeswitch.consoleLog("debug", "Voicemail Greeting Number (Var): " .. voicemailGreetingNumberVar .. "\n")

-- -- Search for the 'param' with the attribute name equal to 'tui-password'
-- -- Search for the 'param' with the attribute name equal to 'tui-password'
-- local tuiPasswordValue = "4321"
-- local oldtuiPasswordValue = nil
-- for _, param in ipairs(xmlDoc.include.user.params.param) do
--   if param["@name"] == "tui-password" then
--       -- Modifying the value
--       oldtuiPasswordValue = param["@value"]
--       param["@value"] = tuiPasswordValue
--       break
--   end
-- end

-- -- Serialize the XML structure to a string
-- local serialized = serializeXml(xmlDoc)

-- -- Save the updated XML string back to the file
-- local file = io.open(filename, "w")
-- file:write(serialized)
-- file:close()

-- freeswitch.consoleLog("debug", "XML file updated.\n")

-- -- Logging the value
-- if tuiPasswordValue then
--     freeswitch.consoleLog("debug", "TUI Password: " .. oldtuiPasswordValue .. ",newValue:" .. tuiPasswordValue .. "\n")
-- else
--     freeswitch.consoleLog("debug", "TUI Password not found.\n")
-- end



-- -- Modifying values
-- -- xmlDoc.include.user.params.param[1]["@value"] = "4321"
-- -- xmlDoc.include.user.variables.variable[1]["@value"] = "4321"

-- -- Optionally save the modified XML back to file or use it as needed


-- -- xml.test["@one"] == "two"
-- -- xml.test.nine["@ten"] == "eleven"
-- -- xml.test.nine:value() == "twelve"
-- -- xml.test.three[1]["@four"][1] == "five"
-- -- xml.test.three[1]["@four"][2] == "six"
-- -- xml.test.three[2]:value() == "eight"


--   -- Require the LuaXML library
--   -- local xml = require("LuaXml")
--   -- local xobj = xml.eval(filename)
-- -- freeswitch.consoleLog("INFO","XML is: "..xobj.."\n")
--   -- local xml2lua = require("xml2lua")
--   -- local handler = require("xmlhandler.tree")
  
--   -- local parser = xml2lua.parser(handler)
--   -- parser:parse(filename)
  
--   freeswitch.consoleLog("debug","XML:\n")   
--   -- freeswitch.consoleLog(handler.root.yourXML)   
--   freeswitch.consoleLog("debug","XML-END\n")   
  
--   -- freeswitch.consoleLog("debug","After require LuaXml\n")   

--   -- local f = io.open(filename, "r")
--   -- if f then
--   --   freeswitch.consoleLog("debug","YES, we can open file\n")   
--   --   f:close()
--   -- else
--   --   freeswitch.consoleLog("debug","Cannot open file:" .. filename .. "\n")   
--   --     return
--   -- end
  
--   -- -- Load the XML file into a Lua table
--   -- local xfile = xml.load(filename)
--   -- local status, xfile = pcall(xml.load, filename)
--   -- if not status then
--   --   freeswitch.consoleLog("debug","Error loading XML:" .. xfile .. "\n")   
--   --     print("Error loading XML:", xfile)
--   -- end
  
--   -- if not xfile then
--   --   freeswitch.consoleLog("debug","Failed to load XML file:" .. filename .. "\n")   
--   --   return
--   -- end
--   -- -- Traverse the XML to find the correct user based on the ID
--   -- for i, user in ipairs(xfile:find("user")) do
--   --     if user["@id"] == "+61878133943" then
--   --         -- Update the tui-password param
--   --         for j, param in ipairs(user[1]) do  -- user[1] points to <params> tag
--   --             if param["@name"] == "tui-password" then
--   --                 param["@value"] = "new_password_value"  -- Replace with the desired new value
--   --             end
--   --         end
--   --     end
--   -- end
--   -- -- Save the modified XML back to the file
--   -- xfile:save(filename)
-- end
-- ========================================================================================================================
-- Serialize a node into an XML string
local function serializeNode(node, indent)
  indent = indent or ""
  local lines = {}
  local attributes = {}
  local children = node:children()
  local nodeName = node:name() or "errorNodeName"  -- Added a fallback for name error

  if not nodeName then
    print("Error: Node name is nil.")
    return ""
  end

  -- Attributes
  if node:properties() then  -- Check if properties exist
    for name, value in pairs(node:properties()) do
      table.insert(attributes, string.format('%s="%s"', name, node:ToXmlString(value)))
    end
  else
    print("No properties found for node:", nodeName)
  end

  local attributesStr = ""
  if #attributes > 0 then
    attributesStr = " " .. table.concat(attributes, " ")
  end

  local startTag = string.format("%s<%s%s", indent, nodeName, attributesStr)
  if #children == 0 then
    if node:value() then
      local value = node:ToXmlString(node:value())
      table.insert(lines, startTag .. ">" .. value .. "</" .. nodeName .. ">")
    else
      table.insert(lines, startTag .. "/>")
    end
  else
    table.insert(lines, startTag .. ">")
    for i, child in ipairs(children) do
      local childStr = serializeNode(child, indent .. "  ")
      table.insert(lines, childStr)
    end
    table.insert(lines, indent .. "</" .. nodeName .. ">")
  end

  return table.concat(lines, "\n")
end

-- Main serialization function
local function serializeXml(rootNode)
  return table.concat(serializeNode(rootNode, 0), "\n")
end

-- Function to save XML string to file
local function saveXmlToFile(xmlString, filename)
  local file, err = io.open(filename, "w")
  if file then
    file:write(xmlString)
    file:close()
  else
    error("Error writing to file: " .. tostring(err))
  end
end

-- Function to modify an XML file
local function modifyAndSaveXml(filename, modifyFunc)
  freeswitch.consoleLog("info", "Current Directory: " .. io.popen("pwd"):read('*l'))
  package.path = package.path .. ";/usr/local/freeswitch/scripts/?.lua"
  freeswitch.consoleLog("info", "package.path: " .. package.path)

  -- Initialize a new parser
  local parser = require("xmlSimple").newParser()

  local xmlDoc = parser:loadFile(filename)

  if xmlDoc then
    modifyFunc(xmlDoc) -- Pass the XML document to the provided modification function
    local xmlString = serializeXml(xmlDoc)
    saveXmlToFile(xmlString, filename)
  else
    error("Could not load XML document.")
  end
end

-- Example usage:
-- Suppose you want to change the value of a certain parameter
local function changeParameterValue(xmlDoc, paramName, newValue)
  for _, user in ipairs(xmlDoc:children()) do
    for _, params in ipairs(user:children()) do
      for _, param in ipairs(params:children()) do
        if param:name() == "param" and param["@name"] == paramName then
          freeswitch.debug("debug", "Found param: " .. paramName ..  ",value:" .. param["@value"] .. "\n")
          param["@value"] = newValue -- modify the parameter value
        end
      end
    end
  end
end

-- ===========New code===========

-- local file = io.open(filePath, "r") -- Open the file for reading
-- if not file then
--     return false, "Could not open file for reading."
-- end

-- local content = file:read("*a") -- Read the entire file content
-- file:close() -- Close the file

-- -- Pattern to find the tui-password line with any leading whitespace, including newline characters
-- local patternToFind = "(%s-)(<param%s+name=\"tui%-password\"%s+value=\"%d%d%d%d\")"

-- -- -- The line to insert
-- local lineToInsert = '<param name="voicemail_greeting_number" value="1"/>'

-- -- Replace the pattern with the new line inserted before it, preserving the indentation
-- local modifiedContent = content:gsub(patternToFind, function(whitespace, tuiPasswordLine)
--     -- We ensure that the whitespace contains at least one newline character
--     local lastNewline = whitespace:match(".*\n")
--     return lastNewline .. lineToInsert .. "\n" .. lastNewline .. tuiPasswordLine
-- end)

-- -- Open the file for writing
-- file = io.open(filePath, "w")
-- if not file then
--     return false, "Could not open file for writing."
-- end

-- -- Write the modified content back to the file
-- file:write(modifiedContent)
-- file:close() -- Close the file

-- return true
-- end

-- ===========End new code=======
-- Call modifyAndSaveXml function providing the file path and the modification function
-- modifyAndSaveXml("path/to, function(xmlDoc)
--   changeParameterValue(xmlDoc, "tui-password", "newpassword123")
-- end)
-- ========================================================================================================================
--- MARK: Other methods
local function reloadxml()
  -- Execute the 'reloadxml' command
  api = freeswitch.API() -- Create an API object
  local result = api:execute("reloadxml")
  
  -- Check the result for success or failure
  if result:match("OK") then
      -- Handle successful reload
      freeswitch.consoleLog("debug", "XML configuration successfully reloaded.\n")
  else
      -- Handle failed reload
      freeswitch.consoleLog("debug", "Failed to reload XML configuration.\n")
  end
  
end 

function readwriteXMLfile(filename)

  freeswitch.consoleLog("info", "Current Directory: " .. io.popen("pwd"):read('*l'))
  package.path = package.path .. ";/usr/local/freeswitch/scripts/?.lua"
  freeswitch.consoleLog("info", "package.path: " .. package.path)

  -- Initialize a new parser
  local parser = require("xmlSimple").newParser()
  
  -- Load the XML file
  local xmlDoc = parser:loadFile(filename)
  local oldtuiPasswordValue = nil
  local newtuiPasswordValue = "4321"

    -- Modify the parameter's value
  local paramNode = findParam(xmlDoc, "tui-password")
  if paramNode then
      oldtuiPasswordValue = paramNode["@value"]
      paramNode["@value"] = newtuiPasswordValue
      freeswitch.consoleLog("debug", "Found TUI password\n")
  end
  freeswitch.consoleLog("info", "About to chage tui password -old:" .. oldtuiPasswordValue  .. ", new:" .. newtuiPasswordValue .. "\n")

    
  local xmlString = serializeXml(xmlDoc)
  
  -- Save the XML string back to the file
  local file = io.open(filename, "w")
  -- file:write(parser:ToXmlString(xmlDoc))
  -- file:write(xmlString)
  file:close()

end
function playMailboxNumber(mailbox_number)
  acct_no_plus = string.gsub(mailbox_number, "%+", "")
  -- Play the fixed prompt
  session:streamFile("voicemail/optus-tui-default.wav")
  -- Play the mailbox number as digits
  -- session:say(acct_no_plus, "en", "number", "pronounced")
  -- session:execute("say", "en NUMBER DIGITS "..acct_no_plus)    -- "oh six six nine..."
  -- session:execute("say", "en number pronounced " .. acct_no_plus)
-- 2. Pronounce each digit of the number separately
  for digit in acct_no_plus:gmatch("%d") do
    session:execute("say", "en NUMBER PRONOUNCED "..digit)
    session:sleep(100)  -- Pause between digits (ms)
  end
  session:sleep(150)  -- Pause at the end
end

function playCustomGreeting_1(greetings)
    if not greetings or #greetings == 0 then
        freeswitch.consoleLog("warning", "No greetings available to play.\n")
        return
    end
    -- Get the first greeting
    local firstGreeting = greetings[1]

    if firstGreeting.audioURL then
        freeswitch.consoleLog("info", "Playing first greeting from URL: " .. firstGreeting.audioURL .. "\n")
        session:streamFile(firstGreeting.audioURL)
    else
        freeswitch.consoleLog("warning", "No audio URL found for the first greeting ID: " .. firstGreeting.id .. "\n")
    end
end

function playSubscriberGreeting(mailbox_number)
  -- Check if tui_subscriber is set to true or false
  freeswitch.consoleLog("warning", "PlaplaySubscriberGreeting - tui_subscriber:" .. tostring(tui_subscriber) .. ",global_greetings:" .. #global_greetings .. "\n")
  freeswitch.consoleLog("warning", "PlaplaySubscriberGreeting - greeting:" .. tostring(global_greetings[1]) .. "\n")
  if tui_subscriber == "true" then
      -- Play the mailbox number in a structured format
      if user_custom_greeting == "1" and #global_greetings == 1 then
        playCustomGreeting_1(global_greetings)
      else
        playMailboxNumber(mailbox_number)
      end
  else
      -- Play a custom greeting instead of the default one
      freeswitch.consoleLog("warning", "Playing MCS greeting - voicemail/optus-mcs-greeting.wav\n")
      session:streamFile("voicemail/optus-mcs-greeting.wav")
  end
end

--
-- JSON = (loadfile(script_root .. "json.lua"))()
pathsep = '/' 
-- Windows users do this instead: 
-- pathsep = '\' 
freeswitch.consoleLog("debug","optus-voicemail.lua - start - uuid:" .. session:getVariable("uuid") .. "\n")   
api = freeswitch.API()   
-- freeswitch.consoleLog("debug","after creating the API object\n")   
-- reply = api:executeString("version") 
-- freeswitch.consoleLog("debug","reply is: " .. reply .."\n") 
reply = api:executeString("status") 
freeswitch.consoleLog("debug","status reply: " .. reply .."\n") 
-- Execute 'show calls count' to get the number of current sessions
-- local session_count = api:execute("show", "calls count")
-- -- Remove leading and trailing whitespace (including newlines)
-- session_count = session_count:gsub("^%s+", ""):gsub("%s+$", "")
-- -- local session_count = api:executeString("show calls count") 
-- freeswitch.consoleLog("info", "Total current calls: " .. session_count .. "\n")

-- reply = api:executeString("sofia status") 
-- freeswitch.consoleLog("WARNING","reply is: " .. reply .."\n") 

-- Answer the call 
-- freeswitch.consoleLog("debug","Before answer\n") 
session:answer() 
-- freeswitch.consoleLog("debug","After answer\n") 
-- Define session and other initial requirements

local called_number = argv[1]

session:execute("set_user", called_number .. "@*************")
-- freeswitch.consoleLog("INFO", "CalledNumber: " .. called_number)
-- Fetch various parameters from the session
local destination_number = session:getVariable("destination_number")
local caller_id_number = session:getVariable("caller_id_number")
local outbound_caller_id_name = session:getVariable("outbound_caller_id_name")
user_config_folder = session:getVariable('conf_dir')

user_data = mozart.fetchUserConfig(called_number)
global_greetings = mozart.getGreetings(called_number)

mozart.fetchAndSaveUserConfig(called_number, user_config_folder)
reloadxml();
-- Assuming you already have user_data from the previous function
tui_subscriber = mozart.getConfigAttributeValue(user_data.params, "param", "tui_subscriber")
user_custom_greeting = mozart.getConfigAttributeValue(user_data.params, "param", "voicemail_greeting_number")
session:setVariable("tui_subscriber",tui_subscriber)

local tui_subscriber_var = session:getVariable("tui_subscriber") or "nil"

if called_number == "+61878284599" then
  session:setVariable("outbound_caller_id_name","+61450019775")
  outbound_caller_id_name = "+61450019775"
end 
if tui_subscriber == "false" then
  freeswitch.consoleLog("warning", "called_number: " .. called_number .. ", is MCS, session:tui-subscriber_ver:" .. tostring(tui_subscriber_var) .. "\n")
else
  session:setVariable("voicemail_greeting_number",user_custom_greeting)

  freeswitch.consoleLog("warning", "called_number: " .. called_number .. ", is TUI subscriber\n")
end

if outbound_caller_id_name == nil then
  outbound_caller_id_name = "Anonymous"
end
-- freeswitch.consoleLog("INFO", "destination_number: " .. destination_number .. ", caller_id_number:" .. caller_id_number .. ", outbound_caller_id_name:" .. outbound_caller_id_name .. "\n")

-- freeswitch.consoleLog("debug", "GREETING-PIN - tui-password: " .. session:getVariable("tui-password") .. "\n")
-- local conf_dir = session:getVariable("conf_dir")
-- local file_to_read =  conf_dir .. "/directory/vmusers/" .. called_number .. ".xml"
-- local file_to_read = "/usr/local/freeswitch/conf/directory/vmusers/" .. called_number .. ".xml"
-- local file_to_read = "/usr/local/freeswitch/conf/directory/vmusers/test.xml"
-- freeswitch.consoleLog("debug", "file_to_read: " .. file_to_read .. "\n")
-- readXMLfile(file_to_read)
-- readwriteXMLfile(file_to_read)
-- modifyAndSaveXml(file_to_read, function(xmlDoc)
--   changeParameterValue(xmlDoc, "tui-password", "newpassword123")
-- end)
-- changeTuiPin(file_to_read, "8765")
-- freeswitch.consoleLog("debug", "PIN is CHANGED \n")
-- setCustomGreeting(file_to_read)

-- setDefaultGreeting(file_to_read)
-- reloadxml();

-- Get an instance of the FreeSWITCH API
-- local api = freeswitch.API()


-- if session:ready() then
--   local channel_vars = session:getChannelVariables()
--   for name, value in pairs(channel_vars) do
--       freeswitch.consoleLog("info", string.format("Channel variable: %s = %s\n", name, value))
--   end
-- end
-- local global_vars_str = api:execute("global_getvar")
-- freeswitch.consoleLog("info", "Global variables:\n" .. global_vars_str)


session:consoleLog("INFO", "VR - caller_id_number: " .. caller_id_number .. ", outbound_caller_id_name: " .. outbound_caller_id_name)

-- First Extension: Checks for Caller ID
if destination_number:match("^(.*)-voicemail$") then
    local matched_dest = destination_number:match("^(.*)-voicemail$")
    
    if caller_id_number == outbound_caller_id_name then
  -- if caller_id_number ~= outbound_caller_id_name then
        session:consoleLog("INFO", "CallerID: " .. caller_id_number)
        session:consoleLog("INFO", "Called Number: " .. called_number)
        session:consoleLog("INFO", "outbound_caller_id_name:" .. outbound_caller_id_name)
        session:execute("answer")
        session:execute("lua", "tui-menu.lua" .. " " .. called_number)
        session:hangup()
        return
    end
end

-- Second Extension: Used to leave a voicemail if caller is not the owner
local file_path = ""
local greeting_folder = "/usr/local/freeswitch/storage/voicemail/hosted/*************/"
if destination_number:match("^(.*)-voicemail$") then
--     local matched_dest = destination_number:match("^(.*)-voicemail$")   
    -- session:execute("set_user", matched_dest .. "@*************")
    session:consoleLog("INFO", "CallerID: " .. caller_id_number .. ",Called Number: " .. called_number .. ",outbound_caller_id_name:" .. outbound_caller_id_name)
    user_config_folder = session:getVariable('conf_dir')

    -- session:consoleLog("INFO", "Called Number: " .. called_number)
    -- session:consoleLog("INFO", "outbound_caller_id_name:" .. outbound_caller_id_name)
    -- userURI = called_number .. "@*************"
    -- tmsi = api:execute("user_data", userURI .. " param vm-tmsi")
    tmsi = string.gsub(called_number, "%+", "")
    session:setVariable("vm-tmsi", tmsi)
    session:consoleLog("INFO", "Get tmsi from user.xml file and store in vm-tmsi variable:" .. tmsi)
    session:execute("jitterbuffer", "60")
    session:execute("set", "record_waste_resources=true")
    session:execute("answer")
    session:execute("set", "skip_instructions=true")
    session:execute("set", "skip_greeting=true")  -- Prevent voicemail app from playing any greeting
    -- if user_custom_greeting then
    --   file_path = greeting_folder .. called_number
    --   mozart.downloadFirstGreeting(called_number, file_path)
    -- end
     -- Set variables to track voicemail
     session:setVariable("voicemail_expected", "true")
     session:setVariable("voicemail_received", "false")
    session:sleep(500)
    playSubscriberGreeting(called_number)
    session:sleep(100)
    session:execute("voicemail", "hosted ************* " .. called_number)
-- Check voicemail received status
  -- local received = session:getVariable("voicemail_received")
  -- if received == "false" then
  --     freeswitch.consoleLog("NOTICE", "Slam down detected for mailbox: " .. called_number .. "\n")
  --     mozart.slamDownNotification(called_number,caller_id_number)
  --     -- Handle slam down case (e.g., log or notify)
  -- else
  --     freeswitch.consoleLog("NOTICE", "Voicemail left successfully for mailbox: " .. called_number .. "\n")
  -- end

end
local filename = file_path .. "/greeting_1.wav"
local status, err = os.remove(filename)
if not status then
    freeswitch.consoleLog("error","Error deleting file: " .. err .. "\n") 
else
    freeswitch.consoleLog("debug","File deleted: " .. filename .. "\n") 
end

local conf_file = user_config_folder .. "/directory/vmusers/" .. called_number .. ".xml"
local status, err = os.remove(conf_file)
if not status then
    freeswitch.consoleLog("error","Error deleting conf file: " .. err .. "\n") 
else
    freeswitch.consoleLog("debug","Conf file deleted: " .. conf_file .. "\n") 
end
freeswitch.consoleLog("debug","EXIT optus-voicemail.lua - called_number:" .. called_number .. "\n")   

