package.path = package.path .. ";/usr/local/freeswitch/scripts/?.lua"
local mozart = require("mozart")
JSON = (loadfile "/usr/local/freeswitch/scripts/json.lua")()

function encodeparams(paramsT)
	function escape (s)
	  s = string.gsub(
		s, 
		'([\r\n"#%%&+:;<=>?@^`{|}%\\%[%]%(%)$!~,/\'])', 
		function (c)
			return '%'..string.format("%X", string.byte(c))
		end
	)
	  s = string.gsub(s, "%s", "+")
	  return s
	end

	function encode (t)
	  local s = ""
	  for k , v in pairs(t) do
		s = s .. "&" .. escape(k) .. "=" .. escape(v)
	  end
	  return string.sub(s, 2)     -- remove first `&'
	end
	
	if type(paramsT) == 'table' then
		return encode(paramsT)
	else
		local tmp = Utils:commaText(paramsT, '&'); 
		local myParamsT = {};
		for k, v in pairs(tmp) do
			local pos = 0
			pos = string.find(v, '=')
			if not pos then return '' end
			myParamsT[string.sub(v, 1, pos-1 )] = string.sub(v, pos+1 )
		end
		return encode(myParamsT)
	end
end

-- Function to get an environment variable or return a default value
function getEnvOrDefault(varName, defaultValue)
	local value = os.getenv(varName)
	if value == nil or value == '' then
		 return defaultValue
	else
		 return value
	end
end


-- For development, let's find out what we know
-- The script here lists some parameters, so let's log them
-- https://freeswitch.org/jira/secure/attachment/22967/event-CUSTOM-vm_maintenance-to-fsapi.lua 

last_app = event:getHeader("variable_last_app")
voicemail_message_len = event:getHeader("variable_voicemail_message_len")
freeswitch.consoleLog("notice", "MCS Event Triggered - last_app:" .. tostring(last_app) .. "\n")

-- if last_app ~= "voicemail" then
--  freeswitch.consoleLog("notice", "VR- answer? - MISSED CALL as well??\n" ) 
--  return end

-- freeswitch.consoleLog("notice", "MC- Caller-Caller-ID-Name: " .. (event:getHeader("Caller-Caller-ID-Name") or "") .. "\n" )
-- freeswitch.consoleLog("warning", "MC- Caller-Caller-ID-Number: " .. (event:getHeader("Caller-Caller-ID-Number") or "") .. "\n" )
-- freeswitch.consoleLog("warning", "MC- Caller-Destination-Number: " .. (event:getHeader("Caller-Destination-Number") or "") .. "\n" )
-- freeswitch.consoleLog("notice", "MC- Event-Date-Local: " .. (event:getHeader("Event-Date-Local") or "") .. "\n" )
-- freeswitch.consoleLog("warning", "MC- Caller-Username: " .. (event:getHeader("Caller-Username") or "") .. "\n" )
-- freeswitch.consoleLog("warning", "MC- variable_user_name: " .. (event:getHeader("variable_user_name") or "") .. "\n" )
-- freeswitch.consoleLog("notice", "MC- variable_domain_name: " .. (event:getHeader("variable_domain_name") or "") .. "\n" )
-- freeswitch.consoleLog("warning", "MC- variable_last_app: " .. (last_app or "") .. "\n" )
-- freeswitch.consoleLog("warning", "MC- voicemail_message_len: " .. (voicemail_message_len or "") .. "\n" )
-- gmt_date = event:getHeader("Event-Date-GMT")
-- timestamp = event:getHeader("Event-Date-Timestamp")

if voicemail_message_len then
  freeswitch.consoleLog("debug", "User left-voicemail - length: " .. (voicemail_message_len or "") .. "\n" ) 
  return end

-- Processing, build the parameters
caller_id_name = event:getHeader("Caller-Caller-ID-Name")
caller_id_number = event:getHeader("Caller-Caller-ID-Number")
user = event:getHeader("variable_sip_to_user")
user_data = mozart.fetchUserConfig(user)
local tui_subscriber = mozart.getConfigAttributeValue(user_data.params, "param", "tui_subscriber")

if tui_subscriber == "false" then
	freeswitch.consoleLog("notice", "MCS no voicemail - report missed call - user:" .. user .. ",caller_id_name:" .. caller_id_name .. ",caller_id_number:" .. caller_id_number .. "\n" )
	mozart.slamDownNotification(user, caller_id_name)
else 
	freeswitch.consoleLog("notice", "User is TUI subscriber:" .. tostring(tui_subscriber) .. "\n" )
end

