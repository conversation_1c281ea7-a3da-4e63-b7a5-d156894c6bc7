local download = {}
local http = require("socket.http")
local ltn12 = require("ltn12")
local json = require("dkjson")
local mime = require("mime")
local xml2lua = require("xml2lua")
local handler = require("xmlhandler.tree")
-- local https = require("ssl.https")
-- local json = require("cjson")

-- Server configuration
local mozart_voicemails_endpoint = os.getenv("MOZART_SERVER") .. "/api/v1/admin/voicemails"
local mozart_greetings_endpoint = os.getenv("MOZART_SERVER") .. "/api/v1/admin/greetings"
local mozart_config_endpoint = os.getenv("MOZART_SERVER") .. "/directory"
local mozart_config_parameter_endpoint = os.getenv("MOZART_SERVER") .. "/users/modify"


local mozart_user_pswrd_headers = {
    ["userid"] = "proton",
    ["password"] = "proton123"
}

local called_number = ""
local global_greetings = nil


-- Function to update a voicemail (mark as read or saved)
function download.updateVoicemail(account, voicemail_id, isRead, isSaved)
    local url = mozart_voicemails_endpoint .. "/" .. voicemail_id .. "?acct=" .. account
    local response = {}
    local data = json.encode({
        voicemail = {
            isRead = isRead,
            isRecored = false,
            isSaved = isSaved
        }
    })
    local specific_headers = {
        ["Content-Type"] = "application/json",
        ["Content-Length"] = tostring(#data)
    }
    -- Merge headers
    local headers = mergeHeaders(mozart_user_pswrd_headers, specific_headers)

    local _, status_code = http.request{
        method = "PATCH",
        url = url,
        headers = headers,
        source = ltn12.source.string(data),
        sink = ltn12.sink.table(response)
    }

    freeswitch.consoleLog("INFO", "Update voicemail response: " .. table.concat(response) .. "\n")
    return status_code == 200
end

-- Function to download a file from a URL and save it locally
function download.downloadFile(url, filename)
    local file = io.open(filename, "wb")
    if not file then
        freeswitch.consoleLog("error", "Failed to open file for writing: " .. filename .. "\n")
        return false
    end

    local response, code, headers, status = http.request{
        url = url,
        sink = ltn12.sink.file(file)
    }

    if code == 200 then
        freeswitch.consoleLog("info", "Downloaded file to " .. filename .. "\n")
        return true
    else
        freeswitch.consoleLog("error", "Failed to download file. HTTP code: " .. tostring(code) .. "\n")
        return false
    end
end
function download.downloadFileCurl(url, filename)
   local command = "curl -o " .. filename .. " '" .. url .. "'"
   freeswitch.consoleLog("warning", "downloadFileCurl -cmd:" .. command .. "\n")
   local result = os.execute(command)
   if result == 0 then
       freeswitch.consoleLog("info", "Downloaded file to " .. filename .. "\n")
       return true
   else
       freeswitch.consoleLog("error", "Failed to download file. Command: " .. command .. "\n")
       return false
   end
end

function download.deleteGreetings_vr(greetings, account)
if greetings then
    for _, greeting in ipairs(greetings) do
        if greeting.type == "custom" and greeting.isActive then
            -- Try deleting this greeting
            local deleted = download.deleteGreeting_vr(greeting.id, account)
            if deleted then
                freeswitch.consoleLog("info", "Deleted greeting: " .. greeting.id .. "\n")
            end
        end
    end
end
end
function download.getGreetings(account)
    local response = {}
    local acct_no_plus = string.gsub(account, "%+", "")
    
    freeswitch.consoleLog("info", "Get greetings for: " .. acct_no_plus .. "\n")

    local url = mozart_greetings_endpoint .. "?acct=" .. acct_no_plus
    freeswitch.consoleLog("info", "HTTP request - url: " .. url .. "\n")

    local res, code, headers, status = http.request{
        url = url,
        method = "GET",
        headers = mozart_user_pswrd_headers,
        sink = ltn12.sink.table(response)
    }

    if code == 200 then
        local responseBody = table.concat(response)
        local ok, greetings = pcall(json.decode, responseBody)
        if ok and greetings and greetings.data and greetings.data.greetings then
            for i, greeting in ipairs(greetings.data.greetings) do
                freeswitch.consoleLog("info", string.format(
                    "Greeting[%d]: id=%s, type=%s, active=%s, audioURL=%s\n",
                    i,
                    greeting.id or "nil",
                    greeting.type or "nil",
                    tostring(greeting.isActive),
                    greeting.audioURL or "nil"
                ))
            end
            return greetings.data.greetings
        else
            freeswitch.consoleLog("warning", "Unexpected JSON or no greetings.\n")
            freeswitch.consoleLog("debug", "Raw response: " .. responseBody .. "\n")
            return nil
        end
    else
        freeswitch.consoleLog("warning", "Failed to fetch greetings. HTTP Code: " .. tostring(code) .. "\n")
        if response and #response > 0 then
            freeswitch.consoleLog("debug", "Response body: " .. table.concat(response) .. "\n")
        end
        return nil
    end
end

function download.deleteGreeting_vr(greetingId, acct)
    freeswitch.consoleLog("error", "deleteGreeting_vr - id:" .. tostring(greetingId) .. ",acct:" .. tostring(acct) .. "\n")
    if not greetingId or not acct then
        freeswitch.consoleLog("error", "Missing greetingId or acct for delete\n")
        return false
    end

    local acct_no_plus = string.gsub(acct, "%+", "")
    local response = {}

    local delete_url = string.format(
        "http://mozart-alb-1142825309.ap-southeast-2.elb.amazonaws.com/api/v1/admin/greetings/%s?acct=%s",
        greetingId,
        acct_no_plus
    )

    freeswitch.consoleLog("info", "Deleting greeting ID: " .. greetingId .. "\n")
    freeswitch.consoleLog("info", "DELETE URL: " .. delete_url .. "\n")

    local res, code, headers, status = http.request{
        url = delete_url,
        method = "DELETE",
        headers = {
            ["userid"] = "proton",
            ["password"] = "proton123",
            ["Content-Length"] = "0"  -- Important: DELETE may fail without this
        },
        source = ltn12.source.string(""),
        sink = ltn12.sink.table(response)
    }

    if code == 200 or code == 204 then
        freeswitch.consoleLog("info", "Greeting deleted successfully. Code: " .. tostring(code) .. "\n")
        return true
    else
        freeswitch.consoleLog("error", "Failed to delete greeting. HTTP Code: " .. tostring(code) .. "\n")
        if response and #response > 0 then
            freeswitch.consoleLog("debug", "Response body: " .. table.concat(response) .. "\n")
        end
        return false
    end
end

-- Modified function to download the first greeting instead of streaming
function download.downloadFirstGreeting(account, save_path)
    local greetings = download.getGreetings(account)
    freeswitch.consoleLog("warning", "User:" .. account .. " has: " .. #greetings .. " greeting(s).\n")
    if not greetings then
        freeswitch.consoleLog("warning", "No greetings found for account: " .. account .. "\n")
        return
    end

    local firstGreeting = greetings[1]
    if firstGreeting and firstGreeting.audioURL then
        local filename = save_path .. "/greeting_1" .. "." .. firstGreeting.fileExtension
        if download.downloadFileCurl(firstGreeting.audioURL, filename) then
            freeswitch.consoleLog("info", "First greeting downloaded and saved as: " .. filename .. "\n")
        end
    else
        freeswitch.consoleLog("warning", "No audio URL found for the first greeting.\n")
    end
end
function download.fetchUserConfig(called_number)
   local response = {}
   local url = mozart_config_endpoint .. "/" .. called_number
   freeswitch.consoleLog("info", "HTTP request - url:" .. url .. "\n")
   local res, code, headers, status = http.request{
       url = url,
       method = "GET",
       headers = mozart_user_pswrd_headers,
       sink = ltn12.sink.table(response)
   }

   if code == 200 then
       local xml_data = table.concat(response)

       -- Parsing the XML data
       local parser = xml2lua.parser(handler)
       local success, err = pcall(function()
           parser:parse(xml_data)
       end)

       if not success then
           freeswitch.consoleLog("err", "Error parsing XML: " .. err .. "\n")
           return nil
       end

       freeswitch.consoleLog("warning", "Config - response 200 - xml_data: " .. xml_data .. "\n")
       -- Accessing the parsed XML structure
       local user_data = handler.root.include.user
       freeswitch.consoleLog("warning", "Config - user_data: " .. tostring(user_data) .. "\n")
       if not user_data then
           freeswitch.consoleLog("err", "Invalid XML format. No user data found.\n")
           return nil
       end

       --    The following Accessing parameters and Accesssing variables code is just for debugging and logging names and values

    --    -- Accessing parameters
    --    if user_data.params and user_data.params.param then
    --        for _, param in ipairs(user_data.params.param) do
    --            local name = param._attr.name
    --            local value = param._attr.value
    --            freeswitch.consoleLog("info", "Param - Name: " .. name .. ", Value: " .. value .. "\n")
    --        end
    --    else
    --        freeswitch.consoleLog("warning", "No parameters found.\n")
    --    end

    --    -- Accessing variables
    --    if user_data.variables and user_data.variables.variable then
    --        for _, variable in ipairs(user_data.variables.variable) do
    --            local name = variable._attr.name
    --            local value = variable._attr.value
    --            freeswitch.consoleLog("info", "Variable - Name: " .. name .. ", Value: " .. value .. "\n")
    --        end
    --    else
    --        freeswitch.consoleLog("warning", "No variables found.\n")
    --    end

       return user_data
   else
       freeswitch.consoleLog("err", "Failed to fetch user config. HTTP Code: " .. tostring(code) .. "\n")
       return nil
   end
end

-- Function to get the value of a specific param or variable
function download.getConfigAttributeValue(data, attrType, attrName)
 
   if not data or not attrType or not attrName then
      freeswitch.consoleLog("ERR", "getAttributeValue - Invalid arguments\n")
      return nil
   end

   if data and data.param then
      for _, param in ipairs(data.param) do
         if param._attr.name == attrName then 
          local value = param._attr.value
          freeswitch.consoleLog("INFO", "getAttributeValue - type:" ..attrType .. ",name:" .. attrName .. ",Found value:" .. param._attr.value .. "\n")
          return param._attr.value
         end
      end
  else
      freeswitch.consoleLog("warning", "No parameters found.\n")
  end

  freeswitch.consoleLog("ERR", "getAttributeValue - Attribute not found\n")
  return nil
end

function download.fetchAndSaveUserConfig(called_number, user_config_folder)
   local response = {}
   local url = mozart_config_endpoint .. "/" .. called_number
   freeswitch.consoleLog("info", "HTTP request - url:" .. url .. "\n")
   local res, code, headers, status = http.request{
       url = url,
       method = "GET",
       headers = mozart_user_pswrd_headers,
       sink = ltn12.sink.table(response)
   }

   if code == 200 then
       local user_config_file = user_config_folder .. "/directory/vmusers/" .. called_number .. ".xml"
       local file = io.open(user_config_file, "w")
       if file then
           file:write(table.concat(response))
           file:close()
           freeswitch.consoleLog("info", "User config saved to " .. user_config_file .. "\n")
           return user_config_file
       else
           freeswitch.consoleLog("err", "Failed to save user config file\n")
           return nil
       end
       -- local response_text = table.concat(response)
       -- freeswitch.consoleLog("warning", "Config data for:" .. called_number .. " is: \n" .. response_text .. "\n")
       -- return user_config_file
   else
       freeswitch.consoleLog("err", "Failed to fetch user config. HTTP Code: " .. tostring(code) .. "\n")
       return nil
   end
end

function download.mergeHeaders(global, specific)
   local merged = {}
   -- Copy global headers
   for k, v in pairs(global) do
       merged[k] = v
   end
   -- Add/Override with specific headers
   for k, v in pairs(specific) do
       merged[k] = v
   end
   return merged
end

function download.slamDownNotification(called_number, calling_number)
   acct_no_plus = string.gsub(called_number, "%+", "")
   local url =  mozart_voicemails_endpoint .. "?acct=" .. acct_no_plus

   -- JSON data to send
   local data = {
      voicemail = {
         duration = 0,
         toId = called_number,
         fromId = calling_number
      }
   }
   -- Encode the data to JSON format
   local payload = json.encode(data)

   local response_body = {}
   freeswitch.consoleLog("debug", "Upload slamdown url:" .. url .. "\n")
   local specific_headers = {
         ["Content-Type"] = "application/json",
         ["Content-Length"] = tostring(#payload)
   }
   -- Merge headers
   local headers = download.mergeHeaders(mozart_user_pswrd_headers, specific_headers)
    freeswitch.consoleLog("debug", "Upload slamdown payload:" .. payload .. "\n")

   local res, code, response_headers, status = http.request{
         url = url,
         method = "POST",
         headers = headers,
         source = ltn12.source.string(payload),
         sink = ltn12.sink.table(response_body)
   }

   if code == 200 or code == 201 then
         freeswitch.consoleLog("info", "Slam down uploaded successfully.\n")
   else
         freeswitch.consoleLog("err", "Failed to upload slam down notificatoin. Status: " .. tostring(status) .. "\n")
   end

   -- Print the response
   if response_body then
         freeswitch.consoleLog("info", "Response: " .. table.concat(response_body) .. "\n")
   end
end

function download.playCustomGreeting(greetings)
    if not greetings or #greetings == 0 then
        freeswitch.consoleLog("warning", "No greetings available to play.\n")
        return
    end
    -- Get the first greeting
    local firstGreeting = greetings[1]

    if firstGreeting.audioURL then
        freeswitch.consoleLog("info", "Playing first greeting from URL: " .. firstGreeting.audioURL .. "\n")
        session:streamFile(firstGreeting.audioURL)
    else
        freeswitch.consoleLog("warning", "No audio URL found for the first greeting ID: " .. firstGreeting.id .. "\n")
    end
end

return download
