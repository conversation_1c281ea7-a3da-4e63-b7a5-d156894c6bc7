 -- modified by DW for testing/deployment  13Apr17

JSON = (loadfile "/usr/local/freeswitch/scripts/json.lua")()

function encodeparams(paramsT)
	function escape (s)
	  s = string.gsub(
		s, 
		'([\r\n"#%%&+:;<=>?@^`{|}%\\%[%]%(%)$!~,/\'])', 
		function (c)
			return '%'..string.format("%X", string.byte(c))
		end
	)
	  s = string.gsub(s, "%s", "+")
	  return s
	end

	function encode (t)
	  local s = ""
	  for k , v in pairs(t) do
		s = s .. "&" .. escape(k) .. "=" .. escape(v)
	  end
	  return string.sub(s, 2)     -- remove first `&'
	end
	
	if type(paramsT) == 'table' then
		return encode(paramsT)
	else
		local tmp = Utils:commaText(paramsT, '&'); 
		local myParamsT = {};
		for k, v in pairs(tmp) do
			local pos = 0
			pos = string.find(v, '=')
			if not pos then return '' end
			myParamsT[string.sub(v, 1, pos-1 )] = string.sub(v, pos+1 )
		end
		return encode(myParamsT)
	end
end

-- Function to get an environment variable or return a default value
function getEnvOrDefault(varName, defaultValue)
	local value = os.getenv(varName)
	if value == nil or value == '' then
		 return defaultValue
	else
		 return value
	end
end


-- For development, let's find out what we know
-- The script here lists some parameters, so let's log them
-- https://freeswitch.org/jira/secure/attachment/22967/event-CUSTOM-vm_maintenance-to-fsapi.lua 

last_app = event:getHeader("variable_last_app")
voicemail_message_len = event:getHeader("variable_voicemail_message_len")

-- if last_app ~= "voicemail" then
--  freeswitch.consoleLog("notice", "VR- answer? - MISSED CALL as well??\n" ) 
--  return end

-- freeswitch.consoleLog("notice", "MC- Caller-Caller-ID-Name: " .. (event:getHeader("Caller-Caller-ID-Name") or "") .. "\n" )
freeswitch.consoleLog("debug", "MC- Caller-Caller-ID-Number: " .. (event:getHeader("Caller-Caller-ID-Number") or "") .. "\n" )
freeswitch.consoleLog("debug", "MC- Caller-Destination-Number: " .. (event:getHeader("Caller-Destination-Number") or "") .. "\n" )
-- freeswitch.consoleLog("notice", "MC- Event-Date-Local: " .. (event:getHeader("Event-Date-Local") or "") .. "\n" )
freeswitch.consoleLog("debug", "MC- Caller-Username: " .. (event:getHeader("Caller-Username") or "") .. "\n" )
freeswitch.consoleLog("debug", "MC- variable_user_name: " .. (event:getHeader("variable_user_name") or "") .. "\n" )
-- freeswitch.consoleLog("notice", "MC- variable_domain_name: " .. (event:getHeader("variable_domain_name") or "") .. "\n" )
freeswitch.consoleLog("debug", "MC- variable_last_app: " .. (last_app or "") .. "\n" )
freeswitch.consoleLog("debug", "MC- voicemail_message_len: " .. (voicemail_message_len or "") .. "\n" )
gmt_date = event:getHeader("Event-Date-GMT")
-- timestamp = event:getHeader("Event-Date-Timestamp")

if voicemail_message_len then
  freeswitch.consoleLog("debug", "User left-voicemail - length: " .. (voicemail_message_len or "") .. "\n" ) 
  return end

-- freeswitch.consoleLog("notice", "No voicemail - report missed call\n" )

-- Processing, build the parameters
user = event:getHeader("variable_user_name")
domain = event:getHeader("variable_domain_name")
caller_id_name = event:getHeader("Caller-Caller-ID-Name")
caller_id_number = event:getHeader("Caller-Caller-ID-Number")
destination_number = event:getHeader("variable_user_name")
uuid = event:getHeader("Unique-ID")

-- Load user data
api = freeswitch.API();
userURI = user .. "@" .. domain
from = api:execute("user_data", userURI ..  " var outbound_caller_id_number")
tmsi = api:execute("user_data", userURI .. " param vm-tmsi")
hlrGuid = api:execute("user_data", userURI .. " param vm-hlr")

-- freeswitch.consoleLog("info", "Loaded user data\n")

-- freeswitch.consoleLog("WARNING", "tmsi is " .. (tmsi or "nil") .. "\n")

if caller_id_name == ''
        or caller_id_name == 'UNKNOWN'
        or caller_id_name == 'UNASSIGNED'
        or caller_id_name == 'WIRELESS CALLER'
        or caller_id_name == 'TOLL FREE CALL'
        or caller_id_name == 'Anonymous'
        or caller_id_name == 'Unavailable'
        then caller_id_name = nil end
if caller_id_number == ''
        then caller_id_number = nil 
end

message = "Missed Call  "
if caller_id_name
        then message = message .. "from " .. caller_id_name .. " (" ..  caller_id_number .. ")"
        elseif caller_id_number
        then message = message .. "from " .. caller_id_number end
message = message .. "to box: " .. destination_number .. "(tmsi: " .. tmsi .. ")"

-- freeswitch.consoleLog("WARNING", "message: " .. message .. "\n")

structure = {
	voicemail = {
	        url = "#",
			tmsi = tmsi,
			message_id = uuid,
        	duration = 0, 
        	to_number    = destination_number ,
        	sender  = caller_id_name,
--		received_at = os.date("%Y-%m-%dT%H:%M:%S%Z", timestamp)
		received_at = gmt_date
	} 
}

-- message = message .. " at " .. os.date("%c", timestamp)
message = message .. " at " .. gmt_date
message = message .. "."
-- Add JSON string escapes to the message
message = string.gsub(message, "([\\\"'])", "\\%1")

-- Prepare the data
data = '{ "to": "' .. (tmsi or "nil") .. '", "from": "' .. from .. '", "body": "' .. message .. '"}'
-- freeswitch.consoleLog("notice", "Final message:\n" .. message .. "\n")
-- freeswitch.consoleLog("notice", "Final data:\n" .. data .. "\n")

-- Select the target:
-- hlrPasiphae = "ba551fae-09a8-4d36-bdb9-c0209ac1000d"
-- hlrSinope =   "3592501b-11c8-4a73-8f26-e9c16b61e6dd"
-- hlrIo =       "fd93eba0-69bb-496d-83b5-0b657ea9d791"
-- if hlrGuid == hlrPasiphae
-- 	then url = "https://pasiphae.norwoodsystems.com/api/voicemail/missed_call.json"
-- elseif hlrGuid == hlrSinope
-- 	then url = "https://sinope.norwoodsystems.com/api/voicemail/missed_call.json"
-- elseif hlrGuid == hlrIo 
--         then url = "https://io.norwoodsystems.com/api/v3/voicemail/missed_call.json"
-- else
	-- use the venerable delegate via Jupiter
	-- url = "https://jupiter.norwoodsystems.com/servers/" .. hlrGuid .. "/api/voicemail/missed_call.json"
  local io_server = getEnvOrDefault("IO_SERVER", "io-optus.norwoodsystems.io")
	url = "https://" .. io_server .. "/api/v3/voicemail/missed_call.json"
-- end

-- Send it!
json_string = JSON:encode(structure)

curlParamString =  url .. " content-type application/json post '" .. json_string .. "'"
freeswitch.consoleLog("notice", "Missed Call - (" .. uuid .. "( Sending with command: \ncurl " .. curlParamString .. "\n")
-- api:execute("curl", curlParamString)
freeswitch.consoleLog("notice", "Missed Call - (" .. uuid .. "( curl return\n")
