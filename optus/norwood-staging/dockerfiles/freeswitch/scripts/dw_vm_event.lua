 -- modified by DW for testing/deployment  13Apr17

JSON = (loadfile "/usr/local/freeswitch/scripts/json.lua")()

function encodeparams(paramsT)
	function escape (s)
	  s = string.gsub(
		s, 
		'([\r\n"#%%&+:;<=>?@^`{|}%\\%[%]%(%)$!~,/\'])', 
		function (c)
			return '%'..string.format("%X", string.byte(c))
		end
	)
	  s = string.gsub(s, "%s", "+")
	  return s
	end

	function encode (t)
	  local s = ""
	  for k , v in pairs(t) do
		s = s .. "&" .. escape(k) .. "=" .. escape(v)
	  end
	  return string.sub(s, 2)     -- remove first `&'
	end
	
	if type(paramsT) == 'table' then
		return encode(paramsT)
	else
		local tmp = Utils:commaText(paramsT, '&'); 
		local myParamsT = {};
		for k, v in pairs(tmp) do
			local pos = 0
			pos = string.find(v, '=')
			if not pos then return '' end
			myParamsT[string.sub(v, 1, pos-1 )] = string.sub(v, pos+1 )
		end
		return encode(myParamsT)
	end
end


freeswitch.consoleLog("notice","DW's voicemail script started\n")

-- For development, let's find out what we know
-- The script here lists some parameters, so let's log them
-- https://freeswitch.org/jira/secure/attachment/22967/event-CUSTOM-vm_maintenance-to-fsapi.lua 

freeswitch.consoleLog("notice", "VM-Action: " .. (event:getHeader("VM-Action") or "") .. "\n" )

-- Do we need to do anything?
action = event:getHeader("VM-Action")
if action ~= "leave-message" then return end

freeswitch.consoleLog("debug", "VM-User: " .. event:getHeader("VM-User") .. "\n" )
freeswitch.consoleLog("debug", "VM-Domain: " .. event:getHeader("VM-Domain") .. "\n" )
freeswitch.consoleLog("debug", "VM-Caller-ID-Name: " .. (event:getHeader("VM-Caller-ID-Name") or "nil") .. "\n" )
freeswitch.consoleLog("debug", "VM-Caller-ID-Number: " .. (event:getHeader("VM-Caller-ID-Number") or "nil") .. "\n" )
freeswitch.consoleLog("debug", "VM-File-Path: " .. (event:getHeader("VM-File-Path") or "nil") .. "\n" )
freeswitch.consoleLog("debug", "VM-Flags: " .. (event:getHeader("VM-Flags") or "nil") .. "\n" )
freeswitch.consoleLog("debug", "VM-Folder: " .. (event:getHeader("VM-Folder") or "nil") .. "\n" )
freeswitch.consoleLog("debug", "VM-UUID: " .. (event:getHeader("VM-UUID") or "nil") .. "\n" )
freeswitch.consoleLog("debug", "VM-Message-Len: " .. (event:getHeader("VM-Message-Len") or "nil") .. "\n" )
freeswitch.consoleLog("debug", "VM-Timestamp: " .. (event:getHeader("VM-Timestamp") or "nil") .. "\n" )

-- Processing, build the parameters
user = event:getHeader("VM-User")
domain = event:getHeader("VM-Domain")
caller_id_name = event:getHeader("VM-Caller-ID-Name")
caller_id_number = event:getHeader("VM-Caller-ID-Number")

file_path = event:getHeader("VM-File-Path")
flags = event:getHeader("VM-Flags")
folder = event:getHeader("VM-Folder")
uuid = event:getHeader("VM-UUID")

freeswitch.consoleLog("WARNING", "Processing voicemail for " .. user .. "\n")

message_len = event:getHeader("VM-Message-Len")
timestamp = event:getHeader("VM-Timestamp")

-- Load user data
api = freeswitch.API();
userURI = user .. "@" .. domain
from = api:execute("user_data", userURI ..  " var outbound_caller_id_number")
tmsi = api:execute("user_data", userURI .. " param vm-tmsi")
hlrGuid = api:execute("user_data", userURI .. " param vm-hlr")

container_ip = api:executeString("global_getvar domain")
freeswitch.consoleLog("info", "Loaded user data\n")

freeswitch.consoleLog("WARNING", "tmsi is " .. (tmsi or "nil") .. "\n")

if caller_id_name == ''
        or caller_id_name == 'UNKNOWN'
        or caller_id_name == 'UNASSIGNED'
        or caller_id_name == 'WIRELESS CALLER'
        or caller_id_name == 'TOLL FREE CALL'
        or caller_id_name == 'Anonymous'
        or caller_id_name == 'Unavailable'
        then caller_id_name = nil end
if caller_id_number == ''
        then caller_id_number = nil 
end

message = "Voicemail "
if caller_id_name
        then message = message .. "from " .. caller_id_name .. " (" ..  caller_id_number .. ")"
        elseif caller_id_number
        then message = message .. "from " .. caller_id_number end

freeswitch.consoleLog("WARNING", "message: " .. message .. "\n")

-- for reference:
        tmsi_PO = "59844103-aa0c-4bbe-abbe-4388d17473d4"

structure = {
	voicemail = {
--	        token = "9a776927-2ff3-47ac-a13a-edcc2dab936",
        	url = file_path,
		tmsi = tmsi,
        	message_id = uuid ,
        	duration = message_len , 
        	to_number    = user ,
        	sender  = caller_id_name,
		received_at = os.date("%Y-%m-%dT%H:%M:%S%Z", timestamp)
	} 
}
-- S3 target URL:
--  http://s3-ap-southeast-2.amazonaws.com/nspl.voicemail.dev/69c6c3c1-4779-4e97-909b-89e2d9f040bf/msg_3f1e13ea-1f38-11e7-beea-07264b712249.wav
-- http://base/bucket/tmsi/filename

s3_structure = {
	path = file_path,
        tmsi = tmsi
}
encoded = encodeparams(s3_structure)
-- @todo convert this to a post, might involve some tweaks of the flask server
upload_url = "http://" .. container_ip .. ":5080/send_to_s3?path=".. file_path .. "&tmsi=" .. tmsi
-- upload_url = "http://127.0.0.1:5080/send_to_s3?" .. encoded
freeswitch.consoleLog("info", "Uploading from " .. file_path  .. " via " .. upload_url .. "\n")

result = api:execute("curl", upload_url)

freeswitch.consoleLog("info"," Curl result :" .. result)
structure['voicemail']['url']=result
json_string = JSON:encode(structure)


freeswitch.consoleLog("WARNING", "Nearly there: \n" .. json_string .. "\n")
message = message .. " at " .. os.date("%a %H:%M", timestamp)
message = message .. " length " .. message_len .. " seconds"
message = message .. " to box " .. user .."\n"
message = message .. " File path " .. file_path .. "\n"
message = message .. " Folder " .. folder .. " UUID " .. uuid .. "\n"
message = message .. "."
-- Add JSON string escapes to the message
message = string.gsub(message, "([\\\"'])", "\\%1")

-- Prepare the data
data = '{ "to": "' .. (tmsi or "nil") .. '", "from": "' .. from .. '", "body": "' .. message .. '"}'
freeswitch.consoleLog("notice", "Final message:\n" .. message .. "\n")
freeswitch.consoleLog("notice", "Final data:\n" .. data .. "\n")

-- Select the target:
hlrPasiphae = "ba551fae-09a8-4d36-bdb9-c0209ac1000d"
hlrSinope =   "3592501b-11c8-4a73-8f26-e9c16b61e6dd"
hlrIo =       "fd93eba0-69bb-496d-83b5-0b657ea9d791"
if hlrGuid == hlrPasiphae
	then url = "https://pasiphae.norwoodsystems.com/api/voicemail.json"
elseif hlrGuid == hlrSinope
	then url = "https://sinope.norwoodsystems.com/api/voicemail.json"
elseif hlrGuid == hlrIo 
        then url = "https://io.norwoodsystems.com/api/v3/voicemail.json"
else
	-- use the venerable delegate via Jupiter
	-- url = "https://jupiter.norwoodsystems.com/servers/" .. hlrGuid .. "/api/voicemail.json"
	url = "https://io-optus.norwoodsystems.io/api/v3/voicemail.json"
end

-- Send it!
curlParamString =  url .. " content-type application/json timeout 5 post '" .. json_string .. "'"
freeswitch.consoleLog("notice", "Sending with command: \ncurl " .. curlParamString .. "\n")
-- api:execute("curl", curlParamString)
function JSON:assert(message)
end
retry = 1
repeat
      	curl_response_code = api:execute("curl", curlParamString)
        lua_code = JSON:decode(curl_response_code)
        freeswitch.consoleLog("notice", "Final response from IO: " .. curl_response_code .. "\n")
        retry = retry + 1
        freeswitch.msleep(10000)
until (lua_code == "ok" or retry > 3)
