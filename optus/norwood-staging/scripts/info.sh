#!/bin/bash
set -e

# Variables
AWS_PROFILE="Optus"
AWS_REGION="ap-southeast-2"
APP_NAME="mozart"  # Default app name

# Process options
while [[ $# -gt 0 ]]; do
  case $1 in
    app|db|redis|ecs|ec2|ecr|all|logs)
      COMMAND="$1"
      shift
      
      # APP_NAME is now mandatory for some commands
      if [[ "$COMMAND" == "app" || "$COMMAND" == "ecs" || "$COMMAND" == "ecr" || "$COMMAND" == "all" || "$COMMAND" == "logs" ]] && [ $# -gt 0 ]; then
        # Check if the next parameter is not a command
        if [[ "$1" != "app" && "$1" != "db" && "$1" != "redis" && "$1" != "ecs" && "$1" != "ec2" && "$1" != "ecr" && "$1" != "all" && "$1" != "logs" ]]; then
          APP_NAME="$1"
          shift
        elif [[ "$COMMAND" == "logs" && -z "$APP_NAME_PROVIDED_FOR_LOGS" ]]; then
            echo "Error: APP_NAME is required for the 'logs' command."
            echo "Usage: $0 logs APP_NAME"
            exit 1
        fi
      elif [[ "$COMMAND" == "logs" && -z "$APP_NAME" ]]; then
         echo "Error: APP_NAME is required for the 'logs' command."
         echo "Usage: $0 logs APP_NAME"
         echo "Alternatively, specify APP_NAME before the command: $0 APP_NAME logs"
         exit 1
      fi
      ;;
    *)
      # If first parameter, treat as APP_NAME
      if [ -z "$COMMAND" ]; then
        APP_NAME="$1"
        if [[ "$2" == "logs" ]]; then
            COMMAND="logs"
            APP_NAME_PROVIDED_FOR_LOGS=true
            shift 2
        else
            shift
        fi
      else
        echo "Unknown option: $1"
        echo "Usage: $0 [APP_NAME] {app|db|redis|ecs|ec2|ecr|all|logs}"
        echo "       $0 logs APP_NAME"
        echo "  APP_NAME - Application name (e.g., 'mozart', 'admin-dashboard', or 'mozart-worker')"
        exit 1
      fi
      ;;
  esac
done

# Set default command if not specified and APP_NAME was provided
if [ -z "$COMMAND" ] && [ -n "$APP_NAME" ] && [ "$APP_NAME_PROVIDED_FOR_LOGS" != true ]; then
    COMMAND="all"
fi

# If logs command was specified but app name is still default 'mozart' and not explicitly given for logs
if [[ "$COMMAND" == "logs" && "$APP_NAME" == "mozart" && "$APP_NAME_PROVIDED_FOR_LOGS" != true && "$1" != "mozart" ]]; then
    :
fi

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is required but not installed. Please install AWS CLI first."
    exit 1
fi

# Check if jq is installed (needed for EC2 info formatting)
if ! command -v jq &> /dev/null; then
    echo "jq is required for some commands but not installed. Please install jq first."
    exit 1
fi

# Check if the user is logged in to AWS
echo "Verifying AWS credentials..."
aws --profile $AWS_PROFILE sts get-caller-identity > /dev/null || {
    echo "AWS credentials not found or expired. Please run 'aws sso login --profile $AWS_PROFILE' first."
    exit 1
}

# Function to get application URL
get_app_url() {
    if [[ "$APP_NAME" == "admin-dashboard" ]]; then
        ALB_DNS=$(cd terraform && terraform output -raw admin_dashboard_alb_dns_name)
        if [ -n "$ALB_DNS" ]; then
            echo "Admin Dashboard URL: http://$ALB_DNS"
        else
            echo "Failed to get Admin Dashboard Load Balancer DNS name."
        fi
    elif [[ "$APP_NAME" == "mozart-worker" ]]; then
        echo "Mozart Worker does not have a direct application URL."
    else
        ALB_DNS=$(cd terraform && terraform output -raw alb_dns_name)
        if [ -n "$ALB_DNS" ]; then
            echo "Application URL: http://$ALB_DNS"
        else
            echo "Failed to get Load Balancer DNS name."
        fi
    fi
}

# Function to get database information
get_db_info() {
    echo "Database Information:"
    
    DB_ENDPOINT=$(cd terraform && terraform output -raw database_endpoint)
    if [ -n "$DB_ENDPOINT" ]; then
        echo "  Endpoint: $DB_ENDPOINT"
    else
        echo "  Endpoint: Not available"
    fi
    
    DB_NAME=$(cd terraform && terraform output -raw database_name)
    if [ -n "$DB_NAME" ]; then
        echo "  Database Name: $DB_NAME"
    else
        echo "  Database Name: Not available"
    fi
    
    DB_USER=$(cd terraform && terraform output -raw database_username)
    if [ -n "$DB_USER" ]; then
        echo "  Username: $DB_USER"
    else
        echo "  Username: Not available"
    fi
    
    DB_PASSWORD_ARN=$(cd terraform && terraform output -raw database_password_secret_arn)
    if [ -n "$DB_PASSWORD_ARN" ]; then
        echo "  Password Secret ARN: $DB_PASSWORD_ARN"
        echo "  Get password: aws --profile $AWS_PROFILE --region $AWS_REGION secretsmanager get-secret-value --secret-id $DB_PASSWORD_ARN --query SecretString --output text"
    else
        echo "  Password Secret ARN: Not available"
    fi
}

# Function to get Redis information
get_redis_info() {
    echo "Redis Information:"
    
    REDIS_ENDPOINT=$(cd terraform && terraform output -raw redis_primary_endpoint)
    if [ -n "$REDIS_ENDPOINT" ]; then
        echo "  Primary Endpoint: $REDIS_ENDPOINT"
    else
        echo "  Primary Endpoint: Not available"
    fi
    
    REDIS_READER_ENDPOINT=$(cd terraform && terraform output -raw redis_reader_endpoint)
    if [ -n "$REDIS_READER_ENDPOINT" ]; then
        echo "  Reader Endpoint: $REDIS_READER_ENDPOINT"
    else
        echo "  Reader Endpoint: Not available"
    fi
    
    REDIS_PORT=$(cd terraform && terraform output -raw redis_port)
    if [ -n "$REDIS_PORT" ]; then
        echo "  Port: $REDIS_PORT"
    else
        echo "  Port: Not available"
    fi
}

# Function to get ECS information
get_ecs_info() {
    if [[ "$APP_NAME" == "admin-dashboard" ]]; then
        echo "Admin Dashboard ECS Information:"
        
        ECS_CLUSTER=$(cd terraform && terraform output -raw ecs_cluster_id)
        if [ -n "$ECS_CLUSTER" ]; then
            echo "  Cluster: $ECS_CLUSTER"
        else
            echo "  Cluster: Not available"
        fi
        
        ECS_SERVICE=$(cd terraform && terraform output -raw admin_dashboard_ecs_service_name)
        if [ -n "$ECS_SERVICE" ]; then
            echo "  Service: $ECS_SERVICE"
        else
            echo "  Service: Not available"
        fi
    elif [[ "$APP_NAME" == "mozart-worker" ]]; then
        echo "Mozart Worker ECS Information:"
        
        ECS_CLUSTER=$(cd terraform && terraform output -raw ecs_cluster_id)
        if [ -n "$ECS_CLUSTER" ]; then
            echo "  Cluster: $ECS_CLUSTER"
        else
            echo "  Cluster: Not available"
        fi
        
        ECS_SERVICE=$(cd terraform && terraform output -raw mozart_worker_ecs_service_name)
        if [ -n "$ECS_SERVICE" ]; then
            echo "  Service: $ECS_SERVICE"
        else
            echo "  Service: Not available"
        fi
    else
        echo "ECS Information:"
        
        ECS_CLUSTER=$(cd terraform && terraform output -raw ecs_cluster_id)
        if [ -n "$ECS_CLUSTER" ]; then
            echo "  Cluster: $ECS_CLUSTER"
        else
            echo "  Cluster: Not available"
        fi
        
        ECS_SERVICE=$(cd terraform && terraform output -raw ecs_service_name)
        if [ -n "$ECS_SERVICE" ]; then
            echo "  Service: $ECS_SERVICE"
        else
            echo "  Service: Not available"
        fi
    fi
}

# Function to get ECR information
get_ecr_info() {
    if [[ "$APP_NAME" == "admin-dashboard" ]]; then
        echo "Admin Dashboard ECR Information:"
        
        ECR_REPO=$(cd terraform && terraform output -raw admin_dashboard_ecr_repository_url)
        if [ -n "$ECR_REPO" ]; then
            echo "  Repository URL: $ECR_REPO"
        else
            echo "  Repository URL: Not available"
        fi
        
        ECR_NAME=$(cd terraform && terraform output -raw admin_dashboard_ecr_repository_name)
        if [ -n "$ECR_NAME" ]; then
            echo "  Repository Name: $ECR_NAME"
        else
            echo "  Repository Name: Not available"
        fi
    elif [[ "$APP_NAME" == "mozart-worker" ]]; then
        echo "Mozart Worker ECR Information:"
        
        ECR_REPO=$(cd terraform && terraform output -raw mozart_worker_ecr_repository_url)
        if [ -n "$ECR_REPO" ]; then
            echo "  Repository URL: $ECR_REPO"
        else
            echo "  Repository URL: Not available"
        fi
        
        ECR_NAME=$(cd terraform && terraform output -raw ecr_repository_name)
        if [ -n "$ECR_NAME" ]; then
            echo "  Repository Name: $ECR_NAME"
        else
            echo "  Repository Name: Not available"
        fi
    else
        echo "ECR Information:"
        
        ECR_REPO=$(cd terraform && terraform output -raw ecr_repository_url)
        if [ -n "$ECR_REPO" ]; then
            echo "  Repository URL: $ECR_REPO"
        else
            echo "  Repository URL: Not available"
        fi
        
        ECR_NAME=$(cd terraform && terraform output -raw ecr_repository_name)
        if [ -n "$ECR_NAME" ]; then
            echo "  Repository Name: $ECR_NAME"
        else
            echo "  Repository Name: Not available"
        fi
    fi
}

# Function to get EC2 information from ECS cluster
get_ec2_info() {
    echo "EC2 Container Instances Information:"
    
    ECS_CLUSTER=$(cd terraform && terraform output -raw ecs_cluster_id)
    
    if [ -z "$ECS_CLUSTER" ]; then
        echo "  Cluster ID not available. Could not retrieve EC2 information."
        return 1
    fi
    
    # Extract the cluster name from the ARN if needed
    CLUSTER_NAME=$(echo "$ECS_CLUSTER" | sed -n 's/.*cluster\/\([^\/]*\)/\1/p')
    if [ -z "$CLUSTER_NAME" ]; then
        CLUSTER_NAME="$ECS_CLUSTER"  # If not an ARN, use as-is
    fi
    
    echo "  Cluster: $ECS_CLUSTER"
    
    # List container instances
    INSTANCE_ARNS=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs list-container-instances \
        --cluster "$CLUSTER_NAME" --query 'containerInstanceArns[*]' --output text)
    
    if [ -z "$INSTANCE_ARNS" ]; then
        echo "  No EC2 instances found in the cluster."
        return 0
    fi
    
    # Count the ARNs
    INSTANCE_COUNT=$(echo "$INSTANCE_ARNS" | wc -w)
    echo "  Number of instances: $INSTANCE_COUNT"
    echo ""
    
    # Describe all the container instances
    INSTANCES_INFO=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-container-instances \
        --cluster "$CLUSTER_NAME" --container-instances $INSTANCE_ARNS)
    
    # Process each instance using jq
    echo "$INSTANCES_INFO" | jq -r '.containerInstances[] | 
        "  Instance ID: " + .ec2InstanceId + 
        "\n    Status: " + .status + 
        "\n    Agent Connected: " + (.agentConnected | tostring) + 
        "\n    Instance Type: " + (.attributes[] | select(.name == "ecs.instance-type") | .value) + 
        "\n    CPU (Total): " + (.registeredResources[] | select(.name == "CPU") | .integerValue | tostring) + " units" + 
        "\n    CPU (Available): " + (.remainingResources[] | select(.name == "CPU") | .integerValue | tostring) + " units" + 
        "\n    Memory (Total): " + (.registeredResources[] | select(.name == "MEMORY") | .integerValue | tostring) + " MiB" + 
        "\n    Memory (Available): " + (.remainingResources[] | select(.name == "MEMORY") | .integerValue | tostring) + " MiB" + 
        "\n    Running Tasks: " + (.runningTasksCount | tostring) + 
        "\n    Pending Tasks: " + (.pendingTasksCount | tostring) + 
        "\n"'
}

# Function to get all information
get_all_info() {
    get_app_url
    echo ""
    get_ecs_info
    echo ""
    get_ec2_info
    echo ""
    get_ecr_info
    
    # Only show DB and Redis info for main app
    if [[ "$APP_NAME" != "admin-dashboard" ]]; then
        echo ""
        get_db_info
        echo ""
        get_redis_info
    fi
}

# Function to tail ECS service logs
tail_logs() {
    echo "Attempting to tail logs for $APP_NAME..."

    local LOG_GROUP_NAME
    local TERRAFORM_APP_NAME_FOR_LOG_GROUP # This will be the app_name variable used in terraform module, typically 'mozart'

    # Determine the root app name used in Terraform module instantiation for constructing log group names.
    # This assumes that for mozart, mozart-worker, and admin-dashboard, the log group name in Terraform
    # is based on the main application name (e.g., 'mozart') when the ecs module is called.
    # If admin-dashboard has its own var.app_name in its module call, this logic might need adjustment for it.
    TERRAFORM_APP_NAME_FOR_LOG_GROUP="mozart" # Default, assuming the module var.app_name is 'mozart'

    if [[ "$APP_NAME" == "admin-dashboard" ]]; then
        # If admin-dashboard has a different root app name for its log group in TF, adjust TERRAFORM_APP_NAME_FOR_LOG_GROUP here
        # For now, assuming it might also be under a general 'admin-dashboard' named log group or a specific one.
        # The script currently tries to use /ecs/admin_dashboard_task_definition_family. Let's make it more direct.
        LOG_GROUP_NAME="/ecs/admin-dashboard" # Adjust if your TF uses a different pattern like /ecs/admin-dashboard-task-family or a specific var
        # To be more robust, ideally this would also come from a Terraform output if the pattern is not fixed.
    elif [[ "$APP_NAME" == "mozart-worker" ]]; then
        LOG_GROUP_NAME="/ecs/${TERRAFORM_APP_NAME_FOR_LOG_GROUP}-mozart-worker"
    elif [[ "$APP_NAME" == "mozart" ]]; then
        LOG_GROUP_NAME="/ecs/${TERRAFORM_APP_NAME_FOR_LOG_GROUP}"
    else
        echo "Error: Logs for '$APP_NAME' are not configured in the script. Unknown application."
        echo "Please use 'mozart', 'admin-dashboard', or 'mozart-worker'."
        return 1
    fi

    echo "Tailing logs for log group: $LOG_GROUP_NAME"
    echo "To stop, press Ctrl+C."

    cd "$(dirname "$0")/.."

    aws logs tail --profile "$AWS_PROFILE" --region "$AWS_REGION" "$LOG_GROUP_NAME" --follow || {
        echo "Error: Failed to tail logs. Possible reasons:"
        echo "  - Log group '$LOG_GROUP_NAME' does not exist (Expected based on Terraform config)."
        echo "  - IAM permissions missing for CloudWatch Logs."
        echo "  - AWS CLI or profile/region issue."
        echo "  Checked Terraform for '$APP_NAME', expected log group: '$LOG_GROUP_NAME'."
    }
}

# Main logic
echo "Executing command: $COMMAND for $APP_NAME"
case "$COMMAND" in
    app)
        get_app_url
        ;;
    db)
        get_db_info
        ;;
    redis)
        get_redis_info
        ;;
    ecs)
        get_ecs_info
        ;;
    ec2)
        get_ec2_info
        ;;
    ecr)
        get_ecr_info
        ;;
    all)
        get_all_info
        ;;
    logs)
        tail_logs
        ;;
    *)
        echo "Invalid command: $COMMAND"
        echo "Usage: $0 {app|db|redis|ecs|ec2|ecr|all|logs} [APP_NAME]"
        echo "  APP_NAME - Application name (e.g., 'mozart', 'admin-dashboard', or 'mozart-worker')"
        echo "  app   - Get application URL"
        echo "  db    - Get database information"
        echo "  redis - Get Redis information"
        echo "  ecs   - Get ECS information"
        echo "  ec2   - Get EC2 container instances information"
        echo "  ecr   - Get ECR information"
        echo "  all   - Get all information (default)"
        echo "  logs  - Tail ECS service logs"
        exit 1
        ;;
esac

echo "Info script completed!"