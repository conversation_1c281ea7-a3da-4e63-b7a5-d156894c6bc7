#!/bin/bash
set -e

# Variables
AWS_PROFILE="Optus"
AWS_REGION="ap-southeast-2"
IMAGE_TAG="latest"
DOCKER_IMAGE_PATH="./path/to/your/dockerfile" # Update this to the path to your Dockerfile
AUTO_APPROVE=""
NO_WAIT=""

# Check if APP_NAME is provided as the first argument
if [ $# -eq 0 ]; then
    echo "Error: APP_NAME is required as the first argument."
    echo "Usage: $0 APP_NAME --image-path <path> [--tag <tag>] [--auto-approve] [--no-wait]"
    exit 1
fi

# Set APP_NAME from first argument
APP_NAME="$1"
shift

# Validate APP_NAME
if [[ "$APP_NAME" != "mozart" && "$APP_NAME" != "admin-dashboard" && "$APP_NAME" != "mozart-worker" ]]; then
    echo "Error: APP_NAME must be 'mozart', 'admin-dashboard', or 'mozart-worker'."
    echo "Usage: $0 APP_NAME --image-path <path> [--tag <tag>] [--auto-approve] [--no-wait]"
    exit 1
fi

# Process remaining arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --image-path)
      DOCKER_IMAGE_PATH="$2"
      shift 2
      ;;
    --tag)
      IMAGE_TAG="$2"
      shift 2
      ;;
    --auto-approve)
      AUTO_APPROVE="--auto-approve"
      shift
      ;;
    --no-wait)
      NO_WAIT="--no-wait"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 APP_NAME --image-path <path> [--tag <tag>] [--auto-approve] [--no-wait]"
      exit 1
      ;;
  esac
done

# Check if --image-path is provided
# For mozart-worker, image path is relevant if we are building the 'mozart' image which it uses.
# If deploying 'mozart-worker' standalone, this might imply just an ECS update with existing image.
# However, the script flow ties deployment to an image build.
if [[ "$APP_NAME" != "mozart-worker" ]] && ([ -z "$DOCKER_IMAGE_PATH" ] || [ "$DOCKER_IMAGE_PATH" == "./path/to/your/dockerfile" ]); then
    echo "Error: --image-path is required for $APP_NAME."
    echo "Usage: $0 APP_NAME --image-path <path> [--tag <tag>] [--auto-approve] [--no-wait]"
    exit 1
elif [[ "$APP_NAME" == "mozart-worker" ]] && ([ -z "$DOCKER_IMAGE_PATH" ] || [ "$DOCKER_IMAGE_PATH" == "./path/to/your/dockerfile" ]); then
    echo "Warning: --image-path not specified for mozart-worker. Assuming image is already built and pushed if an update is intended."
    # Allow proceeding without image-path for worker if user intends to only update ECS service with an existing image tag
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker is required but not installed. Please install Docker first."
    exit 1
fi

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is required but not installed. Please install AWS CLI first."
    exit 1
fi

# Check if Terraform is installed
if ! command -v terraform &> /dev/null; then
    echo "Terraform is required but not installed. Please install Terraform first."
    exit 1
fi

# Check if the user is logged in to AWS
echo "Verifying AWS credentials..."
aws --profile $AWS_PROFILE sts get-caller-identity > /dev/null || {
    echo "AWS credentials not found or expired. Please run 'aws sso login --profile $AWS_PROFILE' first."
    exit 1
}

# Initialize Terraform
echo "Initializing Terraform..."
(cd terraform && terraform init)

# Apply Terraform to create infrastructure with appropriate variables
echo "Applying Terraform changes (if any)..."
# For mozart-worker, terraform apply ensures outputs are up-to-date.
# The image_tag for the worker's image is var.image_tag (same as mozart app).
if [[ "$APP_NAME" == "admin-dashboard" ]]; then
    (cd terraform && terraform apply $AUTO_APPROVE -var="admin_dashboard_image_tag=$IMAGE_TAG")
elif [[ "$APP_NAME" == "mozart" || "$APP_NAME" == "mozart-worker" ]]; then
    # Both mozart and mozart-worker's image source is tied to var.image_tag
    (cd terraform && terraform apply $AUTO_APPROVE -var="image_tag=$IMAGE_TAG")
fi

# Build and push the Docker image
# Only build/push if not deploying just the worker (as it shares image with mozart)
# Or if deploying worker and image path IS provided (implies intent to build mozart image)
if [[ "$APP_NAME" != "mozart-worker" ]] || ( [[ "$APP_NAME" == "mozart-worker" ]] && [[ "$DOCKER_IMAGE_PATH" != "./path/to/your/dockerfile" ]] ); then
    echo "Building and pushing Docker image..."
    ADMIN_DASHBOARD_FLAG=""
    # Determine ECR script target: admin-dashboard or mozart (worker uses mozart's ECR)
    ECR_TARGET_APP_NAME="$APP_NAME"
    if [[ "$APP_NAME" == "mozart-worker" ]]; then
        ECR_TARGET_APP_NAME="mozart" # Worker uses mozart's image
    fi

    if [[ "$ECR_TARGET_APP_NAME" == "admin-dashboard" ]]; then
        ADMIN_DASHBOARD_FLAG="--admin-dashboard"
    fi
    
    # If DOCKER_IMAGE_PATH is the default/empty for mozart-worker, skip ecr.sh call.
    if ! ( [[ "$APP_NAME" == "mozart-worker" ]] && [[ "$DOCKER_IMAGE_PATH" == "./path/to/your/dockerfile" ]] ); then
      ./scripts/ecr.sh --image-path "$DOCKER_IMAGE_PATH" --tag "$IMAGE_TAG" $ADMIN_DASHBOARD_FLAG
    fi
else
    echo "Skipping Docker image build and push for mozart-worker (uses mozart image)."
fi

# Update ECS service
echo "Updating ECS service(s)..."
if [[ "$APP_NAME" == "mozart" ]]; then
    ./scripts/ecs.sh update "mozart" --tag "$IMAGE_TAG" $NO_WAIT
    echo "Updating mozart-worker-service as well..."
    ./scripts/ecs.sh update "mozart-worker" --tag "$IMAGE_TAG" $NO_WAIT
elif [[ "$APP_NAME" == "admin-dashboard" ]]; then
    ./scripts/ecs.sh update "admin-dashboard" --tag "$IMAGE_TAG" $NO_WAIT
elif [[ "$APP_NAME" == "mozart-worker" ]]; then
    ./scripts/ecs.sh update "mozart-worker" --tag "$IMAGE_TAG" $NO_WAIT
fi

# Display deployment information
echo "Displaying deployment information..."
if [[ "$APP_NAME" == "admin-dashboard" ]]; then
    # Get the load balancer DNS name for admin dashboard
    ALB_DNS=$(cd terraform && terraform output -raw admin_dashboard_alb_dns_name)
    echo "Deployment of admin dashboard completed successfully!"
    echo "Your admin dashboard is accessible at: http://$ALB_DNS"
elif [[ "$APP_NAME" == "mozart" ]]; then
    # Get the load balancer DNS name for main app
    ALB_DNS=$(cd terraform && terraform output -raw alb_dns_name)
    echo "Deployment of mozart app completed successfully!"
    echo "Your application is accessible at: http://$ALB_DNS"
    
    # Print database and redis connection information
    DB_ENDPOINT=$(cd terraform && terraform output -raw database_endpoint)
    DB_NAME=$(cd terraform && terraform output -raw database_name)
    DB_USER=$(cd terraform && terraform output -raw database_username)
    DB_PASSWORD_ARN=$(cd terraform && terraform output -raw database_password_secret_arn)
    REDIS_ENDPOINT=$(cd terraform && terraform output -raw redis_primary_endpoint)
    REDIS_PORT=$(cd terraform && terraform output -raw redis_port)
    
    echo ""
    echo "Database Information:"
    echo "Endpoint: $DB_ENDPOINT"
    echo "Database Name: $DB_NAME"
    echo "Username: $DB_USER"
    echo "Password Secret ARN: $DB_PASSWORD_ARN"
    echo ""
    echo "Redis Information:"
    echo "Endpoint: $REDIS_ENDPOINT"
    echo "Port: $REDIS_PORT"
    echo ""
    echo "To retrieve the database password, run:"
    echo "aws --profile $AWS_PROFILE --region $AWS_REGION secretsmanager get-secret-value --secret-id $DB_PASSWORD_ARN --query SecretString --output text"
elif [[ "$APP_NAME" == "mozart-worker" ]]; then
    echo "Deployment of mozart-worker completed successfully!"
    # No ALB for worker
fi