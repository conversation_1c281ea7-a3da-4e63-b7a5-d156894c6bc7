#!/bin/bash
set -e

# Variables
AWS_PROFILE="Optus"
AWS_REGION="ap-southeast-2"
APP_NAME="mozart"
IMAGE_TAG="latest"
DOCKER_IMAGE_PATH=""
DOCKERFILE=""

# Process arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --image-path)
      DOCKER_IMAGE_PATH="$2"
      shift 2
      ;;
    --tag)
      IMAGE_TAG="$2"
      shift 2
      ;;
    --admin-dashboard)
      APP_NAME="admin-dashboard"
      shift 1
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Check if Docker image path is provided
if [ -z "$DOCKER_IMAGE_PATH" ]; then
    echo "Please provide Docker image path with --image-path option"
    exit 1
fi

# Determine if path is a directory or a file
if [ -f "$DOCKER_IMAGE_PATH" ]; then
    # Path is a file (Dockerfile)
    DOCKERFILE="$DOCKER_IMAGE_PATH"
    DOCKER_IMAGE_PATH=$(dirname "$DOCKERFILE")
elif [ -d "$DOCKER_IMAGE_PATH" ]; then
    # Path is a directory
    DOCKERFILE="$DOCKER_IMAGE_PATH/Dockerfile"
    # For admin-dashboard, use the regular Dockerfile
    if [[ "$APP_NAME" == "admin-dashboard" ]]; then
        DOCKERFILE="$DOCKER_IMAGE_PATH/Dockerfile"
    fi
else
    echo "Error: The specified path '$DOCKER_IMAGE_PATH' does not exist."
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker is required but not installed. Please install Docker first."
    exit 1
fi

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is required but not installed. Please install AWS CLI first."
    exit 1
fi

# Check if the user is logged in to AWS
echo "Verifying AWS credentials..."
aws --profile $AWS_PROFILE sts get-caller-identity > /dev/null || {
    echo "AWS credentials not found or expired. Please run 'aws sso login --profile $AWS_PROFILE' first."
    exit 1
}

# Get appropriate ECR repository URL from Terraform output
if [[ "$APP_NAME" == "admin-dashboard" ]]; then
    ECR_REPO=$(cd terraform && terraform output -raw admin_dashboard_ecr_repository_url)
else
    ECR_REPO=$(cd terraform && terraform output -raw ecr_repository_url)
fi

if [ -z "$ECR_REPO" ]; then
    echo "Failed to get ECR repository URL from Terraform output."
    exit 1
fi

# Function to log in to ECR
ecr_login() {
    echo "Logging in to ECR..."
    aws --profile $AWS_PROFILE --region $AWS_REGION ecr get-login-password | docker login --username AWS --password-stdin $ECR_REPO
}

# Function to build Docker image
build_image() {
    echo "Building Docker image: $APP_NAME:$IMAGE_TAG using $DOCKERFILE..."
    # Set platform for Mac users (AMD64)
    cd $DOCKER_IMAGE_PATH
    export DOCKER_DEFAULT_PLATFORM=linux/amd64
    echo "Setting Docker platform to linux/amd64"
    
    if [ -f "$DOCKERFILE" ]; then
        docker build -f "$DOCKERFILE" -t $APP_NAME:$IMAGE_TAG $DOCKER_IMAGE_PATH
    else
        echo "Error: Dockerfile not found at $DOCKERFILE"
        exit 1
    fi
}

# Function to tag Docker image
tag_image() {
    echo "Tagging Docker image: $ECR_REPO:$IMAGE_TAG..."
    docker tag $APP_NAME:$IMAGE_TAG $ECR_REPO:$IMAGE_TAG
}

# Function to push Docker image to ECR
push_image() {
    echo "Pushing Docker image to ECR: $ECR_REPO:$IMAGE_TAG..."
    docker push $ECR_REPO:$IMAGE_TAG
}

# Main function
main() {
    ecr_login
    build_image
    tag_image
    push_image
    echo "Docker image successfully built and pushed to ECR: $ECR_REPO:$IMAGE_TAG"
}

main