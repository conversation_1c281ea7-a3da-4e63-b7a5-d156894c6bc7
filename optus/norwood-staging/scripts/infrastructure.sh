#!/bin/bash
set -e

# Variables
AWS_PROFILE="Optus"
AWS_REGION="ap-southeast-2"
APP_NAME="mozart"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is required but not installed. Please install AWS CLI first."
    exit 1
fi

# Check if Terraform is installed
if ! command -v terraform &> /dev/null; then
    echo "Terraform is required but not installed. Please install Terraform first."
    exit 1
fi

# Check if the user is logged in to AWS
echo "Verifying AWS credentials..."
aws --profile $AWS_PROFILE sts get-caller-identity > /dev/null || {
    echo "AWS credentials not found or expired. Please run 'aws sso login --profile $AWS_PROFILE' first."
    exit 1
}

# Function to init Terraform
init_terraform() {
    echo "Initializing Terraform..."
    (cd terraform && terraform init)
}

# Function to plan Terraform changes
plan_terraform() {
    echo "Planning Terraform changes..."
    (cd terraform && terraform plan)
}

# Function to apply Terraform changes
apply_terraform() {
    local auto_approve=""
    if [ "$1" == "--auto-approve" ]; then
        auto_approve="-auto-approve"
    fi
    
    echo "Applying Terraform changes..."
    (cd terraform && terraform apply $auto_approve)
}

# Function to destroy Terraform resources
destroy_terraform() {
    local auto_approve=""
    if [ "$1" == "--auto-approve" ]; then
        auto_approve="-auto-approve"
    fi
    
    echo "Destroying Terraform resources..."
    (cd terraform && terraform destroy $auto_approve)
}

# Function to apply specific module
apply_module() {
    if [ -z "$1" ]; then
        echo "Module name is required!"
        exit 1
    fi
    
    local auto_approve=""
    if [ "$2" == "--auto-approve" ]; then
        auto_approve="-auto-approve"
    fi
    
    echo "Applying Terraform module: $1..."
    (cd terraform && terraform apply $auto_approve -target=module.$1)
}

# Main logic
case "$1" in
    init)
        init_terraform
        ;;
    plan)
        init_terraform
        plan_terraform
        ;;
    apply)
        init_terraform
        apply_terraform "$2"
        ;;
    destroy)
        init_terraform
        destroy_terraform "$2"
        ;;
    apply-module)
        init_terraform
        apply_module "$2" "$3"
        ;;
    *)
        echo "Usage: $0 {init|plan|apply|destroy|apply-module MODULE_NAME} [--auto-approve]"
        exit 1
        ;;
esac

echo "Infrastructure operation completed!" 