#!/bin/bash
set -e
set -x  # Enable bash debug mode

# Variables
AWS_PROFILE="Optus"
AWS_REGION="ap-southeast-2"
WAIT_FOR_STABILITY=true
IMAGE_TAG="latest"
COMMAND=""
APP_NAME=""

# Process command argument first
if [[ "$1" == "update" || "$1" == "status" || "$1" == "events" || "$1" == "ssh" ]]; then
    COMMAND="$1"
    shift
    
    # APP_NAME is now mandatory
    if [ $# -eq 0 ]; then
        echo "Error: APP_NAME is required."
        echo "Usage: $0 {update|status|events|ssh} APP_NAME [--tag <image_tag>] [--no-wait]"
        exit 1
    fi
    
    APP_NAME="$1"
    shift
else
    echo "Usage: $0 {update|status|events|ssh} APP_NAME [--tag <image_tag>] [--no-wait]"
    echo "  update - Deploy image with specified tag (defaults to 'latest') and update the service"
    echo "  status - Check the current service status"
    echo "  events - Display recent service events"
    echo "  ssh    - Start an SSM session to the first available EC2 instance in the ECS cluster"
    echo "  APP_NAME - Application name (e.g., 'mozart' or 'admin-dashboard')"
    echo "  --tag  - Specify the image tag to deploy (default: latest)"
    echo "  --no-wait - Don't wait for service stability (only with update)"
    exit 1
fi

# Process optional flags
while [[ $# -gt 0 ]]; do
  case $1 in
    --no-wait)
      WAIT_FOR_STABILITY=false
      shift
      ;;
    --tag)
      # Ensure --tag is only used with 'update' command
      if [[ "$COMMAND" != "update" ]]; then
          echo "Error: --tag option is only valid with the 'update' command."
          exit 1
      fi
      IMAGE_TAG="$2"
      if [ -z "$IMAGE_TAG" ]; then
          echo "Error: --tag requires an argument."
          exit 1
      fi
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      # Print usage again for unknown options
      echo "Usage: $0 {update|status|events|ssh} APP_NAME [--tag <image_tag>] [--no-wait]"
      exit 1
      ;;
  esac
done

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is required but not installed. Please install AWS CLI first."
    exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo "jq is required but not installed. Please install jq first."
    exit 1
fi

# Check if the user is logged in to AWS
echo "Verifying AWS credentials..."
aws --profile $AWS_PROFILE sts get-caller-identity > /dev/null || {
    echo "AWS credentials not found or expired. Please run 'aws sso login --profile $AWS_PROFILE' first."
    exit 1
}

# Get ECS cluster and service names from Terraform output
cd "$(dirname "$0")/.." # Move to the project root directory
ECS_CLUSTER=$(cd terraform && terraform output -raw ecs_cluster_id)
if [ -z "$ECS_CLUSTER" ]; then
    echo "Failed to get ECS cluster ID from Terraform output."
    exit 1
fi

# Get the appropriate service name and ECR repo based on the target service
if [[ "$APP_NAME" == "admin-dashboard" ]]; then
    echo "[DEBUG] Getting admin-dashboard specific values..."
    ECS_SERVICE=$(cd terraform && terraform output -raw admin_dashboard_ecs_service_name)
    ECR_REPO=$(cd terraform && terraform output -raw admin_dashboard_ecr_repository_url)
    
    # Both admin-dashboard and mozart now use the same cluster
    ECS_CLUSTER=$(cd terraform && terraform output -raw ecs_cluster_id)
    
    echo "[DEBUG] ECS_SERVICE=$ECS_SERVICE"
    echo "[DEBUG] ECR_REPO=$ECR_REPO"
    echo "[DEBUG] ECS_CLUSTER=$ECS_CLUSTER"
elif [[ "$APP_NAME" == "mozart-worker" ]]; then
    echo "[DEBUG] Getting mozart-worker specific values..."
    ECS_SERVICE=$(cd terraform && terraform output -raw mozart_worker_ecs_service_name)
    ECR_REPO=$(cd terraform && terraform output -raw mozart_worker_ecr_repository_url) # Assumes this output exists
    ECS_CLUSTER=$(cd terraform && terraform output -raw ecs_cluster_id) # Assumes same cluster as others
    
    echo "[DEBUG] ECS_SERVICE (mozart-worker)=$ECS_SERVICE"
    echo "[DEBUG] ECR_REPO (mozart-worker)=$ECR_REPO"
    echo "[DEBUG] ECS_CLUSTER (mozart-worker)=$ECS_CLUSTER"
elif [[ "$APP_NAME" == "imap-server" ]]; then
    echo "[DEBUG] Getting imap-server specific values..."
    ECS_SERVICE=$(cd terraform && terraform output -raw imap_server_ecs_service_name) # Assumes this output exists
    ECR_REPO=$(cd terraform && terraform output -raw ecr_repository_url)    # Assumes this output exists (likely same as mozart's)
    ECS_CLUSTER=$(cd terraform && terraform output -raw ecs_cluster_id)               # Assumes same cluster

    echo "[DEBUG] ECS_SERVICE (imap-server)=$ECS_SERVICE"
    echo "[DEBUG] ECR_REPO (imap-server)=$ECR_REPO"
    echo "[DEBUG] ECS_CLUSTER (imap-server)=$ECS_CLUSTER"
else
    ECS_SERVICE=$(cd terraform && terraform output -raw ecs_service_name)
    ECR_REPO=$(cd terraform && terraform output -raw ecr_repository_url)
fi

if [ -z "$ECS_SERVICE" ]; then
    echo "Failed to get ECS service name from Terraform output."
    exit 1
fi

if [ -z "$ECR_REPO" ]; then
    echo "Failed to get ECR repository URL from Terraform output."
    exit 1
fi

# Function to update ECS service
update_service() {
    echo "Updating ECS service: $ECS_SERVICE in cluster: $ECS_CLUSTER..."
    
    local image_to_use

    if [[ "$APP_NAME" == "mozart-worker" ]]; then
        echo "[INFO] 'mozart-worker' will be updated to sync its image with the 'mozart' service."
        # If a specific tag was provided for mozart-worker with the update command, it's ignored by this sync logic.
        if [[ "$IMAGE_TAG" != "latest" && "$COMMAND" == "update" ]]; then
            echo "[DEBUG] Note: The --tag '$IMAGE_TAG' specified for 'mozart-worker' is ignored due to its sync configuration with the 'mozart' service."
        fi

        local MOZART_SERVICE_NAME_TF_KEY="ecs_service_name" # Terraform key for Mozart's service name
        local MOZART_SERVICE_NAME=$(cd terraform && terraform output -raw $MOZART_SERVICE_NAME_TF_KEY)
        
        if [ -z "$MOZART_SERVICE_NAME" ]; then
            echo "Error: Failed to get 'mozart' service name ($MOZART_SERVICE_NAME_TF_KEY) from Terraform output."
            exit 1
        fi

        echo "[DEBUG] Fetching current image for 'mozart' service ($MOZART_SERVICE_NAME) in cluster $ECS_CLUSTER..."
        local MOZART_CURRENT_TASK_DEF_ARN=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-services \
            --cluster $ECS_CLUSTER \
            --services "$MOZART_SERVICE_NAME" \
            --query 'services[0].taskDefinition' \
            --output text)

        if [[ "$MOZART_CURRENT_TASK_DEF_ARN" == "None" || -z "$MOZART_CURRENT_TASK_DEF_ARN" ]]; then
            echo "[DEBUG] No active task definition for 'mozart' service ($MOZART_SERVICE_NAME), looking for latest task definition with family prefix 'mozart-task'..."
            local MOZART_TASK_DEF_FAMILY="mozart-task" # Assuming 'mozart' app name maps to 'mozart-task' family
            MOZART_CURRENT_TASK_DEF_ARN=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs list-task-definitions \
                --family-prefix "$MOZART_TASK_DEF_FAMILY" \
                --sort DESC \
                --max-items 1 \
                --query 'taskDefinitionArns[0]' \
                --output text)
        fi

        if [[ "$MOZART_CURRENT_TASK_DEF_ARN" == "None" || -z "$MOZART_CURRENT_TASK_DEF_ARN" ]]; then
            echo "Error: Could not find a valid task definition for 'mozart' service ($MOZART_SERVICE_NAME) to source the image."
            echo "Please ensure the 'mozart' service is deployed and has a running task definition."
            exit 1
        fi

        echo "[DEBUG] Getting 'mozart' task definition details from ARN: $MOZART_CURRENT_TASK_DEF_ARN"
        local MOZART_TASK_DEFINITION_DETAILS=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-task-definition \
            --task-definition "$MOZART_CURRENT_TASK_DEF_ARN" \
            --query 'taskDefinition')

        image_to_use=$(echo "$MOZART_TASK_DEFINITION_DETAILS" | jq -r '.containerDefinitions[0].image')
        
        if [[ "$image_to_use" == "null" || -z "$image_to_use" ]]; then
            echo "Error: Could not extract image from 'mozart' service's task definition."
            echo "Task Definition Details: $MOZART_TASK_DEFINITION_DETAILS"
            exit 1
        fi
        echo "[INFO] Using image from 'mozart' service: $image_to_use for $APP_NAME"
    elif [[ "$APP_NAME" == "imap-server" ]]; then
        echo "[INFO] 'imap-server' will be updated to sync its image with the 'mozart' service."
        local MOZART_SERVICE_NAME_TF_KEY="ecs_service_name"
        local MOZART_SERVICE_NAME=$(cd terraform && terraform output -raw $MOZART_SERVICE_NAME_TF_KEY)
        if [ -z "$MOZART_SERVICE_NAME" ]; then echo "Error: Failed to get 'mozart' service name ($MOZART_SERVICE_NAME_TF_KEY)"; exit 1; fi
        echo "[DEBUG] Fetching current image for 'mozart' service ($MOZART_SERVICE_NAME) in cluster $ECS_CLUSTER..."
        local MOZART_CURRENT_TASK_DEF_ARN=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-services --cluster $ECS_CLUSTER --services "$MOZART_SERVICE_NAME" --query 'services[0].taskDefinition' --output text)
        if [[ "$MOZART_CURRENT_TASK_DEF_ARN" == "None" || -z "$MOZART_CURRENT_TASK_DEF_ARN" ]]; then
            local MOZART_TASK_DEF_FAMILY="mozart-task"
            MOZART_CURRENT_TASK_DEF_ARN=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs list-task-definitions --family-prefix "$MOZART_TASK_DEF_FAMILY" --sort DESC --max-items 1 --query 'taskDefinitionArns[0]' --output text)
        fi
        if [[ "$MOZART_CURRENT_TASK_DEF_ARN" == "None" || -z "$MOZART_CURRENT_TASK_DEF_ARN" ]]; then echo "Error: Could not find valid task definition for 'mozart' service ($MOZART_SERVICE_NAME)."; exit 1; fi
        echo "[DEBUG] Getting 'mozart' task definition details from ARN: $MOZART_CURRENT_TASK_DEF_ARN"
        local MOZART_TASK_DEFINITION_DETAILS=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-task-definition --task-definition "$MOZART_CURRENT_TASK_DEF_ARN" --query 'taskDefinition')
        image_to_use=$(echo "$MOZART_TASK_DEFINITION_DETAILS" | jq -r '.containerDefinitions[0].image')
        if [[ "$image_to_use" == "null" || -z "$image_to_use" ]]; then echo "Error: Could not extract image from 'mozart' service task definition."; exit 1; fi
        echo "[INFO] Using image from 'mozart' service: $image_to_use for $APP_NAME"
    else
        # Standard behavior: use ECR_REPO and IMAGE_TAG for the current APP_NAME
        echo "[INFO] Using image tag: $IMAGE_TAG for ECR repo: $ECR_REPO (App: $APP_NAME)"
        image_to_use="$ECR_REPO:$IMAGE_TAG"
    fi
    
    echo "[DEBUG] Checking service exists..."
    # Check if service exists
    SERVICE_EXISTS=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-services \
        --cluster $ECS_CLUSTER \
        --services $ECS_SERVICE \
        --query 'services[0].status' \
        --output text)
    
    echo "[DEBUG] Service status: $SERVICE_EXISTS"
    
    if [[ "$SERVICE_EXISTS" != "ACTIVE" ]]; then
        echo "Error: Service $ECS_SERVICE does not exist or is not active in cluster $ECS_CLUSTER"
        exit 1
    fi
    
    # Get the current task definition ARN
    echo "[DEBUG] Getting current task definition ARN..."
    TASK_DEF_ARN=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-services \
        --cluster $ECS_CLUSTER \
        --services $ECS_SERVICE \
        --query 'services[0].taskDefinition' \
        --output text)
    
    echo "Task definition ARN: $TASK_DEF_ARN"
    
    # If no task definition found, try to get the latest one with family prefix
    if [[ "$TASK_DEF_ARN" == "None" || -z "$TASK_DEF_ARN" ]]; then
        echo "[DEBUG] No task definition found in service, looking for latest task definition with family prefix..."
        TASK_DEF_FAMILY="${APP_NAME}-task"
        echo "[DEBUG] Trying task definition family: $TASK_DEF_FAMILY"
        
        TASK_DEF_ARN=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs list-task-definitions \
            --family-prefix $TASK_DEF_FAMILY \
            --sort DESC \
            --max-items 1 \
            --query 'taskDefinitionArns[0]' \
            --output text)
            
        echo "[DEBUG] Found task definition from family: $TASK_DEF_ARN"
    fi
    
    if [[ "$TASK_DEF_ARN" == "None" || -z "$TASK_DEF_ARN" ]]; then
        echo "Error: Could not find a valid task definition for $APP_NAME"
        echo "You need to deploy a valid task definition first using the deploy.sh script"
        exit 1
    fi
    
    # Get the current task definition
    echo "[DEBUG] Getting task definition details from ARN: $TASK_DEF_ARN for $APP_NAME"
    TASK_DEFINITION=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-task-definition \
        --task-definition "$TASK_DEF_ARN" \
        --query 'taskDefinition')
    
    echo "[DEBUG] Task definition for $APP_NAME retrieved successfully"
    
    # Create a new task definition with the updated image tag
    echo "[DEBUG] Creating new task definition for $APP_NAME with image: $image_to_use"

    if [[ "$APP_NAME" == "mozart-worker" ]]; then
        # For mozart-worker, override the command
        WORKER_COMMAND_JSON='["--skip-build","App","queues"]' # Note: JSON array as a string
        echo "[INFO] Overriding command for $APP_NAME to: $WORKER_COMMAND_JSON"
        NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq \
            --arg IMAGE "$image_to_use" \
            --argjson CMD "$WORKER_COMMAND_JSON" \
            '.containerDefinitions[0].image = $IMAGE | .containerDefinitions[0].command = $CMD')
    elif [[ "$APP_NAME" == "imap-server" ]]; then
        # For imap-server, override the command
        IMAP_COMMAND_JSON='["--skip-build", "App", "start-imap-server", "--host", "0.0.0.0", "--port", "8080"]'
        echo "[INFO] Overriding command for $APP_NAME to: $IMAP_COMMAND_JSON"
        NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq \
            --arg IMAGE "$image_to_use" \
            --argjson CMD "$IMAP_COMMAND_JSON" \
            '.containerDefinitions[0].image = $IMAGE | .containerDefinitions[0].command = $CMD')
    else
        # For other services, just update the image
        NEW_TASK_DEFINITION=$(echo "$TASK_DEFINITION" | jq \
            --arg IMAGE "$image_to_use" \
            '.containerDefinitions[0].image = $IMAGE')
    fi
    
    # Register the new task definition
    echo "[DEBUG] Registering new task definition"
    NEW_TASK_DEF_ARN=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs register-task-definition \
        --family $(echo $TASK_DEFINITION | jq -r '.family') \
        --execution-role-arn $(echo $TASK_DEFINITION | jq -r '.executionRoleArn') \
        --task-role-arn $(echo $TASK_DEFINITION | jq -r '.taskRoleArn') \
        --network-mode $(echo $TASK_DEFINITION | jq -r '.networkMode') \
        --container-definitions "$(echo $NEW_TASK_DEFINITION | jq '.containerDefinitions')" \
        --cpu $(echo $TASK_DEFINITION | jq -r '.cpu') \
        --memory $(echo $TASK_DEFINITION | jq -r '.memory') \
        --requires-compatibilities $(echo $TASK_DEFINITION | jq -r '.requiresCompatibilities[]') \
        --query 'taskDefinition.taskDefinitionArn' \
        --output text)
    
    echo "[DEBUG] New task definition ARN: $NEW_TASK_DEF_ARN"
    
    # Update the service with the new task definition
    echo "[DEBUG] Updating service with new task definition"
    aws --profile $AWS_PROFILE --region $AWS_REGION ecs update-service \
        --cluster $ECS_CLUSTER \
        --service $ECS_SERVICE \
        --task-definition $NEW_TASK_DEF_ARN \
        --force-new-deployment
        
    echo "[DEBUG] Service update initiated successfully"
}

# Function to wait for service stability
wait_for_stability() {
    if [ "$WAIT_FOR_STABILITY" = true ]; then
        echo "Waiting for service to stabilize..."
        aws --profile $AWS_PROFILE --region $AWS_REGION ecs wait services-stable \
            --cluster $ECS_CLUSTER \
            --services $ECS_SERVICE
    else
        echo "Skipping wait for service stability."
    fi
}

# Function to get service status
service_status() {
    echo "Getting service status for $APP_NAME..."
    aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-services \
        --cluster $ECS_CLUSTER \
        --services $ECS_SERVICE \
        --query 'services[0].{Status:status,DesiredCount:desiredCount,RunningCount:runningCount,PendingCount:pendingCount}'
}

# Function to check service events
service_events() {
    echo "Recent service events for $APP_NAME:"
    aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-services \
        --cluster $ECS_CLUSTER \
        --services $ECS_SERVICE \
        --query 'services[0].events[0:5]'
}

# Function to start an SSM session to the first available EC2 instance
start_ssm_session() {
    echo "Finding EC2 instance running $APP_NAME service..."
    
    # List tasks for the specified service
    TASK_ARNS=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs list-tasks \
        --cluster $ECS_CLUSTER \
        --service-name $ECS_SERVICE \
        --desired-status RUNNING \
        --query 'taskArns[0]' \
        --output text)
    
    if [ "$TASK_ARNS" == "None" ] || [ -z "$TASK_ARNS" ]; then
        echo "No running tasks found for service $ECS_SERVICE."
        exit 1
    fi
    
    echo "Found task: $TASK_ARNS"
    
    # Get container instance ARN for the task
    CONTAINER_INSTANCE=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-tasks \
        --cluster $ECS_CLUSTER \
        --tasks $TASK_ARNS \
        --query 'tasks[0].containerInstanceArn' \
        --output text)
    
    if [ "$CONTAINER_INSTANCE" == "None" ] || [ -z "$CONTAINER_INSTANCE" ]; then
        echo "Failed to get container instance for task."
        exit 1
    fi
    
    echo "Found container instance: $CONTAINER_INSTANCE"
    
    # Get EC2 instance ID from container instance
    EC2_INSTANCE=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-container-instances \
        --cluster $ECS_CLUSTER \
        --container-instances $CONTAINER_INSTANCE \
        --query 'containerInstances[0].ec2InstanceId' \
        --output text)
    
    if [ -z "$EC2_INSTANCE" ]; then
        echo "Failed to get EC2 instance ID."
        exit 1
    fi
    
    echo "Starting SSM session to instance $EC2_INSTANCE running $APP_NAME service..."
    aws --profile $AWS_PROFILE --region $AWS_REGION ssm start-session --target $EC2_INSTANCE
}

# Main logic
case "$COMMAND" in
    update)
        update_service
        wait_for_stability
        service_status
        ;;
    status)
        service_status
        ;;
    events)
        service_events
        ;;
    ssh)
        start_ssm_session
        ;;
    # *) case is now handled during initial command processing
esac

echo "ECS operation completed!"