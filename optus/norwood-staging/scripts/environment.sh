#!/bin/bash
set -e

# Variables
AWS_PROFILE="Optus"
AWS_REGION="ap-southeast-2"
APP_NAME="mozart"  # Default app name
ENV_CONFIG_FILE=".env.local"

# Process options
while [[ $# -gt 0 ]]; do
  case $1 in
    init|update)
      COMMAND="$1"
      shift
      ;;
    mozart|admin-dashboard)
      APP_NAME="$1"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 {init|update} [APP_NAME]"
      echo "  APP_NAME - Application name (e.g., 'mozart' or 'admin-dashboard')"
      exit 1
      ;;
  esac
done

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "AWS CLI is required but not installed. Please install AWS CLI first."
    exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo "jq is required but not installed. Please install jq first."
    exit 1
fi

# Check if the user is logged in to AWS
echo "Verifying AWS credentials..."
aws --profile $AWS_PROFILE sts get-caller-identity > /dev/null || {
    echo "AWS credentials not found or expired. Please run 'aws sso login --profile $AWS_PROFILE' first."
    exit 1
}

# Load environment variables from config file if it exists
load_env_config() {
    if [ -f "$ENV_CONFIG_FILE" ]; then
        echo "Loading environment variables from $ENV_CONFIG_FILE..."
        set -a
        source "$ENV_CONFIG_FILE"
        set +a
    else
        echo "Environment config file $ENV_CONFIG_FILE not found."
        echo "Using default placeholder values for some variables."
        echo "Create $ENV_CONFIG_FILE with the following variables for production use:"
        echo "AAI_API_KEY=your_assemblyai_api_key"
        echo "AAI_WEBHOOK_URL=your_assemblyai_webhook_url"
        echo "APNS_KEY=your_apple_push_notification_key"
        echo "JWT_SECRET=your_jwt_secret_key"
        echo "TWILIO_ACCOUNT_SID=your_twilio_account_sid"
        echo "TWILIO_AUTH_TOKEN=your_twilio_auth_token"
        echo "SMS_FROM_NUMBER=your_sms_from_number"
        
        # Set default values for secrets
        AAI_API_KEY="X"
        AAI_WEBHOOK_URL="X"
        APNS_KEY="X"
        JWT_SECRET="your_secret_key"
        TWILIO_ACCOUNT_SID="X"
        TWILIO_AUTH_TOKEN="X"
        SMS_FROM_NUMBER="X"
        
        # Admin dashboard specific values
        AUTH_PASSWORD="admin123"
    fi
}

# Get ECS cluster and service names from Terraform output
cd terraform
ECS_CLUSTER=$(terraform output -raw ecs_cluster_id)
if [ -z "$ECS_CLUSTER" ]; then
    echo "Failed to get ECS cluster ID from Terraform output."
    exit 1
fi

# Get appropriate service name based on APP_NAME
if [[ "$APP_NAME" == "admin-dashboard" ]]; then
    ECS_SERVICE=$(terraform output -raw admin_dashboard_ecs_service_name)
else
    ECS_SERVICE=$(terraform output -raw ecs_service_name)
fi

if [ -z "$ECS_SERVICE" ]; then
    echo "Failed to get ECS service name from Terraform output."
    exit 1
fi

# Get database and Redis connection info (only needed for mozart)
if [[ "$APP_NAME" == "mozart" ]]; then
    DB_ENDPOINT=$(terraform output -raw database_endpoint)
    DB_NAME=$(terraform output -raw database_name)
    DB_USER=$(terraform output -raw database_username)
    DB_PASSWORD_ARN=$(terraform output -raw database_password_secret_arn)
    REDIS_ENDPOINT=$(terraform output -raw redis_primary_endpoint)
    REDIS_PORT=$(terraform output -raw redis_port)
    
    # Get database password
    DB_PASSWORD=$(aws --profile $AWS_PROFILE --region $AWS_REGION secretsmanager get-secret-value \
        --secret-id $DB_PASSWORD_ARN --query 'SecretString' --output text)
fi
cd ..

# Get the current task definition
TASK_DEFINITION=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-task-definition \
    --task-definition $(aws --profile $AWS_PROFILE --region $AWS_REGION ecs describe-services \
        --cluster $ECS_CLUSTER \
        --services $ECS_SERVICE \
        --query 'services[0].taskDefinition' \
        --output text) \
    --query 'taskDefinition')

# Function to update environment variables for mozart
update_mozart_env_vars() {
    echo "Updating environment variables for Mozart..."
    
    # Load configuration
    load_env_config
    
    # Allow overriding non-sensitive values
    APP_ENV=${APP_ENV:-"production"}
    AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME:-"mozart-dev-bucket"}
    SMS_PROVIDER=${SMS_PROVIDER:-"twilio"}
    EMAIL_PROVIDER=${EMAIL_PROVIDER:-"aws"}
    AAI_HOSTNAME=${AAI_HOSTNAME:-"https://api.assemblyai.com/v2/transcript"}
    APNS_ENVIRONMENT=${APNS_ENVIRONMENT:-"sandbox"}
    REDIS_MAX_CONNECTION=${REDIS_MAX_CONNECTION:-"50"}
    TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID:-"X"}
    TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN:-"X"}
    SMS_FROM_NUMBER=${SMS_FROM_NUMBER:-"X"}

    # Create the connection strings
    DB_CONNECTION="postgres://${DB_USER}:${DB_PASSWORD}@${DB_ENDPOINT}/${DB_NAME}?sslmode=disable"
    REDIS_CONNECTION="redis://${REDIS_ENDPOINT}:${REDIS_PORT}"

    # Update task definition with environment variables
    NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg awsregion "$AWS_REGION" \
        --arg aaihostname "$AAI_HOSTNAME" \
        --arg apnsenv "$APNS_ENVIRONMENT" \
        --arg aaikey "$AAI_API_KEY" \
        --arg dbconn "$DB_CONNECTION" \
        --arg redisconn "$REDIS_CONNECTION" \
        --arg redismax "$REDIS_MAX_CONNECTION" \
        --arg aaiwebhook "$AAI_WEBHOOK_URL" \
        --arg apnskey "$APNS_KEY" \
        --arg smsprovider "$SMS_PROVIDER" \
        --arg emailprovider "$EMAIL_PROVIDER" \
        --arg jwtsecret "$JWT_SECRET" \
        --arg s3bucket "$AWS_S3_BUCKET_NAME" \
        --arg appenv "$APP_ENV" \
        --arg twilioaccsid "$TWILIO_ACCOUNT_SID" \
        --arg twilioauthtoken "$TWILIO_AUTH_TOKEN" \
        --arg smsfromnumber "$SMS_FROM_NUMBER" \
        '.containerDefinitions[0].environment += [
            {"name": "AWS_REGION", "value": $awsregion},
            {"name": "AAI_HOSTNAME", "value": $aaihostname},
            {"name": "APNS_ENVIRONMENT", "value": $apnsenv},
            {"name": "AAI_API_KEY", "value": $aaikey},
            {"name": "DB_CONNECTION", "value": $dbconn},
            {"name": "REDIS_CONNECTION", "value": $redisconn},
            {"name": "REDIS_MAX_CONNECTION", "value": $redismax},
            {"name": "AAI_WEBHOOK_URL", "value": $aaiwebhook},
            {"name": "APNS_KEY", "value": $apnskey},
            {"name": "SMS_PROVIDER", "value": $smsprovider},
            {"name": "EMAIL_PROVIDER", "value": $emailprovider},
            {"name": "JWT_SECRET", "value": $jwtsecret},
            {"name": "AWS_S3_BUCKET_NAME", "value": $s3bucket},
            {"name": "APP_ENV", "value": $appenv},
            {"name": "TWILIO_ACCOUNT_SID", "value": $twilioaccsid},
            {"name": "TWILIO_AUTH_TOKEN", "value": $twilioauthtoken},
            {"name": "SMS_FROM_NUMBER", "value": $smsfromnumber}
        ]')
}

# Function to update environment variables for admin dashboard
update_admin_dashboard_env_vars() {
    echo "Updating environment variables for Admin Dashboard..."
    
    # Load configuration
    load_env_config
    
    # Get API server URL (mozart ALB DNS)
    API_URL=$(cd terraform && terraform output -raw alb_dns_name)
    
    # Update task definition with environment variables
    NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg nodeenv "production" \
        --arg apiurl "http://$API_URL" \
        --arg publicapiurl "http://$API_URL" \
        --arg authuser "proton" \
        --arg authpass "$AUTH_PASSWORD" \
        '.containerDefinitions[0].environment += [
            {"name": "NODE_ENV", "value": $nodeenv},
            {"name": "API_SERVER_URL", "value": $apiurl},
            {"name": "NEXT_PUBLIC_API_SERVER_URL", "value": $publicapiurl},
            {"name": "AUTH_USERNAME", "value": $authuser},
            {"name": "AUTH_PASSWORD", "value": $authpass}
        ]')
}

# Function to update environment variables
update_env_vars() {
    echo "Updating environment variables for $APP_NAME..."
    
    if [[ "$APP_NAME" == "admin-dashboard" ]]; then
        update_admin_dashboard_env_vars
    else
        update_mozart_env_vars
    fi
    
    # Register the new task definition
    NEW_TASK_DEF_ARN=$(aws --profile $AWS_PROFILE --region $AWS_REGION ecs register-task-definition \
        --family $(echo $TASK_DEFINITION | jq -r '.family') \
        --execution-role-arn $(echo $TASK_DEFINITION | jq -r '.executionRoleArn') \
        --task-role-arn $(echo $TASK_DEFINITION | jq -r '.taskRoleArn') \
        --network-mode $(echo $TASK_DEFINITION | jq -r '.networkMode') \
        --container-definitions "$(echo $NEW_TASK_DEFINITION | jq '.containerDefinitions')" \
        --cpu $(echo $TASK_DEFINITION | jq -r '.cpu') \
        --memory $(echo $TASK_DEFINITION | jq -r '.memory') \
        --requires-compatibilities $(echo $TASK_DEFINITION | jq -r '.requiresCompatibilities[]') \
        --query 'taskDefinition.taskDefinitionArn' \
        --output text)
    
    # Update the service with the new task definition
    aws --profile $AWS_PROFILE --region $AWS_REGION ecs update-service \
        --cluster $ECS_CLUSTER \
        --service $ECS_SERVICE \
        --task-definition $NEW_TASK_DEF_ARN \
        --force-new-deployment

    echo "Waiting for service to stabilize..."
    aws --profile $AWS_PROFILE --region $AWS_REGION ecs wait services-stable \
        --cluster $ECS_CLUSTER \
        --services $ECS_SERVICE

    echo "Environment variables updated successfully!"
}

# Create example .env.local if it doesn't exist
create_env_example() {
    if [ ! -f ".env.local.example" ]; then
        echo "Creating .env.local.example file..."
        cat > .env.local.example << 'EOL'
# This is an example .env.local file
# Copy this to .env.local and replace with your actual values
# IMPORTANT: Never commit .env.local to git!

# External service API keys and sensitive values
AAI_API_KEY=your_assemblyai_api_key
AAI_WEBHOOK_URL=your_assemblyai_webhook_url
APNS_KEY=your_apple_push_notification_key
JWT_SECRET=your_jwt_secret_key
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
SMS_FROM_NUMBER=your_sms_from_number

# Application configuration (optional, defaults will be used if not specified)
APP_ENV=production
AAI_HOSTNAME=https://api.assemblyai.com/v2/transcript
APNS_ENVIRONMENT=sandbox
SMS_PROVIDER=twilio
EMAIL_PROVIDER=aws
AWS_S3_BUCKET_NAME=mozart-dev-bucket
REDIS_MAX_CONNECTION=50
EOL

        echo "Created .env.local.example"
        echo "Copy this to .env.local and update with your actual values:"
        echo "cp .env.local.example .env.local"
    fi
}

# Main logic
if [ -z "$COMMAND" ]; then
    echo "Usage: $0 {update|init} [APP_NAME]"
    echo "  update - Update environment variables for the ECS service"
    echo "  init   - Create example .env.local.example template"
    echo "  APP_NAME - Application name (e.g., 'mozart' or 'admin-dashboard')"
    exit 1
elif [[ "$COMMAND" == "update" ]]; then
    create_env_example
    update_env_vars
elif [[ "$COMMAND" == "init" ]]; then
    create_env_example
    echo "Created .env.local.example template."
    echo "Copy this to .env.local and update with your actual values:"
    echo "cp .env.local.example .env.local"
else
    echo "Usage: $0 {update|init} [APP_NAME]"
    echo "  update - Update environment variables for the ECS service"
    echo "  init   - Create example .env.local.example template"
    echo "  APP_NAME - Application name (e.g., 'mozart' or 'admin-dashboard')"
    exit 1
fi

echo "Environment operation completed!"