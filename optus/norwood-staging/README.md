# Mozart Vapor App AWS Deployment

This repository contains Terraform code and deployment scripts for deploying the Mozart Swift Vapor application to AWS ECS with EC2 instances.

## Infrastructure Components

The deployment includes the following AWS services:

- **Amazon ECR**: For storing the Docker images
- **Amazon ECS (EC2 mode)**: For running the containerized application
- **Amazon Aurora PostgreSQL**: For the database
- **Amazon ElastiCache for Redis**: For caching
- **Application Load Balancer**: For routing traffic to the application
- **VPC and Networking**: For secure network configuration
- **AWS Secrets Manager & SSM Parameter Store**: For managing environment variables and secrets

## Prerequisites

Before deploying, ensure you have the following installed:

- [AWS CLI](https://aws.amazon.com/cli/)
- [Terraform](https://www.terraform.io/downloads.html)
- [Docker](https://www.docker.com/get-started)
- [jq](https://jqlang.github.io/jq/download/) - Used by the scripts for JSON processing

You also need to have AWS credentials configured for the 'Optus' profile:

```bash
aws sso login --profile <PERSON><PERSON>
```

## Setting Up Environment Variables & Secrets

The application requires several environment variables and secrets to function properly. These are managed in two ways:

### 1. Setting Up Terraform Secrets (Long-term Infrastructure Setup)

To securely manage sensitive values in your infrastructure:

1. Copy the example secrets file to create your own:

   ```bash
   cp terraform/secrets.tf.example terraform/secrets.tf
   ```
2. Edit `terraform/secrets.tf` to add your actual secret values:

   ```bash
   # Replace placeholder values with real secrets
   "AAI_API_KEY"     = jsonencode({key = "YOUR_ACTUAL_API_KEY"})
   "AAI_WEBHOOK_URL" = jsonencode({url = "YOUR_ACTUAL_WEBHOOK_URL"})
   "APNS_KEY"        = jsonencode({key = "YOUR_ACTUAL_APNS_KEY"})
   "JWT_SECRET"      = jsonencode({secret = "STRONG_RANDOM_SECRET_KEY"})
   ```
3. Run `terraform apply` to deploy with these values.
4. The file `terraform/secrets.tf` is included in `.gitignore` to prevent secrets from being committed to Git.

### 2. Quick Environment Variable Updates

For a quick update without running a full Terraform apply:

```bash
# Initialize the environment config file template
./scripts/environment.sh init

# Copy the example file and edit it with your actual values
cp .env.local.example .env.local
nano .env.local  # or use your preferred editor

# Update the ECS task with environment variables for Mozart
./scripts/environment.sh update mozart

# Update the ECS task with environment variables for Admin Dashboard
./scripts/environment.sh update admin-dashboard
```

The script will:

- Load sensitive values from your local `.env.local` file (not tracked by Git)
- Fetch database and Redis connection details from Terraform
- Update all environment variables on the ECS task definition
- Force a new deployment of the service

If `.env.local` doesn't exist, placeholder values will be used for sensitive data, which should be replaced with actual values for production use.

## Deployment Instructions

### 1. Update the Docker Image Path

When running any script that requires a Docker image, use the `--image-path` option to point to your Dockerfile directory.

### 2. Individual Deployment Scripts

This project includes several modular scripts to manage different aspects of deployment:

#### Infrastructure Management (`scripts/infrastructure.sh`)

```bash
# Initialize Terraform
./scripts/infrastructure.sh init

# Plan Terraform changes
./scripts/infrastructure.sh plan

# Apply all Terraform modules
./scripts/infrastructure.sh apply [--auto-approve]

# Apply a specific module
./scripts/infrastructure.sh apply-module networking [--auto-approve]

# Destroy all resources
./scripts/infrastructure.sh destroy [--auto-approve]
```

#### Docker Image Management (`scripts/ecr.sh`)

```bash
# Build and push Docker image for mozart (default)
./scripts/ecr.sh --image-path /folder_path/to/your/dockerfile [--tag your-tag]

# Build and push Docker image for admin-dashboard
./scripts/ecr.sh --image-path /folder_path/to/your/dockerfile --admin-dashboard [--tag your-tag]
```

#### ECS Service Management (`scripts/ecs.sh`)

```bash
# Update the service with a specific image tag
./scripts/ecs.sh update APP_NAME [--tag your-tag] [--no-wait]
# Example: ./scripts/ecs.sh update mozart --tag v1.0.0
# Example: ./scripts/ecs.sh update admin-dashboard --tag v1.0.0

# Check service status
./scripts/ecs.sh status APP_NAME

# View recent service events
./scripts/ecs.sh events APP_NAME

# SSH into an EC2 instance in the ECS cluster
./scripts/ecs.sh ssh APP_NAME
```

Where APP_NAME is the name of the application (e.g., 'mozart' or 'admin-dashboard').

#### Environment Variables Management (`scripts/environment.sh`)

```bash
# Initialize the environment config file template
./scripts/environment.sh init

# Copy the example file and edit it with your actual values
cp .env.local.example .env.local
nano .env.local  # or use your preferred editor

# Update the ECS task with environment variables for Mozart
./scripts/environment.sh update mozart

# Update the ECS task with environment variables for Admin Dashboard
./scripts/environment.sh update admin-dashboard
```

#### Deployment Information (`scripts/info.sh`)

```bash
# Get all deployment information
./scripts/info.sh all [APP_NAME]

# Get only application URL
./scripts/info.sh app [APP_NAME]

# Get database information
./scripts/info.sh db

# Get Redis information
./scripts/info.sh redis

# Get ECS information
./scripts/info.sh ecs [APP_NAME]

# Get ECR information
./scripts/info.sh ecr [APP_NAME]

# Get EC2 information
./scripts/info.sh ec2
```

Where APP_NAME is the name of the application (e.g., 'mozart' or 'admin-dashboard'). If not specified, 'mozart' is used as the default.

### 3. Full Deployment

To perform a full deployment in one step:

```bash
./scripts/deploy.sh APP_NAME --image-path /folder_path/to/your/dockerfile [--tag your-tag] [--auto-approve] [--no-wait]
```

Where APP_NAME is the name of the application ('mozart' or 'admin-dashboard').

This script will:

1. Deploy all infrastructure
2. Build and push your Docker image
3. Update the ECS service
4. Display deployment information

#### Example Deployments

For the main Mozart application:

```bash
./scripts/deploy.sh mozart --image-path /path/to/Mozart/backend
```

For the admin dashboard:

```bash
./scripts/deploy.sh admin-dashboard --image-path /path/to/Mozart/frontend/admin-dashboard
```

### 4. Access Your Applications

After deployment, the script will output the URL for accessing your applications:

- Main Mozart application: http://[alb_dns_name]
- Admin Dashboard: http://[admin_dashboard_alb_dns_name]

You can also get the URLs separately with:

```bash
# Get main application URL
./scripts/info.sh app mozart

# Get admin dashboard URL
./scripts/info.sh app admin-dashboard
```

## Configuration

### Terraform Modules

- **networking**: Sets up VPC, subnets, route tables, NAT gateway
- **ecr**: Sets up the ECR repository for Docker images
- **ecs**: Sets up the ECS cluster, service, task definitions, and load balancer
  - Can be configured with a custom image tag through Terraform variables
- **rds**: Sets up the Aurora PostgreSQL database
- **elasticache**: Sets up the Redis cache

### Custom Image Tags

You can specify a custom image tag in multiple ways:

1. When building and pushing an image:

   ```bash
   ./scripts/ecr.sh --image-path /path/to/your/dockerfile --tag v1.0.0
   ```
2. When updating the ECS service:

   ```bash
   ./scripts/ecs.sh update --tag v1.0.0
   ```
3. Through Terraform variables:

   ```bash
   terraform -chdir=terraform apply -var="image_tag=v1.0.0"
   ```

If no tag is specified, the default tag "latest" will be used.

### Application Environment Variables

The following environment variables are configured for your applications:

### Mozart Backend Application

**Primary Configuration**

- `APP_ENV`: Environment name (production/staging/development)
- `PORT`: The port your application should listen on (default: 8080)

**Database**

- `DB_CONNECTION`: PostgreSQL connection string

**Redis**

- `REDIS_CONNECTION`: Redis connection string
- `REDIS_MAX_CONNECTION`: Maximum number of Redis connections

**AWS Settings**

- `AWS_REGION`: AWS region for the application
- `AWS_S3_BUCKET_NAME`: S3 bucket for file storage

**External Services**

- `AAI_HOSTNAME`: AssemblyAI API hostname
- `AAI_API_KEY`: AssemblyAI API key (secret)
- `AAI_WEBHOOK_URL`: AssemblyAI webhook URL (secret)
- `APNS_ENVIRONMENT`: Apple Push Notification environment
- `APNS_KEY`: Apple Push Notification key (secret)
- `SMS_PROVIDER`: SMS provider name
- `EMAIL_PROVIDER`: Email provider name
- `JWT_SECRET`: Secret key for JWT tokens (secret)
- `TWILIO_AUTH_TOKEN`: Secret key for TWILIO (secret)

### Admin Dashboard Application

**Configuration**

- `NODE_ENV`: Environment name (production/development)
- `API_SERVER_URL`: URL to the Mozart backend API (set automatically)
- `NEXT_PUBLIC_API_SERVER_URL`: URL to the Mozart backend API for client-side code (set automatically)
- `AUTH_USERNAME`: Username for basic authentication (default: proton)
- `AUTH_PASSWORD`: Password for basic authentication (secret, default: proton123)

## Clean Up

To remove all created resources:

```bash
./scripts/infrastructure.sh destroy
```
