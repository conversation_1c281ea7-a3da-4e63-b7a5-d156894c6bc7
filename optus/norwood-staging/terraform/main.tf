provider "aws" {
  profile = "Optus"
  region  = "ap-southeast-2"
}

module "networking" {
  source = "./modules/networking"
}

module "ecr" {
  source = "./modules/ecr"
}

# S3 Bucket for Mozart application artifacts
resource "aws_s3_bucket" "mozart_artifacts" {
  bucket = "mozart-assets-bucket" # TODO: Consider parameterizing for different environments e.g., "mozart-${var.environment}-bucket"

  tags = {
    Name        = "mozart-assets-bucket"
    Application = "Mozart"
  }
}

resource "aws_s3_bucket_public_access_block" "mozart_artifacts_public_access" {
  bucket = aws_s3_bucket.mozart_artifacts.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_versioning" "mozart_artifacts_versioning" {
  bucket = aws_s3_bucket.mozart_artifacts.id
  versioning_configuration {
    status = "Enabled"
  }
}

# IAM Policy for Mozart services to access the S3 bucket
resource "aws_iam_policy" "mozart_s3_access_policy" {
  name        = "MozartS3AccessPolicy" # TODO: Consider adding environment suffix if deploying to multiple environments
  description = "Allows Mozart services to access the S3 artifact bucket"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.mozart_artifacts.arn
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:DeleteObject",
          "s3:DeleteObjectVersion" # Required for versioned buckets
        ]
        Resource = [
          "${aws_s3_bucket.mozart_artifacts.arn}/*"
        ]
      }
    ]
  })
}

# Create SSM parameters for app configuration (non-sensitive)
resource "aws_ssm_parameter" "app_config" {
  for_each = {
    # Non-sensitive environment variables
    "AWS_REGION"           = "ap-southeast-2"
    "APNS_ENVIRONMENT"     = "sandbox"
    "REDIS_MAX_CONNECTION" = "50"
    "SMS_PROVIDER"         = "twilio"
    "EMAIL_PROVIDER"       = "aws"
    "AWS_S3_BUCKET_NAME"   = aws_s3_bucket.mozart_artifacts.id
    "TWILIO_ACCOUNT_SID"   = "AC91dd724da37a87d80a309b761d0128ad"
    "SMS_FROM_NUMBER"      = "+***********"
    "AWS_FROM_EMAIL"       = "<EMAIL>"
    
    # Database and Redis connection strings with proper values
    "DB_CONNECTION"        = "postgres://${module.rds.master_username}:${data.aws_secretsmanager_secret_version.rds_password.secret_string}@${module.rds.cluster_endpoint}/${module.rds.database_name}?sslmode=disable"
    "REDIS_CONNECTION"     = "redis://${module.elasticache.primary_endpoint}:${module.elasticache.port}"
  }

  name        = "/mozart/${each.key}"
  description = "Mozart application configuration parameter"
  type        = "String"
  value       = each.value

  tags = {
    Name = "mozart-${each.key}"
  }
}

# Create secrets for sensitive values
resource "aws_secretsmanager_secret" "app_secrets" {
  for_each = {
    # Add secrets (sensitive values)
    "DB_PASSWORD"     = "db-password"
    "AAI_API_KEY"     = "aai-api-key"
    "AAI_HOSTNAME"    = "aai-hostname"
    "AAI_WEBHOOK_URL" = "aai-webhook-url"
    "APNS_KEY"        = "apns-key"
    "JWT_SECRET"      = "jwt-secret"
    "AUTH_PASSWORD"   = "admin-dashboard-auth-password"
    "TWILIO_AUTH_TOKEN" = "twilio-auth-token"
  }

  name = "/mozart/${each.key}"
  
  tags = {
    Name = "mozart-${each.key}"
  }
}

# Secret values for external services are defined in secrets.tf which should be in .gitignore
# See secrets.tf.example for format reference

# Handle DB password separately since it comes from the RDS module (no sensitive data is leaked)
resource "aws_secretsmanager_secret_version" "db_password" {
  secret_id     = aws_secretsmanager_secret.app_secrets["DB_PASSWORD"].id
  secret_string = jsonencode({
    password = data.aws_secretsmanager_secret_version.rds_password.secret_string
  })
}

data "aws_secretsmanager_secret_version" "rds_password" {
  secret_id = module.rds.password_secret_arn
}

module "ecs" {
  source         = "./modules/ecs"
  app_name       = "mozart"
  vpc_id         = module.networking.vpc_id
  subnets        = module.networking.private_subnets
  alb_subnets    = module.networking.public_subnets
  repository_url = module.ecr.repository_url
  image_tag      = var.image_tag
  s3_access_policy_arn = aws_iam_policy.mozart_s3_access_policy.arn
  
  # Pass environment variables (non-sensitive)
  environment_variables = [
    {
      name  = "APP_ENV"
      value = "development"
    }
  ]
  
  # Pass all parameters and secrets
  secrets = concat(
    # Non-sensitive parameters from SSM Parameter Store
    [
      for key in keys(aws_ssm_parameter.app_config) : {
        name      = key
        valueFrom = aws_ssm_parameter.app_config[key].arn
      }
    ],
    # Sensitive values from Secrets Manager
    [
      for key in keys(aws_secretsmanager_secret.app_secrets) : {
        name      = key
        valueFrom = aws_secretsmanager_secret.app_secrets[key].arn
      }
    ]
  )
  
  depends_on     = [module.networking, module.ecr, module.rds, module.elasticache]
}

# Create SSM parameters for admin dashboard configuration (non-sensitive)
resource "aws_ssm_parameter" "admin_dashboard_config" {
  for_each = {
    # Non-sensitive environment variables for admin dashboard
    "API_SERVER_URL"          = "http://${module.ecs.alb_dns_name}"
    "NEXT_PUBLIC_API_SERVER_URL"  = "http://${module.ecs.alb_dns_name}"
    "AUTH_USERNAME"           = "proton"
    "NODE_ENV"                = "production"
  }

  name        = "/mozart/admin-dashboard/${each.key}"
  description = "Mozart Admin Dashboard application configuration parameter"
  type        = "String"
  value       = each.value

  tags = {
    Name = "mozart-admin-dashboard-${each.key}"
  }
}

# Create secrets for admin dashboard
resource "aws_secretsmanager_secret" "admin_dashboard_secrets" {
  for_each = {
    "AUTH_PASSWORD" = "admin-dashboard-auth-password"
  }

  name = "/mozart/admin-dashboard/${each.key}"
  
  tags = {
    Name = "mozart-admin-dashboard-${each.key}"
  }
}

# Admin dashboard resources
resource "aws_security_group" "admin_dashboard_alb" {
  name        = "admin-dashboard-alb-sg"
  description = "Allow HTTP inbound traffic for admin dashboard"
  vpc_id      = module.networking.vpc_id

  ingress {
    protocol    = "tcp"
    from_port   = 80
    to_port     = 80
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 443
    to_port     = 443
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "admin-dashboard-alb-sg"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_security_group" "admin_dashboard_tasks" {
  name        = "admin-dashboard-tasks-sg"
  description = "Allow inbound access from the ALB only for admin dashboard"
  vpc_id      = module.networking.vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = 4000
    to_port         = 4000
    security_groups = [aws_security_group.admin_dashboard_alb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "admin-dashboard-tasks-sg"
  }
}

resource "aws_lb" "admin_dashboard" {
  name               = "admin-dashboard-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.admin_dashboard_alb.id]
  subnets            = module.networking.public_subnets

  tags = {
    Name = "admin-dashboard-alb"
  }
}

resource "aws_lb_target_group" "admin_dashboard" {
  name        = "admin-dashboard-target-group"
  port        = 4000
  protocol    = "HTTP"
  vpc_id      = module.networking.vpc_id
  target_type = "ip"

  health_check {
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 30
    interval            = 60
    path                = "/"
    port                = "traffic-port"
    matcher             = "200-499"
  }

  tags = {
    Name = "admin-dashboard-target-group"
  }
  
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb_listener" "admin_dashboard" {
  load_balancer_arn = aws_lb.admin_dashboard.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.admin_dashboard.arn
  }
}

resource "aws_iam_role" "admin_dashboard_task_execution_role" {
  name = "admin-dashboard-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })
  
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_iam_role_policy_attachment" "admin_dashboard_execution_role_policy" {
  role       = aws_iam_role.admin_dashboard_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_policy" "admin_dashboard_secrets_access" {
  name        = "admin-dashboard-secrets-access"
  description = "Allow admin dashboard ECS tasks to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "ssm:GetParameters",
          "ssm:GetParameter",
          "ssm:DescribeParameters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "admin_dashboard_secrets_policy" {
  role       = aws_iam_role.admin_dashboard_task_execution_role.name
  policy_arn = aws_iam_policy.admin_dashboard_secrets_access.arn
}

resource "aws_iam_role" "admin_dashboard_task_role" {
  name = "admin-dashboard-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })
  
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_cloudwatch_log_group" "admin_dashboard" {
  name              = "/ecs/admin-dashboard"
  retention_in_days = 30

  tags = {
    Name = "admin-dashboard-log-group"
  }
}

resource "aws_ecs_task_definition" "admin_dashboard" {
  family                   = "admin-dashboard-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["EC2"]
  cpu                      = 2048
  memory                   = 2048
  execution_role_arn       = aws_iam_role.admin_dashboard_task_execution_role.arn
  task_role_arn            = aws_iam_role.admin_dashboard_task_role.arn

  container_definitions = jsonencode([
    {
      name      = "admin-dashboard"
      image     = "${module.ecr.admin_dashboard_repository_url}:${var.admin_dashboard_image_tag}"
      essential = true
      portMappings = [
        {
          containerPort = 4000
          hostPort      = 4000
        }
      ]
      environment = [
        {
          name  = "PORT"
          value = "4000"
        }
      ]
      secrets = concat(
        # Non-sensitive parameters from SSM Parameter Store
        [
          for key in keys(aws_ssm_parameter.admin_dashboard_config) : {
            name      = key
            valueFrom = aws_ssm_parameter.admin_dashboard_config[key].arn
          }
        ],
        # Sensitive values from Secrets Manager
        [
          for key in keys(aws_secretsmanager_secret.admin_dashboard_secrets) : {
            name      = key
            valueFrom = aws_secretsmanager_secret.admin_dashboard_secrets[key].arn
          }
        ]
      )
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/admin-dashboard"
          "awslogs-region"        = "ap-southeast-2"
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])

  tags = {
    Name = "admin-dashboard-task-definition"
  }
}

resource "aws_ecs_service" "admin_dashboard" {
  name            = "admin-dashboard-service"
  cluster         = module.ecs.cluster_id
  task_definition = aws_ecs_task_definition.admin_dashboard.arn
  desired_count   = 1
  launch_type     = null

  capacity_provider_strategy {
    capacity_provider = "mozart-ec2-capacity-provider"
    weight            = 100
    base              = 1
  }

  network_configuration {
    security_groups  = [aws_security_group.admin_dashboard_tasks.id]
    subnets          = module.networking.private_subnets
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.admin_dashboard.arn
    container_name   = "admin-dashboard"
    container_port   = 4000
  }

  lifecycle {
    ignore_changes = [
      task_definition     # ← prevents roll‑backs
    ]
  }

  depends_on = [aws_lb_listener.admin_dashboard, module.ecs]

  tags = {
    Name = "admin-dashboard-service"
  }
}

# ====================== Kamailio resources =========================
# Kamailio resources

# Create SSM parameters for kamailio configuration 
resource "aws_ssm_parameter" "kamailio_config" {
  for_each = {
    # Non-sensitive environment variables for admin dashboard
    "MYPUBLICKAMAILIO"          = "kamailio-nlb-a232ff11a7a26225.elb.ap-southeast-2.amazonaws.com:55060" 
    "MYSQLPUBLICIP"  = "kamailio-cluster.cluster-cdygk0cswvru.ap-southeast-2.rds.amazonaws.com"
    "PATH"           = "/usr/sbin/kamailio:/usr/sbin/:/usr/bin"
  }

  name        = "/kamailio/${each.key}"
  description = "Kamailio application configuration parameter"
  type        = "String"
  value       = each.value

  tags = {
    Name = "kamailio-${each.key}"
  }
}

resource "aws_iam_policy" "kamailio_secrets_access" {
  name        = "kamailio-secrets-access"
  description = "Allow kamailio ECS tasks to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "ssm:GetParameters",
          "ssm:GetParameter",
          "ssm:DescribeParameters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_security_group" "kamailio_nlb" {
  name        = "kamailio-nlb-sg"
  description = "Allow SIP traffic for kamailio server"
  vpc_id      = module.networking.vpc_id

  ingress {
    protocol    = "udp"
    from_port   = 55060
    to_port     = 55060
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 55060
    to_port     = 55060
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "kamailio-nlb-sg"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb" "kamailio" {
  name               = "kamailio-nlb"
  internal           = false
  load_balancer_type = "network"
  security_groups    = [aws_security_group.kamailio_nlb.id]
  subnets            = module.networking.public_subnets

  tags = {
    Name = "kamailio-nlb"
  }
}

resource "aws_security_group" "kamailio" {
  name        = "kamailio-sg"
  description = "Allow inbound access from the NLB only for kamailio server"
  vpc_id      = module.networking.vpc_id

  ingress {
    protocol        = "udp"
    from_port       = 55060
    to_port         = 55060
    security_groups = [aws_security_group.kamailio_nlb.id]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 55060
    to_port     = 55060
    security_groups = [aws_security_group.kamailio_nlb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "kamailio-sg"
  }
}

resource "aws_lb_target_group" "kamailio" {
  name        = "kamailio-target-group"
  port        = 55060
  protocol    = "UDP"
  vpc_id      = module.networking.vpc_id
  target_type = "instance"

  health_check {
    protocol            = "TCP"
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 30
    interval            = 60
    port                = "traffic-port"
  }

  tags = {
    Name = "kamailio-target-group"
  }
  
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb_listener" "kamailio" {
  load_balancer_arn = aws_lb.kamailio.arn
  port              = 55060
  protocol          = "UDP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.kamailio.arn
  }
}


resource "aws_ecs_task_definition" "kamailio" {
  family                   = "kamailio-task-new"
  network_mode             = "bridge"  # or "host" if using host networking
  requires_compatibilities = ["EC2"]   # or ["FARGATE"] if using Fargate
  execution_role_arn       = aws_iam_role.kamailio_execution_role.arn
  task_role_arn            = aws_iam_role.kamailio_task_role.arn
  cpu                      = 2048      # 2 vCPU
  memory                   = 3096      # 4GB memory

  container_definitions = jsonencode([{
    name      = "kamailio"
    image     = "961341535886.dkr.ecr.ap-southeast-2.amazonaws.com/test/kamailio:latest"
    essential = true

    portMappings = [{
      containerPort = 55060
      hostPort      = 55060
      protocol      = "udp"
    }]

    secrets = concat(
        # Non-sensitive parameters from SSM Parameter Store
        [
          for key in keys(aws_ssm_parameter.kamailio_config) : {
            name      = key
            valueFrom = aws_ssm_parameter.kamailio_config[key].arn
          }
        ])

    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-group         = "/ecs/kamailio"
        awslogs-region        = "ap-southeast-2"
        awslogs-stream-prefix = "kamailio"
      }
    }
  }])
}

resource "aws_ecs_service" "kamailio" {
  name            = "kamailio-service"
  cluster         = module.ecs.cluster_id
  task_definition = aws_ecs_task_definition.kamailio.arn
  desired_count   = 1
  launch_type     = null

  capacity_provider_strategy {
    capacity_provider = "mozart-ec2-capacity-provider"
    weight            = 100
    base              = 1
  }

  #network_configuration {
  #  security_groups  = [aws_security_group.kamailio.id]
  #  subnets          = module.networking.public_subnets
  #   assign_public_ip = true
  #}

  load_balancer {
    target_group_arn = aws_lb_target_group.kamailio.arn
    container_name   = "kamailio"
    container_port   = 55060
  }

  lifecycle {
    ignore_changes = [
      task_definition     # ← prevents roll‑backs
    ]
  }

  depends_on = [aws_lb_listener.kamailio, module.ecs]

  tags = {
    Name = "kamailio-service"
  }
}

# Supporting Resources

resource "aws_iam_role" "kamailio_execution_role" {
  name = "ecs-kamailio-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

resource "aws_iam_role_policy_attachment" "kamailio_execution_role_policy" {
  role       = aws_iam_role.kamailio_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_role_policy_attachment" "kamailio_secrets_policy" {
  role       = aws_iam_role.kamailio_execution_role.name
  policy_arn = aws_iam_policy.kamailio_secrets_access.arn
}

resource "aws_iam_role" "kamailio_task_role" {
  name = "ecs-kamailio-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

resource "aws_cloudwatch_log_group" "kamailio" {
  name              = "/ecs/kamailio"
  retention_in_days = 30
}

#========================= The end of Kamailio resources ===================

# ====================== Freeswitch resources =========================

# Create SSM parameters for Freeswitch configuration 
resource "aws_ssm_parameter" "freeswitch_config" {
  for_each = {
    # Non-sensitive environment variables for freeswitch container
    "PATH"           = "/usr/local/freeswitch/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
    "GATEWAY"        = "**************"
    "VM_MAX_WORKERS"  = "16"
    "MOZART_SERVER" = "http://mozart-alb-1142825309.ap-southeast-2.elb.amazonaws.com"
    "AFFINITY" = "Freeswitch"
    "UPLOAD_MEDIA_SERVER"   = "s3-ap-southeast-2.amazonaws.com"
    "LD_LIBRARY_PATH" = "/usr/local/src/freeswitch/libs/speechsdk/lib/x64"
    "UPLOAD_MEDIA_BUCKET"   = "mozart-assets-bucket"
    "UPLOAD_TO_MOZART"      = "YES"
    "DELETE_LOCAL_VM"       = "NO"
  }

  name        = "/freeswitch/${each.key}"
  description = "Freeswitch application configuration parameter"
  type        = "String"
  value       = each.value

  tags = {
    Name = "freeswitch-${each.key}"
  }
}

# Create secrets for freeswitch server
resource "aws_secretsmanager_secret" "freeswitch_secrets" {
  for_each = {
    "AWS_ACCESS_KEY"        = "aws-access-key"
    "MOZART_AUTH_TOKEN"     = "mozart-auth-token"
    "AWS_SECRET_ACCESS_KEY" = "aws-secret-access-key"
  }

  name = "/freeswitch/${each.key}"
  
  tags = {
    Name = "freeswitch-${each.key}"
  }
}

resource "aws_iam_policy" "freeswitch_secrets_access" {
  name        = "freeswitch-secrets-access"
  description = "Allow Freeswitch ECS tasks to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "ssm:GetParameters",
          "ssm:GetParameter",
          "ssm:DescribeParameters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_security_group" "freeswitch_nlb" {
  name        = "freeswitch-nlb-sg"
  description = "Allow SIP traffic for Freeswitch server"
  vpc_id      = module.networking.vpc_id

  ingress {
    protocol    = "udp"
    from_port   = 6060
    to_port     = 6060
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 6060
    to_port     = 6060
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "freeswitch-nlb-sg"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb" "freeswitch" {
  name               = "freeswitch-nlb"
  internal           = false
  load_balancer_type = "network"
  security_groups    = [aws_security_group.freeswitch_nlb.id]
  subnets            = module.networking.public_subnets

  tags = {
    Name = "freeswitch-nlb"
  }
}

resource "aws_security_group" "freeswitch" {
  name        = "freeswitch-sg"
  description = "Allow inbound access from the NLB only for freeswitch server"
  vpc_id      = module.networking.vpc_id

  ingress {
    protocol        = "udp"
    from_port       = 6060
    to_port         = 6060
    security_groups = [aws_security_group.kamailio_nlb.id]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 6060
    to_port     = 6060
    security_groups = [aws_security_group.kamailio_nlb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "freeswitch-sg"
  }
}

resource "aws_lb_target_group" "freeswitch" {
  name        = "freeswitch-target-group"
  port        = 6060
  protocol    = "UDP"
  vpc_id      = module.networking.vpc_id
  target_type = "instance"

  health_check {
    protocol            = "TCP"
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 30
    interval            = 60
    port                = "traffic-port"
  }

  tags = {
    Name = "freeswitch-target-group"
  }
  
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb_listener" "freeswitch" {
  load_balancer_arn = aws_lb.freeswitch.arn
  port              = 6060
  protocol          = "UDP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.freeswitch.arn
  }
}


resource "aws_ecs_task_definition" "freeswitch" {
  family                   = "freeswitch-task-new"
  network_mode             = "bridge"  # or "host" if using host networking
  requires_compatibilities = ["EC2"]   # or ["FARGATE"] if using Fargate
  execution_role_arn       = aws_iam_role.freeswitch_execution_role.arn
  task_role_arn            = aws_iam_role.freeswitch_task_role.arn
  cpu                      = 2048      # 2 vCPU
  memory                   = 3096      # 4GB memory

  container_definitions = jsonencode([{
    name      = "freeswitch"
    image     = "961341535886.dkr.ecr.ap-southeast-2.amazonaws.com/freeswitch-optus-voicemail:latest"
    essential = true

    portMappings = [{
      containerPort = 6060
      hostPort      = 6060
      protocol      = "udp"
    }]

    secrets = concat(
        # Non-sensitive parameters from SSM Parameter Store
        [
          for key in keys(aws_ssm_parameter.freeswitch_config) : {
            name      = key
            valueFrom = aws_ssm_parameter.freeswitch_config[key].arn
          }
        ],
        # Sensitive values from Secrets Manager
        [
          for key in keys(aws_secretsmanager_secret.freeswitch_secrets) : {
            name      = key
            valueFrom = aws_secretsmanager_secret.freeswitch_secrets[key].arn
          }
        ]
      )

    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-group         = "/ecs/freeswitch"
        awslogs-region        = "ap-southeast-2"
        awslogs-stream-prefix = "freeswitch"
      }
    }
  }])
}

resource "aws_ecs_service" "freeswitch" {
  name            = "freeswitch-service"
  cluster         = module.ecs.cluster_id
  task_definition = aws_ecs_task_definition.freeswitch.arn
  desired_count   = 1
  launch_type     = null

  capacity_provider_strategy {
    capacity_provider = "mozart-ec2-capacity-provider"
    weight            = 100
    base              = 1
  }

  #network_configuration {
  #  security_groups  = [aws_security_group.kamailio.id]
  #  subnets          = module.networking.public_subnets
  #   assign_public_ip = true
  #}

  load_balancer {
    target_group_arn = aws_lb_target_group.freeswitch.arn
    container_name   = "freeswitch"
    container_port   = 6060
  }

  lifecycle {
    ignore_changes = [
      task_definition     # ← prevents roll‑backs
    ]
  }

  depends_on = [aws_lb_listener.freeswitch, module.ecs]

  tags = {
    Name = "freeswitch-service"
  }
}

# Supporting Resources

resource "aws_iam_role" "freeswitch_execution_role" {
  name = "ecs-freeswitch-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

resource "aws_iam_role_policy_attachment" "freeswitch_secrets_policy" {
  role       = aws_iam_role.freeswitch_execution_role.name
  policy_arn = aws_iam_policy.freeswitch_secrets_access.arn
}

resource "aws_iam_role_policy_attachment" "freeswitch_execution_role_policy" {
  role       = aws_iam_role.freeswitch_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_role" "freeswitch_task_role" {
  name = "ecs-freeswitch-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

resource "aws_cloudwatch_log_group" "freeswitch" {
  name              = "/ecs/freeswitch"
  retention_in_days = 30
}

#========================= The end of Freeswitch resources ===================

# Output for the admin dashboard ALB DNS name
output "admin_dashboard_alb_dns_name" {
  description = "The DNS name of the admin dashboard load balancer"
  value       = aws_lb.admin_dashboard.dns_name
}

# Output for the admin dashboard ECS service name
output "admin_dashboard_ecs_service_name" {
  description = "The name of the admin dashboard ECS service"
  value       = aws_ecs_service.admin_dashboard.name
}

# Output for the kamailio NLB DNS name
output "kamailio_nlb_dns_name" {
  description = "The DNS name of the kamailio load balancer"
  value       = aws_lb.kamailio.dns_name
}

# Output for the kamailio ECS service name
output "kamailio_ecs_service_name" {
  description = "The name of the kamailio ECS service"
  value       = aws_ecs_service.kamailio.name
}

# Output for the freeswitch NLB DNS name
output "freeswitch_nlb_dns_name" {
  description = "The DNS name of the freeswitch load balancer"
  value       = aws_lb.freeswitch.dns_name
}

# Output for the freeswitch ECS service name
output "freeswitch_ecs_service_name" {
  description = "The name of the freeswitch ECS service"
  value       = aws_ecs_service.freeswitch.name
}

module "rds" {
  source         = "./modules/rds"
  app_name       = "mozart"
  vpc_id         = module.networking.vpc_id
  subnets        = module.networking.database_subnets
  depends_on     = [module.networking]
}

module "elasticache" {
  source     = "./modules/elasticache"
  app_name   = "mozart"
  vpc_id     = module.networking.vpc_id
  subnets    = module.networking.elasticache_subnets
  depends_on = [module.networking]
} 

module "rds_kamailio" {
  source         = "./modules/rds_kamailio"
  app_name       = "kamailio"
  vpc_id         = module.networking.vpc_id
  subnets        = module.networking.database_subnets
  depends_on     = [module.networking]
}
