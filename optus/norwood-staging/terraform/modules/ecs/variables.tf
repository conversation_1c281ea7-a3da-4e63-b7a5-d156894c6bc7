variable "app_name" {
  description = "Name of the application"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where resources will be created"
  type        = string
}

variable "subnets" {
  description = "List of subnet IDs for the ECS service"
  type        = list(string)
}

variable "repository_url" {
  description = "ECR repository URL for container images"
  type        = string
}

variable "image_tag" {
  description = "The tag of the Docker image to deploy"
  type        = string
  default     = "latest"
}

variable "container_port" {
  description = "Port the container exposes"
  type        = number
  default     = 8080
}

variable "cpu" {
  description = "CPU units for the task"
  type        = number
  default     = 2048
}

variable "memory" {
  description = "Memory for the task in MiB"
  type        = number
  default     = 2048
}

variable "desired_count" {
  description = "Desired number of tasks running"
  type        = number
  default     = 1
}

variable "environment_variables" {
  description = "Environment variables for the container (non-sensitive)"
  type        = list(object({
    name  = string
    value = string
  }))
  default     = []
}

variable "secrets" {
  description = "Secrets to pass to the container (from Secrets Manager or SSM Parameter Store)"
  type        = list(object({
    name      = string
    valueFrom = string
  }))
  default     = []
}

variable "use_existing_cluster" {
  description = "Whether to use an existing ECS cluster instead of creating a new one"
  type        = bool
  default     = false
}

variable "existing_cluster_id" {
  description = "ID of an existing ECS cluster to use (when use_existing_cluster is true)"
  type        = string
  default     = ""
}

variable "alb_subnets" {
  description = "A list of subnets to associate with the Application Load Balancer. Should be public subnets."
  type        = list(string)
}

variable "imap_server_container_port" {
  description = "Port the imap-server container exposes and the NLB listens on."
  type        = number
  default     = 8080
}

variable "imap_server_desired_count" {
  description = "Desired number of imap-server tasks running."
  type        = number
  default     = 1
}

variable "s3_access_policy_arn" {
  description = "The ARN of the IAM policy that grants S3 access to the ECS tasks."
  type        = string
  default     = null # Or make it non-nullable if it's always required
} 
