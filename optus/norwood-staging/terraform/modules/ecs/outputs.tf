output "cluster_id" {
  value = aws_ecs_cluster.main.id
}

output "service_name" {
  value = aws_ecs_service.app.name
}

output "alb_dns_name" {
  value = aws_lb.main.dns_name
}

output "mozart_worker_service_name" {
  description = "The name of the Mozart Worker ECS service"
  value       = aws_ecs_service.mozart_worker.name
}

output "mozart_worker_task_definition_family" {
  description = "The family of the Mozart Worker task definition"
  value       = aws_ecs_task_definition.mozart_worker.family
}

output "imap_server_nlb_dns_name" {
  description = "The DNS name of the Network Load Balancer for the IMAP server."
  value       = aws_lb.imap_nlb.dns_name
}

output "imap_server_service_name" {
  description = "The name of the ECS service for the IMAP server."
  value       = aws_ecs_service.imap_server.name
} 