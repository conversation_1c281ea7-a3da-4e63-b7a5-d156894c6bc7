resource "aws_ecr_repository" "mozart" {
  name                 = "mozart"
  image_tag_mutability = "MUTABLE"

  image_scanning_configuration {
    scan_on_push = true
  }

  tags = {
    Name = "mozart-repository"
  }
}

resource "aws_ecr_lifecycle_policy" "mozart" {
  repository = aws_ecr_repository.mozart.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last 10 images"
        selection = {
          tagStatus     = "any"
          countType     = "imageCountMoreThan"
          countNumber   = 10
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

resource "aws_ecr_repository" "admin_dashboard" {
  name                 = "admin-dashboard"
  image_tag_mutability = "MUTABLE"

  image_scanning_configuration {
    scan_on_push = true
  }

  tags = {
    Name = "admin-dashboard-repository"
  }
}

resource "aws_ecr_lifecycle_policy" "admin_dashboard" {
  repository = aws_ecr_repository.admin_dashboard.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep last 10 images"
        selection = {
          tagStatus     = "any"
          countType     = "imageCountMoreThan"
          countNumber   = 10
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}