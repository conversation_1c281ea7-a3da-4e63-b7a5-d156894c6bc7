# This is an example file for secrets.tf
# Copy this file to secrets.tf and replace the placeholder values with actual secrets
# IMPORTANT: Add secrets.tf to .gitignore to prevent committing sensitive data

# Set the secret values for external services and APIs
resource "aws_secretsmanager_secret_version" "secrets" {
  for_each = {
    # Replace these placeholder values with actual secrets for the main Mozart app
    "AAI_API_KEY"     = jsonencode({key = "YOUR_AAI_API_KEY_HERE"})
    "AAI_HOSTNAME"    = jsonencode({url = "https://api.assemblyai.com/v2/transcript"})
    "AAI_WEBHOOK_URL" = jsonencode({url = "YOUR_AAI_WEBHOOK_URL_HERE"})
    "APNS_KEY"        = jsonencode({key = "YOUR_APNS_KEY_HERE"})
    "JWT_SECRET"      = jsonencode({secret = "STRONG_RANDOM_KEY_HERE"})
  }

  secret_id     = aws_secretsmanager_secret.app_secrets[each.key].id
  secret_string = each.value
}

# Set the secret values for the admin dashboard
resource "aws_secretsmanager_secret_version" "admin_dashboard_secrets" {
  for_each = {
    # Replace this placeholder value with actual admin dashboard password
    "AUTH_PASSWORD" = jsonencode({password = "proton123"})
  }

  secret_id     = aws_secretsmanager_secret.admin_dashboard_secrets[each.key].id
  secret_string = each.value
}

# Note: The DB password handling is in main.tf since it doesn't contain sensitive hardcoded values