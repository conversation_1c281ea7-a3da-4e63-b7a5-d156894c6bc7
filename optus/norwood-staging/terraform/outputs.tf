output "ecr_repository_url" {
  description = "The URL of the ECR repository"
  value       = module.ecr.repository_url
}

output "ecr_repository_name" {
  description = "The name of the ECR repository"
  value       = module.ecr.repository_name
}

output "admin_dashboard_ecr_repository_url" {
  description = "The URL of the admin dashboard ECR repository"
  value       = module.ecr.admin_dashboard_repository_url
}

output "admin_dashboard_ecr_repository_name" {
  description = "The name of the admin dashboard ECR repository"
  value       = module.ecr.admin_dashboard_repository_name
}

output "ecs_cluster_id" {
  description = "The ID of the ECS cluster"
  value       = module.ecs.cluster_id
}

output "ecs_service_name" {
  description = "The name of the ECS service"
  value       = module.ecs.service_name
}

output "alb_dns_name" {
  description = "The DNS name of the load balancer"
  value       = module.ecs.alb_dns_name
}

output "database_endpoint" {
  description = "The endpoint of the RDS cluster"
  value       = module.rds.cluster_endpoint
}

output "database_reader_endpoint" {
  description = "The reader endpoint of the RDS cluster"
  value       = module.rds.reader_endpoint
}

output "database_name" {
  description = "The name of the database"
  value       = module.rds.database_name
}

output "kamailio_database_name" {
  description = "The name of the kamailio database"
  value       = module.rds_kamailio.database_name
}

output "database_username" {
  description = "The master username for the database"
  value       = module.rds.master_username
}

output "kamailio_database_username" {
  description = "The master username for the kamailio database"
  value       = module.rds_kamailio.master_username
}

output "database_password_secret_arn" {
  description = "The ARN of the secret containing the database password"
  value       = module.rds.password_secret_arn
}

output "kamailio_database_password_secret_arn" {
  description = "The ARN of the secret containing the kamailio database password"
  value       = module.rds_kamailio.password_secret_arn
}

output "redis_primary_endpoint" {
  description = "The primary endpoint of the Redis cluster"
  value       = module.elasticache.primary_endpoint
}

output "redis_reader_endpoint" {
  description = "The reader endpoint of the Redis cluster"
  value       = module.elasticache.reader_endpoint
}

output "redis_port" {
  description = "The port of the Redis cluster"
  value       = module.elasticache.port
} 

output "mozart_worker_ecs_service_name" {
  description = "The name of the Mozart Worker ECS service (from ecs module)"
  value       = module.ecs.mozart_worker_service_name
}

output "mozart_worker_task_definition_family" {
  description = "The family of the Mozart Worker task definition (from ecs module)"
  value       = module.ecs.mozart_worker_task_definition_family
}

output "mozart_worker_ecr_repository_url" {
  description = "The URL of the ECR repository for Mozart Worker (same as Mozart service)"
  value       = module.ecr.repository_url # Assumes mozart-worker uses the same ECR repo as mozart
}

output "imap_server_endpoint" { 
  description = "The public endpoint (NLB DNS name) for the IMAP server."
  value       = module.ecs.imap_server_nlb_dns_name
} 

output "imap_server_ecs_service_name" { 
  description = "The name of the ECS service for the IMAP server (from ecs module)."
  value       = module.ecs.imap_server_service_name
} 