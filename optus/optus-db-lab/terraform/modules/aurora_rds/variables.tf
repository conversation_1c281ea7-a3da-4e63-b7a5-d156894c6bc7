variable "app_name" {
  description = "Name of the application"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where resources will be created"
  type        = string
  default     = "vpc-0e968670fadd4c2e8"
}

variable "subnets" {
  description = "List of subnet IDs for the RDS cluster"
  type        = list(string)
  default     = ["subnet-0cabae3036945e3e7","subnet-08868598098e83bf3"]
}

variable "instance_class" {
  description = "Instance class for the RDS instances"
  type        = string
  default     = "db.t3.medium"
}

variable "database_name" {
  description = "Name of the database to create"
  type        = string
  default     = "mozart"
}

variable "master_username" {
  description = "Master username for the database"
  type        = string
  default     = "mozart"
}

variable "backup_retention_period" {
  description = "Number of days to retain backups"
  type        = number
  default     = 7
}  
