variable "app_name" {
  description = "Name of the application"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where resources will be created"
  type        = string
  default     = "vpc-0e968670fadd4c2e8"
}

variable "subnets" {
  description = "List of subnet IDs for the ElastiCache cluster"
  type        = list(string)
  default     = ["subnet-0cabae3036945e3e7","subnet-08868598098e83bf3"]
}

variable "node_type" {
  description = "The instance class to be used"
  type        = string
  default     = "cache.t3.micro"
}

variable "engine_version" {
  description = "Version number of the Redis engine"
  type        = string
  default     = "6.x"
}

variable "parameter_group_name" {
  description = "The name of the parameter group to associate with this cache cluster"
  type        = string
  default     = "default.redis6.x"
}
