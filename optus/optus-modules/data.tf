data "aws_launch_template" "existing" {
  filter {
    name   = "tag:Name"
    values = ["mozart-lab-launch_template"]
  }
}

data "aws_ecs_cluster" "existing" {
  cluster_name = "mozart-lab-cluster"
}


# Access them in a local variable (easier reference)
locals {
  ssm_parameters = {
    "AWS_REGION"           = "/mozart-lab/AWS_REGION"
    "APNS_ENVIRONMENT"     = "/mozart-lab/APNS_ENVIRONMENT"
    "REDIS_MAX_CONNECTION" = "/mozart-lab/REDIS_MAX_CONNECTION"
    "SMS_PROVIDER"         = "/mozart-lab/SMS_PROVIDER"
    "EMAIL_PROVIDER"       = "/mozart-lab/EMAIL_PROVIDER"
    "AWS_S3_BUCKET_NAME"   = "/mozart-lab/AWS_S3_BUCKET_NAME"
    "TWILIO_ACCOUNT_SID"   = "/mozart-lab/TWILIO_ACCOUNT_SID"
    "SMS_FROM_NUMBER"      = "/mozart-lab/SMS_FROM_NUMBER"
    "AWS_FROM_EMAIL"       = "/mozart-lab/AWS_FROM_EMAIL"
  }
}

data "aws_ssm_parameter" "app_config" {
  for_each = local.ssm_parameters
  name     = each.value
}

data "aws_s3_bucket" "lab_mozart_artifacts_bkt" {
  bucket = "lab-mozart-assets-bucket"
}

data "aws_ecr_repository" "admin_dashboard_ecr" {
  name = "admin-dashboard"
}

data "aws_elasticache_replication_group" "redis-endpoint" {
  replication_group_id = "mozart-redis-lab"
}

data "aws_secretsmanager_secret" "rds_secret" {
  name = "/mozart-lab/DB_PASSWORD-LAB"
}

data "aws_secretsmanager_secrets" "all_secrets" {
  filter {
    name   = "name"
    values = ["lab"]
  }
}

data "aws_rds_cluster" "rds" {
  cluster_identifier = "mozart-db-lab-cluster"
}