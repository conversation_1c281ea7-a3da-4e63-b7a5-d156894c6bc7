
resource "aws_ecs_task_definition" "mozart_worker" {
  family                   = "${var.app_name}-mozart-worker-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["EC2"]
  cpu                      = var.cpu
  memory                   = var.memory
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn

  container_definitions = jsonencode([
    {
      name      = "${var.app_name}-mozart-worker"
      image     = "${var.repository_url}:${var.image_tag}"
      essential = true
      command   = ["swift", "run", "App", "queues"]
      portMappings = []
      environment = var.environment_variables
      secrets     = var.secrets
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/${var.app_name}-mozart-worker"
          "awslogs-region"        = "ap-southeast-2"
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])

  tags = {
    Name = "${var.app_name}-mozart-worker-task-definition"
  }
}

resource "aws_cloudwatch_log_group" "mozart_worker" {
  name              = "/ecs/${var.app_name}-mozart-worker"
  retention_in_days = 30

  tags = {
    Name = "${var.app_name}-mozart-worker-log-group"
  }
}

resource "aws_ecs_service" "mozart_worker" {
  name            = "${var.app_name}-mozart-worker-service"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.mozart_worker.arn
  desired_count   = var.desired_count
  launch_type     = null

  capacity_provider_strategy {
    capacity_provider = aws_ecs_capacity_provider.ec2.name
    weight            = 100
    base              = 1
  }

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id]
    subnets          = var.woker_subnets
    assign_public_ip = false
  }

  lifecycle {
    ignore_changes = [
      task_definition
    ]
  }

  depends_on = [aws_ecs_task_definition.mozart_worker]

  tags = {
    Name = "${var.app_name}-mozart-worker-service"
  }
}