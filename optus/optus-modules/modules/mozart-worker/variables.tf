variable "app_name" {
  description = "Name of the application"
  type        = string
}

variable "desired_count" {
  description = "Desired number of tasks in the service"
  type        = number
  default     = 1
  
}

variable "worker_subnets" {
  description = "List of subnets for the service"
  type        = list(string)
  default     = ["subnet-0f3312b3c67f5540c", "subnet-0fd7ee0ab0d8798ea"]
  
}

variable "repository_url" {
  description = "ECR repository URL for Mozart Worker container images"
  type        = string
  default     = "961341535886.dkr.ecr.ap-southeast-2.amazonaws.com/mozart-worker"
  
}

variable "image_tag" {
  description = "Container image tag to deploy"
  type        = string
  default     = "latest"
}