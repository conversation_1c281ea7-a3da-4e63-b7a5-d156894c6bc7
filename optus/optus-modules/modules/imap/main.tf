
locals {
  ssm_parameters = {
    "AWS_REGION"           = "/mozart-lab/AWS_REGION"
    "APNS_ENVIRONMENT"     = "/mozart-lab/APNS_ENVIRONMENT"
    "REDIS_MAX_CONNECTION" = "/mozart-lab/REDIS_MAX_CONNECTION"
    "SMS_PROVIDER"         = "/mozart-lab/SMS_PROVIDER"
    "EMAIL_PROVIDER"       = "/mozart-lab/EMAIL_PROVIDER"
    "AWS_S3_BUCKET_NAME"   = "/mozart-lab/AWS_S3_BUCKET_NAME"
    "TWILIO_ACCOUNT_SID"   = "/mozart-lab/TWILIO_ACCOUNT_SID"
    "SMS_FROM_NUMBER"      = "/mozart-lab/SMS_FROM_NUMBER"
    "AWS_FROM_EMAIL"       = "/mozart-lab/AWS_FROM_EMAIL"
    "DB_CONNECTION"        = "/mozart-lab/DB_CONNECTION"
    "REDIS_CONNECTION"     = "/mozart-lab/REDIS_CONNECTION"
  }
#     secret_arns = {
#     AAI_API_KEY            = "/mozart-lab/AAI_API_KEY_LABB-E7i9it"
#     AAI_HOSTNAME           = "/mozart-lab/AAI_HOSTNAME_LABB-15bCor"
#     AAI_WEBHOOK_URL        = "/mozart-lab/AAI_WEBHOOK_URL_LABB-jTOS0m"
#     APNS_KEY               = "/mozart-lab/APNS_KEY_LABB-H1DHkp"
#     JWT_SECRET             = "/mozart-lab/JWT_SECRET_LABB-eRv1Jk"
#     AUTH_PASSWORD          = "/mozart-lab/AUTH_PASSWORD_LABB-H1DHkp"
#     TWILIO_AUTH_TOKEN      = "/mozart-lab/TWILIO_AUTH_TOKEN_LABB-83PZHq"
#   }
  secret_arns = {
    AAI_API_KEY            = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/AAI_API_KEY_LABB-E7i9it"
    AAI_HOSTNAME           = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/AAI_HOSTNAME_LABB-15bCor"
    AAI_WEBHOOK_URL        = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/AAI_WEBHOOK_URL_LABB-jTOS0m"
    APNS_KEY               = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/APNS_KEY_LABB-H1DHkp"
    JWT_SECRET             = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/JWT_SECRET_LABB-eRv1Jk"
    AUTH_PASSWORD          = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/AUTH_PASSWORD_LABB-H1DHkp"
    TWILIO_AUTH_TOKEN      = "arn:aws:secretsmanager:ap-southeast-2:************:secret:/mozart-lab/TWILIO_AUTH_TOKEN_LABB-83PZHq"
  }
}

# data "aws_ssm_parameter" "app_config_local" {
#   for_each = local.ssm_parameters
#   name     = each.value
# }

data "aws_ssm_parameter" "app_config" {
  for_each = local.ssm_parameters
  name     = each.value
}

data "aws_secretsmanager_secret" "rds_secret" {
  name = "/mozart-lab/DB_PASSWORD-LAB"
}

data "aws_secretsmanager_secrets" "all_secrets" {
  filter {
    name   = "name"
    values = ["lab"]
  }
}

#Fetch each secret
data "aws_secretsmanager_secret_version" "secrets" {
  for_each  = local.secret_arns
  secret_id = each.value
}

#Extract values into a usable map
locals {
  secret_values = {
    for key, secret in data.aws_secretsmanager_secret_version.secrets :
    key => secret.secret_string
  }
}


resource "aws_iam_role" "ecs_task_execution_role" {
  name = "${var.app_name}-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_role_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}


resource "aws_iam_role" "ecs_task_role" {
  name = "${var.app_name}-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "task_role_secrets_manager_policy" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.secrets_manager_access.arn
}


#--- IMAP Server Resources ---

resource "aws_ecs_task_definition" "imap_server" {
  family                   = "${var.app_name}-server-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["EC2"]
  cpu                      = var.cpu    # Using general CPU/memory, adjust if needed
  memory                   = var.memory # Using general CPU/memory, adjust if needed
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn # Assuming same task role is sufficient

  container_definitions = jsonencode([
    {
      name      = "${var.app_name}-server"
      image     = "${var.repository_url}:${var.image_tag}" # Assumes same image as mozart/worker
      essential = true
      portMappings = [
        {
          containerPort = var.imap_server_container_port
          # hostPort is not needed for awsvpc with NLB using IP targets
        }
      ]
      environment = var.environment_variables # Consider if imap-server needs different env vars
      secrets = concat(
        # Non-sensitive parameters from SSM Parameter Store
#         [for key in keys(local.ssm_parameters) : {
#     name      = key
#     valueFrom = data.aws_ssm_parameter.app_config_local[key].arn
#   }],
    [
      for key in keys(data.aws_ssm_parameter.app_config) : {
        name      = key
        valueFrom = data.aws_ssm_parameter.app_config[key].arn
      }  
    ],
    # Sensitive values from Secrets Manager
    # [
    #       for i in range(length(data.aws_secretsmanager_secrets.all_secrets.names)) : {
    #         name      = data.aws_secretsmanager_secrets.all_secrets.names[i]
    #         valueFrom = data.aws_secretsmanager_secrets.all_secrets.arns[i]
    #       }
    # ],
    [
      for key, arn in local.secret_arns : {
        name      = key
        valueFrom = arn
      }
    ]

      )            # Consider if imap-server needs different secrets
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/${var.app_name}-server"
          "awslogs-region"        = "ap-southeast-2" # Hardcoded region
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])

  tags = {
    Name = "${var.app_name}-server-task-definition"
  }
}

resource "aws_cloudwatch_log_group" "imap_server" {
  name              = "/ecs/${var.app_name}-server"
  retention_in_days = 30 # Or as desired

  tags = {
    Name = "${var.app_name}-server-log-group"
  }
}

resource "aws_lb" "imap_nlb" {
  name               = "${var.app_name}-nlb"
  internal           = false
  load_balancer_type = "network"
  subnets            = var.alb_subnets # NLB needs to be in public subnets to be internet-facing

  enable_cross_zone_load_balancing = true

  tags = {
    Name = "${var.app_name}-nlb"
  }
}

resource "aws_lb_target_group" "imap_server" {
  name        = "${var.app_name}-tg"
  port        = var.imap_server_container_port
  protocol    = "TCP"
  vpc_id      = var.vpc_id
  target_type = "ip"

  health_check {
    enabled             = true
    protocol            = "TCP" # Basic TCP health check
    port                = tostring(var.imap_server_container_port)
    healthy_threshold   = 3
    unhealthy_threshold = 3
    interval            = 30
  }

  tags = {
    Name = "${var.app_name}-server-tg"
  }
}

resource "aws_lb_listener" "imap_server" {
  load_balancer_arn = aws_lb.imap_nlb.arn
  port              = var.imap_server_container_port
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.imap_server.arn
  }
}

resource "aws_ecs_service" "imap_server" {
  name            = "${var.app_name}-server-service"
  cluster         = var.ecs_cluster
  task_definition = aws_ecs_task_definition.imap_server.arn
  desired_count   = var.imap_server_desired_count
  #launch_type     = null # Using capacity provider

  capacity_provider_strategy {
    capacity_provider = aws_ecs_capacity_provider.ec2.name
    weight            = 100
    base              = 1
  }

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id] # Reuses existing SG, added rule for imap port
    subnets          = var.subnets # Private subnets
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.imap_server.arn
    container_name   = "${var.app_name}-server"
    container_port   = var.imap_server_container_port
  }

  depends_on = [aws_lb_listener.imap_server, aws_ecs_task_definition.imap_server,aws_ecs_capacity_provider.ec2]

  tags = {
    Name = "${var.app_name}-server-service"
  }
}



resource "aws_ecs_capacity_provider" "ec2" {
  name = "${var.app_name}-ec2-capacity-provider"

  auto_scaling_group_provider {
    auto_scaling_group_arn = aws_autoscaling_group.ecs.arn
    
    managed_scaling {
      maximum_scaling_step_size = 1000
      minimum_scaling_step_size = 1
      status                    = "ENABLED"
      target_capacity           = 100
    }
  }
}

resource "aws_autoscaling_group" "ecs" {
  name                 = "${var.app_name}-ecs-asg"
  vpc_zone_identifier  = var.subnets
#   launch_configuration = aws_launch_template.ecs.name
  min_size             = 1
  max_size             = 8
  desired_capacity     = 2

  launch_template {
    id      = aws_launch_template.ecs.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "${var.app_name}-ecs-instance"
    propagate_at_launch = true
  }

  tag {
    key                 = "AmazonECSManaged"
    value               = ""
    propagate_at_launch = true
  }
}


resource "aws_launch_template" "ecs" {
  name_prefix   = "${var.app_name}-ecs-lt"
  image_id      = var.ecs_ami_id
  instance_type = var.instance_type
#   key_name      = var.key_name

  iam_instance_profile {
    name = aws_iam_instance_profile.ec2_instance_profile.name
  }

  user_data = base64encode(<<-EOF
              #!/bin/bash
              echo ECS_CLUSTER=${var.ecs_cluster} >> /etc/ecs/ecs.config
              EOF
  )

  network_interfaces {
    associate_public_ip_address = true
    security_groups             = [aws_security_group.instance.id]
  }
}

# resource "aws_launch_configuration" "ecs" {
#   name_prefix          = "${var.app_name}-ecs-"
#   image_id             = data.aws_ami.ecs_optimized.id
#   instance_type        = "t3.medium"
#   security_groups      = [aws_security_group.instance.id]
#   iam_instance_profile = aws_iam_instance_profile.ec2_instance_profile.name
#   key_name             = null
#   user_data            = base64encode(<<-EOF
#               #!/bin/bash
#               echo ECS_CLUSTER=${var.ecs_cluster} >> /etc/ecs/ecs.config
#               EOF
#   )

#   lifecycle {
#     create_before_destroy = true
#   }
# }

resource "aws_iam_instance_profile" "ec2_instance_profile" {
  name = "${var.app_name}-ec2-instance-profile"
  role = aws_iam_role.ec2_instance_role.name
}

resource "aws_iam_role_policy_attachment" "ecs_instance_role_policy" {
  role       = aws_iam_role.ec2_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

resource "aws_iam_role" "ec2_instance_role" {
  name = "${var.app_name}-ec2-instance-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_security_group" "ecs_tasks" {
  name        = "${var.app_name}-ecs-tasks-sg"
  description = "Allow inbound access from the ALB only"
  vpc_id      = var.vpc_id

  ingress {
    protocol        = "tcp"
    from_port       = var.container_port
    to_port         = var.container_port
    security_groups = [aws_security_group.alb.id]
  }

  ingress {
    protocol        = "tcp"
    from_port       = var.container_port
    to_port         = var.container_port
    security_groups = [aws_security_group.instance.id]
  }

  # Allow inbound TCP for imap-server port from public for NLB access
  ingress {
    protocol    = "tcp"
    from_port   = var.imap_server_container_port  # 8080
    to_port     = var.imap_server_container_port  # 8080
    cidr_blocks = ["0.0.0.0/0"]  # Allow from anywhere for NLB health checks
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${var.app_name}-ecs-tasks-sg"
  }
}

resource "aws_security_group" "alb" {
  name        = "${var.app_name}-alb-sg"
  description = "Allow HTTP inbound traffic"
  vpc_id      = var.vpc_id

  ingress {
    protocol    = "tcp"
    from_port   = 80
    to_port     = 80
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 8080
    to_port     = 8080
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 443
    to_port     = 443
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${var.app_name}-alb-sg"
  }
}

# resource "aws_security_group_rule" "allow_from_instance_to_tasks" {
#   type                     = "ingress"
#   from_port                = var.imap_server_container_port
#   to_port                  = var.imap_server_container_port
#   protocol                 = "tcp"
#   security_group_id        = aws_security_group.ecs_tasks.id
#   source_security_group_id = aws_security_group.instance.id
# }

resource "aws_security_group" "instance" {
  name        = "${var.app_name}-instance-sg"
  description = "Security group for EC2 instances in ECS cluster"
  vpc_id      = var.vpc_id

  ingress {
    protocol    = "tcp"
    from_port   = 22
    to_port     = 22
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 55060
    to_port     = 55060
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "udp"
    from_port   = 55060
    to_port     = 55060
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "udp"
    from_port   = 6060
    to_port     = 6060
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }
#   egress {
#   protocol        = "tcp"
#   from_port       = var.container_port
#   to_port         = var.container_port
#   security_groups = [aws_security_group.ecs_tasks.id]
#   description     = "Allow EC2 to reach ECS tasks"
# }

  tags = {
    Name = "${var.app_name}-instance-sg"
  }
}


# Add permissions for Secrets Manager
resource "aws_iam_policy" "secrets_manager_access" {
  name        = "${var.app_name}-secrets-manager-access"
  description = "Allow ECS tasks to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "ssm:GetParameters",
          "ssm:GetParameter",
          "ssm:DescribeParameters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_secrets_manager_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = aws_iam_policy.secrets_manager_access.arn
}

data "aws_ami" "ecs_optimized" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-ecs-hvm-*-x86_64-ebs"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# --- End IMAP Server Resources --- 