variable "app_name" {
  description = "Name of the application"
  type        = string
}

variable "vpc_id" {
  description = "ID of the VPC"
  type        = string
}

variable "alb_subnets" {
  description = "List of subnet IDs for the load balancer"
  type        = list(string)
}

variable "ecs_cluster" {
  description = "Name of the ECS cluster"
  type        = string
}

variable "subnets" {
  description = "List of subnet IDs for the ECS service (same as kamailio module)"
  type        = list(string)
  default     = ["subnet-06e973afc15d07937", "subnet-0cb1299ddd156c5c8"]  # Same as kamailio module
}

variable "capacity_provider_name" {
  description = "Name of the capacity provider to use"
  type        = string
  default     = "kamailio-lab-ec2-capacity-provider"
}