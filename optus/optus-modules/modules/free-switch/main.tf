# ====================== Freeswitch resources =========================

# Create SSM parameters for Freeswitch configuration
resource "aws_ssm_parameter" "freeswitch_config" {
  for_each = {
    # Non-sensitive environment variables for freeswitch container
    "PATH"           = "/usr/local/freeswitch/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
    "GATEWAY"        = "**************"
    "VM_MAX_WORKERS"  = "16"
    "MOZART_SERVER" = "http://mozart-alb-1142825309.ap-southeast-2.elb.amazonaws.com"
    "AFFINITY" = "Freeswitch"
    "UPLOAD_MEDIA_SERVER"   = "s3-ap-southeast-2.amazonaws.com"
    "LD_LIBRARY_PATH" = "/usr/local/src/freeswitch/libs/speechsdk/lib/x64"
    "UPLOAD_MEDIA_BUCKET"   = "mozart-assets-bucket"
    "UPLOAD_TO_MOZART"      = "YES"
    "DELETE_LOCAL_VM"       = "NO"
  }

  name        = "/${var.app_name}/${each.key}"
  description = "Freeswitch application configuration parameter"
  type        = "String"
  value       = each.value

  tags = {
    Name = "${var.app_name}-${each.key}"
  }
}

#Get all secrets from AWS Secrets Manager
data "aws_secretsmanager_secrets" "all" {}

locals {
  all_secret_pairs = flatten([
    for name in tolist(data.aws_secretsmanager_secrets.all.names) : [
      for arn in tolist(data.aws_secretsmanager_secrets.all.arns) : {
        name = name
        arn  = arn
      } if endswith(arn, name)
    ]
  ])

  lab_secrets = [
    for s in local.all_secret_pairs : s
    if can(regex("freeswitch", lower(s.name))) && s.arn != null && s.arn != ""
  ]
}

# Create secrets for freeswitch server
# resource "aws_secretsmanager_secret" "freeswitch_secrets" {
#   for_each = {
#     "AWS_ACCESS_KEY"        = "aws-access-key"
#     "MOZART_AUTH_TOKEN"     = "mozart-auth-token"
#     "AWS_SECRET_ACCESS_KEY" = "aws-secret-access-key"
#   }

#   name = "/${var.app_name}/${each.key}"
  
#   tags = {
#     Name = "${var.app_name}-${each.key}"
#   }
# }

resource "aws_iam_policy" "freeswitch_secrets_access" {
  name        = "${var.app_name}-secrets-access"
  description = "Allow Freeswitch ECS tasks to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "ssm:GetParameters",
          "ssm:GetParameter",
          "ssm:DescribeParameters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_security_group" "kamailio_nlb" {
  name        = "kamailio-nlb-sg"
  description = "Allow SIP traffic for kamailio server"
  vpc_id      = var.vpc_id

  ingress {
    protocol    = "udp"
    from_port   = 55060
    to_port     = 55060
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 55060
    to_port     = 55060
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "kamailio-nlb-sg"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_security_group" "freeswitch_nlb" {
  name        = "${var.app_name}-nlb-sg"
  description = "Allow SIP traffic for Freeswitch server"
  vpc_id      = var.vpc_id

  ingress {
    protocol    = "udp"
    from_port   = 6060
    to_port     = 6060
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 6060
    to_port     = 6060
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${var.app_name}-nlb-sg"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb" "freeswitch" {
  name               = "${var.app_name}-nlb"
  internal           = false
  load_balancer_type = "network"
  security_groups    = [aws_security_group.freeswitch_nlb.id]
  subnets            = var.alb_subnets

  tags = {
    Name = "${var.app_name}-nlb"
  }
}

resource "aws_security_group" "freeswitch" {
  name        = "${var.app_name}-sg"
  description = "Allow inbound access from the NLB only for freeswitch server"
  vpc_id      = var.vpc_id

  ingress {
    protocol        = "udp"
    from_port       = 6060
    to_port         = 6060
    security_groups = [aws_security_group.kamailio_nlb.id,aws_security_group.freeswitch_nlb.id]
  }

  ingress {
    protocol    = "tcp"
    from_port   = 6060
    to_port     = 6060
    security_groups = [aws_security_group.kamailio_nlb.id,aws_security_group.freeswitch_nlb.id]
  }

  egress {
    protocol    = "-1"
    from_port   = 0
    to_port     = 0
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "${var.app_name}-sg"
  }
}


resource "aws_lb_target_group" "freeswitch" {
  name        = "${var.app_name}-target-group"
  port        = 6060
  protocol    = "UDP"
  vpc_id      = var.vpc_id
  target_type = "ip"  # Changed from "instance" to "ip" for bridge networking

  health_check {
    protocol            = "TCP"
    healthy_threshold   = 3
    unhealthy_threshold = 3
    timeout             = 30
    interval            = 60
    port                = 6060
  }

  tags = {
    Name = "${var.app_name}-target-group"
  }
  
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lb_listener" "freeswitch" {
  load_balancer_arn = aws_lb.freeswitch.arn
  port              = 6060
  protocol          = "UDP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.freeswitch.arn
  }
}

resource "aws_iam_instance_profile" "ec2_instance_profile" {
  name = "${var.app_name}-ec2-instance-profile"
  role = aws_iam_role.ec2_instance_role.name
}

resource "aws_iam_role_policy_attachment" "ecs_instance_role_policy" {
  role       = aws_iam_role.ec2_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

resource "aws_iam_role" "ec2_instance_role" {
  name = "${var.app_name}-ec2-instance-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
}


resource "aws_ecs_task_definition" "freeswitch" {
  family                   = "${var.app_name}-task-new"
  network_mode             = "awsvpc"  # or "host" if using host networking
  requires_compatibilities = ["EC2"]   # or ["FARGATE"] if using Fargate
  execution_role_arn       = aws_iam_role.freeswitch_execution_role.arn
  task_role_arn            = aws_iam_role.freeswitch_task_role.arn
  cpu                      = 2048      # 2 vCPU
  memory                   = 3096      # 4GB memory

  container_definitions = jsonencode([{
    name      = "${var.app_name}"
    image     = "961341535886.dkr.ecr.ap-southeast-2.amazonaws.com/freeswitch-optus-voicemail:0.9.0"
    essential = true

    portMappings = [
      {
          "containerPort": 6060,
          "hostPort": 6060,
          "protocol": "udp"
      }
    ]

    secrets = concat(
        # Non-sensitive parameters from SSM Parameter Store
        [
          for key in keys(aws_ssm_parameter.freeswitch_config) : {
            name      = key
            valueFrom = aws_ssm_parameter.freeswitch_config[key].arn
          }
        ],
        # Sensitive values from Secrets Manager
        [
      for secret in local.lab_secrets : {
        name      = secret.name
        valueFrom = secret.arn
      }
      if secret.arn != null && secret.arn != ""
    ]
        # [
        #   for key in keys(aws_secretsmanager_secret.freeswitch_secrets) : {
        #     name      = key
        #     valueFrom = aws_secretsmanager_secret.freeswitch_secrets[key].arn
        #   }
        # ]
      )

    logConfiguration = {
      logDriver = "awslogs"
      options = {
        awslogs-group         = "/ecs/${var.app_name}"
        awslogs-region        = "ap-southeast-2"
        awslogs-stream-prefix = "${var.app_name}"
      }
    }
  }])
}

resource "aws_ecs_service" "freeswitch" {
  name            = "${var.app_name}-service"
  cluster         = var.ecs_cluster
  task_definition = aws_ecs_task_definition.freeswitch.arn
  desired_count   = 1
  #launch_type     = null

  capacity_provider_strategy {
    capacity_provider = var.capacity_provider_name  # Use configurable capacity provider
    weight            = 100
    base              = 1
  }

  network_configuration {
    security_groups  = [aws_security_group.freeswitch.id]  # Use the correct security group
    subnets          = var.subnets  # Use the subnets variable directly
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.freeswitch.arn
    container_name   = "${var.app_name}"
    container_port   = 6060
  }

  lifecycle {
    ignore_changes = [
      task_definition     # ← prevents roll‑backs
    ]
  }

  depends_on = [aws_lb_listener.freeswitch]

  tags = {
    Name = "${var.app_name}-service"
  }
}


# Supporting Resources

resource "aws_iam_role" "freeswitch_execution_role" {
  name = "ecs-${var.app_name}-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

resource "aws_iam_role_policy_attachment" "freeswitch_secrets_policy" {
  role       = aws_iam_role.freeswitch_execution_role.name
  policy_arn = aws_iam_policy.freeswitch_secrets_access.arn
}

resource "aws_iam_role_policy_attachment" "freeswitch_execution_role_policy" {
  role       = aws_iam_role.freeswitch_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_role" "freeswitch_task_role" {
  name = "ecs-${var.app_name}-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action    = "sts:AssumeRole"
      Effect    = "Allow"
      Principal = {
        Service = "ecs-tasks.amazonaws.com"
      }
    }]
  })
}

resource "aws_cloudwatch_log_group" "freeswitch" {
  name              = "/ecs/${var.app_name}"
  retention_in_days = 30
}

#========================= The end of Freeswitch resources ===================