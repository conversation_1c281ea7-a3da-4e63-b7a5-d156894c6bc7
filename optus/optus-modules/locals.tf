# #Get all secrets from AWS Secrets Manager
# data "aws_secretsmanager_secrets" "all" {}

# locals {
#   all_secrets = [
#     for name in data.aws_secretsmanager_secrets.all.names : {
#       name = name
#       arn  = one([for arn in data.aws_secretsmanager_secrets.all.arns : arn if endswith(arn, name)])
#     }
#   ]

#   lab_secrets = [
#     for secret in local.all_secrets : secret
#     if can(regex("lab", lower(secret.name)))
#   ]
# }

# # output "filtered_lab_secrets" {
# #   value = local.lab_secrets
# # }

# #Parameter Store
# data "aws_ssm_parameters_by_path" "all" {
#   path           = "/"
#   recursive      = true
#   with_decryption = true
# }

# data "aws_ssm_parameter" "lab_params" {
#   for_each = {
#     for name in data.aws_ssm_parameters_by_path.all.names :
#     name => name
#     if can(regex("lab", lower(name)))
#   }

#   name = each.value
# }

# output "lab_parameter_names" {
#   value = [for p in data.aws_ssm_parameter.lab_params : p.name]
# }

# #include secrets and parameters into the container definition
# secrets = concat(
#   [
#     for key in keys(data.aws_ssm_parameter.lab_params) : {
#       name      = key
#       valueFrom = data.aws_ssm_parameter.lab_params[key].arn
#     }
#   ],
#   [
#     for key in keys(data.aws_ssm_parameter.app_config) : {
#       name      = key
#       valueFrom = data.aws_ssm_parameter.app_config[key].arn
#     }
#   ],
#   [
#     for secret in local.lab_secrets : {
#       name      = secret.name
#       valueFrom = secret.arn
#     }
#   ]
# )