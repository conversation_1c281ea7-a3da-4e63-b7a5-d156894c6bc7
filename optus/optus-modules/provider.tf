provider "aws" {
  profile = "Optus"
  region  = "ap-southeast-2"
  default_tags {
    tags = {
      o_b_bu         = "networks"
      o_b_pri-own    = "<PERSON> Lam"
      o_b_bus-own    = "<PERSON>"
      o_t_app-plat   = "networks"
      o_t_app        = "voicemail"
      o_t_env        = "poc"
      o_t_app-own    = "<PERSON>"
      o_t_tech-own   = "Serena Feng"
      o_b_cc         = "gk881infra"
      o_s_app-class  = "cat2"
      o_b_project    = "VMS"
      o_s_data-class = "conf_non_pii"
      o_t_app-role   = "app"
      o_a_avail      = "24x7"
      o_s_sra        = "00642"
      o_t_dep-mthd   = "hybrid"
      o_t_lifecycle  = "inbuild"
    }
  }
}

# data "aws_availability_zones" "available" {
#   state = "available"
# }

# locals {
#   selected_azs = slice(data.aws_availability_zones.available.names, 0, 2)

# }