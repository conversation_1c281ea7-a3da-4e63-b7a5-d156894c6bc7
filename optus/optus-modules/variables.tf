variable "environment" {
  default = "norwood-lab"
}

variable "app_name_admin_dashboard" {
  default = "admin-dashboard-lab"
}

variable "app_name_imap" {
  default = "imap-lab"
}

variable "app_name_imap_test" {
  default = "imap-test-lab"
}

variable "app_name_freeswitch" {
  default = "freeswitch-lab"
}

variable "alb_subnets" {
  #default = ["subnet-06e7470e43d97cbc0", "subnet-04c53e37b36d59ed6"]
  default = ["subnet-0e42ed580f3069fae", "subnet-0f26eace80a2d48df"] #public subnets
}

variable "image_tag" {
  description = "Container image tag to deploy"
  type        = string
  default     = "latest"
}

variable "admin_dashboard_image_tag" {
  description = "Admin dashboard container image tag to deploy"
  type        = string
  default     = "latest"
}