# Cloud Deployment Repository

This repository contains all cloud deployment setups and configurations for various projects.

## Repository Structure

Each folder contains all necessary resources, configurations, and instructions for deploying applications to specific cloud environments:

### [optus](./optus)

The `optus` directory contains all resources for deploying the Mozart Vapor application to AWS:

- **Terraform configurations** for infrastructure as code
- **Deployment scripts** for automating the provisioning process
- **Documentation** on setup and maintenance procedures

Infrastructure components include:
- Amazon ECR for Docker images
- Amazon ECS (EC2 mode) for running containers
- Amazon Aurora PostgreSQL database
- Amazon ElastiCache for Redis
- Application Load Balancer
- VPC and network setup

## Getting Started

1. Navigate to the specific deployment folder you're interested in
2. Follow the README instructions in that folder for detailed setup steps
3. Use the provided scripts to deploy and manage your infrastructure

## Prerequisites

Most deployments require:
- Cloud provider CLI tools
- Terraform
- Docker
- Appropriate access credentials

## Contributing

When adding new deployment setups, please:
1. Create a dedicated folder for each cloud environment/client
2. Include a detailed README with deployment instructions
3. Add necessary scripts and Terraform configurations
4. Update this main README with a brief description of the new setup
