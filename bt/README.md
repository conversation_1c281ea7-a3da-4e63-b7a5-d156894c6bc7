


# BT Deployment

This deployment contains all cloud deployment set ups to deploy BT cluster on Azure using Terraform and K8.

## Repo Structure

The deployment has been section off into different parts:
- terraform_inf contains all the config files to deploy infrastructure necessary to run K8 on Azure. Different infrastructure components have been set up as modules under the module folder under this directory
- k8 contains manifest files to deploy required pods

Auxiliary scripts:
- deploy_tf.sh : this script will set up the necessary Azure infrastructure
   - Key Vault, 
   - ACR(Azure Container Registry), 
   - AKS(Azure K8 Service)
   - Log Analytics
- copy_kv_2_kv.sh : This scripts copies secrets from one Key Vault to another.
- deploy_k8.sh : Creates the K8 cluster and sets up some of the roles so that AKS perform necessary functions.

## Getting Started

1. Navigate to the specific deployment folder you're interested in
2. Follow the README instructions in that folder for detailed setup steps
3. Use the provided scripts to deploy and manage your infrastructure

## Prerequisites

This deployments requires:
- Terraform
- Docker
- Kubectl
- Azure CLI
- Appropriate access credentials
- Helm

## Deployment Steps

The order in which scripts need to be executed and supplementary steps so that deployment can be successful:

1. deploy_tf.sh - this script is run from the bt directory
2. copy_kv_2_kv.sh UNLESS you are manually adding secrets etc
3. You might want to also import images from another ACR if that is required. Here is the structure of the command that needs to be run:
   az acr import \
     --name <target-acr-name> \
     --source <source-acr-name>.azurecr.io/<repository>:<tag> \
     --image <repository>:<tag>
4. deploy_k8.sh - this script is run from the bt directory
5. Run 'kubectl get pods' to check if the cluster has been deployed.
