#!/bin/bash

cd terraform_inf || { echo "Directory terraform_inf not found! Exiting."; exit 1; }

echo "Running terraform init..."
terraform init || { echo "Terraform init failed! Exiting."; exit 1; }

echo "Running terraform plan..."
terraform plan

read -p "Does everything look good? (yes/no): " confirmation

if [[ "$confirmation" == "yes" ]]; then
    echo "Running terraform apply..."
    terraform apply || { echo "Terraform apply failed! Exiting."; exit 1; }
else
    echo "Terraform apply was canceled."
    exit 0
fi

echo "DON'T FORGET TO POPULATE THE KEY VAULT BEFORE RUNNING THE K8 SCRIPT!!!!!!"
