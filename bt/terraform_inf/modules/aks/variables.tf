variable "name" {
  type = string
}

variable "location" {
  type = string
}

variable "resource_group_name" {
  type = string
}

variable "log_analytics_workspace_id" {
  type = string
}

variable "key_vault_id" {
  type = any
}

variable "acr_id" {
  type = any
}

variable "node_count" {
  description = "The number of nodes in the default node pool."
  type        = number
}

variable "vm_size" {
  description = "The size of the VMs in the default node pool."
  type        = string
}

variable "min_count" {
  description = "The minimum number of nodes in the default node pool."
  type        = number
}

variable "max_count" {
  description = "The maximum number of nodes in the default node pool."
  type        = number
}

