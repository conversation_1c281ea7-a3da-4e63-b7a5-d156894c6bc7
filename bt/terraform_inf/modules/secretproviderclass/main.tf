resource "kubernetes_manifest" "azure_keyvault_secretprovider" {
  manifest = {
    apiVersion = "secrets-store.csi.x-k8s.io/v1"
    kind       = "SecretProviderClass"
    metadata = {
      name = "azure-keyvault-secrets"
      annotations = {
        "secrets-store.csi.x-k8s.io/syncSecret.enabled" = "true"
      }
    }
    spec = {
      provider = "azure"
      parameters = {
        usePodIdentity          = "false"
        useVMManagedIdentity    = "true"
        keyvaultName            = var.keyvault_name
        tenantId                = var.tenant_id
        objects = var.objects
      }
      secretObjects = var.secret_objects
    }
  }
}