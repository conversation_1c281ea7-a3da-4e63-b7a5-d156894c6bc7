---

apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: azure-keyvault-ic-bt-secrets
  annotations:
      secrets-store.csi.x-k8s.io/syncSecret.enabled: "true"
#  namespace: default
spec:
  provider: azure
  parameters:
    usePodIdentity: "false"
    useVMManagedIdentity: "true"
    userAssignedIdentityID: "69890fea-3227-4de6-ab78-9ce011c6cacc"
    keyvaultName: "btopenspan-keyvault-us"
    tenantId: "392a33c8-012b-4d32-8e1f-3e316fa0dfad"
    objects: |
      array:
        - |
          objectName: aws-access-key-id
          objectType: secret
        - |
          objectName: fraud-detection-password
          objectType: secret
        - |
          objectName: aws-dashboard-client-id
          objectType: secret
        - |
          objectName: aws-dashboard-client-sec
          objectType: secret
        - |
          objectName: aws-secret-access-key
          objectType: secret
        - |
          objectName: azure-api-key
          objectType: secret
        - |
          objectName: azure-tts-key
          objectType: secret
        - |
          objectName: calendar-account-id
          objectType: secret
        - |
          objectName: calendar-id
          objectType: secret
        - |
          objectName: cognito-admin-userpool-id
          objectType: secret
        - |
          objectName: deepgram-api-key
          objectType: secret
        - |
          objectName: firebase-api-key
          objectType: secret
        - |
          objectName: freeswitch-credentials
          objectType: secret
        - |
          objectName: google-api-key
          objectType: secret
        - |
          objectName: ideal-postcodes-api-key
          objectType: secret
        - |
          objectName: ivr-callscreener-private-key
          objectType: secret
        - |
          objectName: ivr-callscreener-pub-key
          objectType: secret
        - |
          objectName: mod-aai-transcription-token
          objectType: secret
        - |
          objectName: mod-audio-docker-token
          objectType: secret
        - |
          objectName: nylas-token
          objectType: secret
        - |
          objectName: openai-api-key
          objectType: secret
        - |
          objectName: security-token
          objectType: secret
        - |
          objectName: speech-service-apikey
          objectType: secret
        - |
          objectName: twilio-account-sid
          objectType: secret
        - |
          objectName: twilio-auth-token
          objectType: secret
        - |
          objectName: websocket-protocol
          objectType: secret
        - |
          objectName: redis-password
          objectType: secret
        - |
          objectName: ivr-azure-certificate
          objectType: secret
        - |
          objectName: ivr-azure-certificate
          objectType: secret
        - |
          objectName: ums-acs-connection-string
          objectType: secret
        - |
          objectName: ums-calendar-account-id
          objectType: secret
        - |
          objectName: ums-nylas-token
          objectType: secret
        - |
          objectName: ums-calendar-id
          objectType: secret
        - |
          objectName: ums-ideal-postcodes-api-key
          objectType: secret
        - |
          objectName: ums-aws-dashboard-client-id
          objectType: secret
        - |
          objectName: ums-aws-dashboard-client-sec
          objectType: secret
        - |
          objectName: ums-openai-api-key
          objectType: secret
        - |
          objectName: ums-twilio-account-sid
          objectType: secret
        - |
          objectName: ums-twilio-auth-token
          objectType: secret
        - |
          objectName: ums-aws-access-key-id
          objectType: secret
        - |
          objectName: ums-aws-secret-access-key
          objectType: secret
        - |
          objectName: ums-aws-session-token
          objectType: secret
        - |
          objectName: ums-google-api-key
          objectType: secret
        - |
          objectName: ums-apns-api-key
          objectType: secret
        - |
          objectName: ums-square-access-token
          objectType: secret
        - |
          objectName: ums-square-refresh-token
          objectType: secret
        - |
          objectName: fd-google-api-key
          objectType: secret
        - |
          objectName: fd-deepgram-api-key
          objectType: secret
        - |
          objectName: fd-mod-aai-transcription-token
          objectType: secret
        - |
          objectName: fd-security-token
          objectType: secret
        - |
          objectName: fd-speech-service-apikey
          objectType: secret      
        - |
          objectName: fd-websocket-protocol
          objectType: secret
        - |
          objectName: fd-mod-audio-docker-token
          objectType: secret
        - |
          objectName: fd-mongo-username
          objectType: secret
        - |
          objectName: fd-mongo-password
          objectType: secret
        - |
          objectName: groq-api-key
          objectType: secret
        - |
          objectName: ba-webhook-key
          objectType: secret
        - |
          objectName: ba-client-secret
          objectType: secret
        - |
          objectName: tts-api-key
          objectType: secret
        - |
          objectName: ba-client-id
          objectType: secret
        - |
          objectName: mrf-twilio-account-sid
          objectType: secret
        - |
          objectName: mrf-twilio-auth-token
          objectType: secret 
  secretObjects:
    - secretName: ic-k8s-secret
      type: Opaque
      data:
        - key: secret1-key
          objectName: aws-access-key-id
        - key: secret2-key
          objectName: aws-dashboard-client-id
        - key: secret3-key
          objectName: aws-dashboard-client-sec
        - key: secret4-key
          objectName: aws-secret-access-key
        - key: secret5-key
          objectName: azure-api-key
        - key: secret6-key
          objectName: azure-tts-key
        - key: secret7-key
          objectName: calendar-account-id
        - key: secret8-key
          objectName: calendar-id
        - key: secret9-key
          objectName: cognito-admin-userpool-id
        - key: secret10-key
          objectName: deepgram-api-key
        - key: secret11-key
          objectName: firebase-api-key
        - key: secret12-key
          objectName: freeswitch-credentials
        - key: secret13-key
          objectName: google-api-key
        - key: secret14-key
          objectName: ideal-postcodes-api-key
        - key: secret15-key
          objectName: ivr-callscreener-private-key
        - key: secret16-key
          objectName: ivr-callscreener-pub-key
        - key: secret17-key
          objectName: mod-aai-transcription-token
        - key: secret18-key
          objectName: mod-audio-docker-token
        - key: secret19-key
          objectName: nylas-token
        - key: secret20-key
          objectName: openai-api-key
        - key: secret21-key
          objectName: security-token
        - key: secret22-key
          objectName: speech-service-apikey
        - key: secret23-key
          objectName: twilio-account-sid
        - key: secret24-key
          objectName: twilio-auth-token
        - key: secret25-key
          objectName: websocket-protocol
        - key: secret26-key
          objectName: redis-password
        - key: secret27-key
          objectName: ums-acs-connection-string
        - key: secret28-key
          objectName: ums-calendar-account-id
        - key: secret29-key
          objectName: ums-nylas-token
        - key: secret30-key
          objectName: ums-calendar-id
        - key: secret31-key
          objectName: ums-ideal-postcodes-api-key
        - key: secret32-key
          objectName: ums-aws-dashboard-client-id
        - key: secret33-key
          objectName: ums-aws-dashboard-client-sec
        - key: secret34-key
          objectName: ums-openai-api-key
        - key: secret35-key
          objectName: ums-twilio-account-sid
        - key: secret36-key
          objectName: ums-twilio-auth-token
        - key: secret37-key
          objectName: ums-aws-access-key-id
        - key: secret38-key
          objectName: ums-aws-secret-access-key
        - key: secret39-key
          objectName: ums-aws-session-token
        - key: secret40-key
          objectName: ums-google-api-key
        - key: secret41-key
          objectName: ums-apns-api-key
        - key: secret42-key
          objectName: fraud-detection-password
        - key: secret43-key
          objectName: ums-square-access-token
        - key: secret44-key
          objectName: ums-square-refresh-token
        - key: secret45-key
          objectName: fd-google-api-key
        - key: secret46-key
          objectName: fd-deepgram-api-key
        - key: secret47-key
          objectName: fd-mod-aai-transcription-token
        - key: secret48-key
          objectName: fd-security-token
        - key: secret49-key
          objectName: fd-speech-service-apikey
        - key: secret50-key
          objectName: fd-websocket-protocol
        - key: secret51-key
          objectName: fd-mod-audio-docker-token
        - key: secret52-key
          objectName: fd-mongo-username
        - key: secret53-key
          objectName: fd-mongo-password
        - key: secret54-key
          objectName: groq-api-key
        - key: secret55-key
          objectName: ba-webhook-key
        - key: secret56-key
          objectName: ba-client-secret
        - key: secret57-key
          objectName: tts-api-key
        - key: secret58-key
          objectName: ba-client-id
        - key: secret59-key
          objectName: mrf-twilio-account-sid
        - key: secret60-key
          objectName: mrf-twilio-auth-token
    - secretName: ic-k8s-tls-secret
      type: kubernetes.io/tls
      data:      
        - key: tls.crt
          objectName: ivr-azure-certificate
        - key: tls.key
          objectName: ivr-azure-certificate

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: freeswitch-deployment
spec:
  selector:
    matchLabels:
      app: freeswitch
  replicas: 1
  template:
    metadata:
      labels:
        app: freeswitch
    spec:
      containers:
      - name: freeswitch-container
        #image: freeswitchfd.azurecr.io/frees-verizon-poc-2-azure:1.0
        #image: freeswitchfd.azurecr.io/frees-verizon-poc-2-azure:latest
        #image: freeswitchfd.azurecr.io/frees-verizon-poc-2-azure:latest
        image: btopenspanacrus.azurecr.io/freeswitch:latest
        imagePullPolicy: Always
        securityContext:
         capabilities:
          add:
            - SYS_NICE
        resources:
          limits:
            #cpu: "1"
            memory: "3Gi"
        env:
        - name: FREESWITCH_PUBLIC_IP
          value: "***************"
        - name: "PATH"
          value: "/usr/local/freeswitch/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
        - name: "GATEWAY"
          value: "**************"
        - name: "MOD_AUDIO_DOCKER_BUFFER_SECS"
          value: "2"
        - name: "MOD_AUDIO_DOCKER_FRAME_SIZE"
          value: "10"
        - name: "MOD_AAI_TRANSCRIPTION_SERVICE_THREADS"
          value: "2"
        - name: "GOOGLE_APPLICATION_CREDENTIALS"
          value: "/usr/local/freeswitch/freeswitch-api-7867f30c80d7.json"
        - name: "MOD_AUDIO_DOCKER_TOKEN"
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret51-key
        - name: "MOD_AUDIO_DOCKER_DISPLACE_AUDIO"
          value: "YES"
        - name: "AZURE_TTS_REGION"
          value: "eastus"
        - name: "MOD_AUDIO_AAI_BUFFER_SECS"
          value: "2"
        - name: "AZURE_TTS_KEY"
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret6-key
        - name: "MOD_AUDIO_DOCKER_THREADS"
          value: "2"
        - name: "AFFINITY"
          value: "Freeswitch"
        - name: "MOD_AAI_TRANSCRIPTION_FRAME_SIZE"
          value: "10"
        - name: "LD_LIBRARY_PATH"
          value: "/usr/local/src/freeswitch/libs/speechsdk/lib/x64"
        - name: "MOD_AUDIO_DOCKER_SUBPROTOCOL_NAME"
          value: "streaming"
        - name: "MOD_AUDIO_DOCKER_PLAY_AUDIO_DIRECTION"
          value: "A"
        - name: "MOD_AUDIO_DOCKER_SERVER"
          value: "*************:8081"
        - name: "GOOGLE_APPLICATION_API"
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret45-key
        - name: "MOD_AAI_TRANSCRIPTION_TOKEN"
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret45-key
        ports:
        - containerPort: 8085
          protocol: TCP
        - containerPort: 6065
          protocol: TCP
        - containerPort: 6066
          protocol: TCP
        - containerPort: 6065
          protocol: UDP
        - containerPort: 6066
          protocol: UDP
        - containerPort: 5060
          protocol: TCP
        - containerPort: 5061
          protocol: TCP
        - containerPort: 5060
          protocol: UDP
        - containerPort: 5061
          protocol: UDP
        - containerPort: 5080
          protocol: TCP
        - containerPort: 5081
          protocol: TCP
        - containerPort: 5080
          protocol: UDP
        - containerPort: 5081
          protocol: UDP
        - containerPort: 6060
          protocol: TCP
        - containerPort: 6061
          protocol: TCP
        - containerPort: 6060
          protocol: UDP
        - containerPort: 6061
          protocol: UDP
        - containerPort: 16384
          protocol: UDP
        - containerPort: 16385
          protocol: UDP
        - containerPort: 16386
          protocol: UDP
        - containerPort: 16387
          protocol: UDP
        - containerPort: 16388
          protocol: UDP
        - containerPort: 16389
          protocol: UDP
        - containerPort: 16390
          protocol: UDP
        - containerPort: 16391
          protocol: UDP
        - containerPort: 16392
          protocol: UDP
        - containerPort: 16393
          protocol: UDP
        - containerPort: 16394
          protocol: UDP
        - containerPort: 8080
          protocol: TCP
        - containerPort: 8082
          protocol: TCP
        - containerPort: 16395
          protocol: UDP
        - containerPort: 16396
          protocol: UDP
        - containerPort: 16397
          protocol: UDP
        - containerPort: 16398
          protocol: UDP
        - containerPort: 16399
          protocol: UDP
        - containerPort: 16400
          protocol: UDP
        - containerPort: 16401
          protocol: TCP
        - containerPort: 16402
          protocol: TCP
        - containerPort: 16403
          protocol: TCP
        - containerPort: 16404
          protocol: TCP
        - containerPort: 55060
          protocol: TCP
        - containerPort: 55060
          protocol: UDP
        volumeMounts:      # Correct placement
        - name: secrets-store-inline
          mountPath: "/mnt/secrets-store"  # Adjust path as needed
      volumes:            # Correct placement
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: "azure-keyvault-ic-bt-secrets"  # Ensure this matches your SecretProviderClass name

---


apiVersion: apps/v1
kind: Deployment
metadata:
  name: audio-docker-deployment
spec:
  selector:
    matchLabels:
      app: audio-docker
  replicas: 1
  template:
    metadata:
      labels:
        app: audio-docker
    spec:
      containers:
      - name: audio-docker-container
        image: btopenspanacrus.azurecr.io/mrf:latest
        imagePullPolicy: Always
        resources:
          limits:
            cpu: "1"
            memory: "3Gi"
        ports:
        - containerPort: 8081
        - containerPort: 2579
          protocol: TCP
        env:
        - name: AI_ENGINE_HOST_ADDRESS
          value: "ws://************:8766/"
          #value: "ws://fraud-detector-service:8007/"
          #value: "wss://1d7e-49-194-103-210.ngrok-free.app/"
        - name: ENABLED_GOOGLE_TTS
          value: "enabled"
        - name: DEEPGRAM_API_KEY
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret46-key
        - name: ENVIRONMENT
          value: "development"
        - name: FRAUD_ANNOUNCEMENT
          value: "Suspected fraud, please be cautious or hang up"
        - name: SECURITY_TOKEN
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret48-key
        - name: SPEECH_SERVICE_APIKEY
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret49-key
        - name: TRANSCRIPTION_SERVICE_PROVIDER
          value: "deepgram"
        - name: WEBSOCKET_PROTOCOL
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret50-key
        volumeMounts:      # Correct placement
        - name: secrets-store-inline
          mountPath: "/mnt/secrets-store"  # Adjust path as needed
      volumes:            # Correct placement
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: "azure-keyvault-ic-bt-secrets"  # Ensure this matches your SecretProviderClass name

#apiVersion: apps/v1
#kind: Deployment
#metadata:
#  name: audio-docker-deployment
#spec:
#  selector:
#    matchLabels:
#      app: audio-docker
#  replicas: 1
#  template:
#    metadata:
#      labels:
#        app: audio-docker
#    spec:
#      containers:
#      - name: audio-docker-container
#        #image: btopenspanacr.azurecr.io/mrf:latest
#        image: btopenspanacr.azurecr.io/mrf:awseks
#        imagePullPolicy: Always
#        resources:
#          limits:
#            cpu: "1"
#            memory: "3Gi"
#        ports:
#        - containerPort: 8081
#        - containerPort: 2579
#          protocol: TCP
#        env:
#        - name: AI_ENGINE_HOST_ADDRESS
#          value: "ws://************:8766/"
#          #value: "ws://fraud-detector-service:8007/"
#          #value: "wss://1d7e-49-194-103-210.ngrok-free.app/"
#        - name: "AIDA_DOCKER_HOST_ADDRESS"
#          #value: "http://aida-loadbalancer:8085"
#          value: "https://f8abe4f70648.ngrok.app"
#        - name: MEDIA_STREAM_PROVIDER
#          value: "TWILIO"
#        - name: EXPECTED_OUTPUT_CODEC
#          value: "MULAW"
#        - name: EXPECTED_INPUT_CODEC
#          value: "MULAW"
#        - name: ENABLE_SAVING_FILE
#          value: "disabled"
#        - name: ENABLED_GOOGLE_TTS
#          value: "enabled"
#        - name: DEEPGRAM_API_KEY
#          valueFrom:
#            secretKeyRef:
#              name: ic-k8s-secret
#              key: secret46-key
#        - name: TWILIO_ACCOUNT_SID
#          valueFrom:
#            secretKeyRef:
#              name: ic-k8s-secret
#              key: secret58-key
#        - name: TWILIO_AUTH_TOKEN
#          valueFrom:
#            secretKeyRef:
#              name: ic-k8s-secret
#              key: secret60-key
#        - name: ENVIRONMENT
#          value: "development"
#        - name: FRAUD_ANNOUNCEMENT
#          value: "Suspected fraud, please be cautious or hang up"
#        - name: SECURITY_TOKEN
#          valueFrom:
#            secretKeyRef:
#              name: ic-k8s-secret
#              key: secret48-key
#        - name: SPEECH_SERVICE_APIKEY
#          valueFrom:
#            secretKeyRef:
#              name: ic-k8s-secret
#              key: secret49-key
#        - name: TRANSCRIPTION_SERVICE_PROVIDER
#          value: "deepgram"
#        - name: WEBSOCKET_PROTOCOL
#          valueFrom:
#            secretKeyRef:
#              name: ic-k8s-secret
#              key: secret50-key
#        volumeMounts:      # Correct placement
#        - name: secrets-store-inline
#          mountPath: "/mnt/secrets-store"  # Adjust path as needed
#      volumes:            # Correct placement
#      - name: secrets-store-inline
#        csi:
#          driver: secrets-store.csi.k8s.io
#          readOnly: true
#          volumeAttributes:
#            secretProviderClass: "azure-keyvault-ic-bt-secrets" 



---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: multiagent-deployment
spec:
  selector:
    matchLabels:
      app: multiagent
  replicas: 1
  template:
    metadata:
      labels:
        app: multiagent
    spec:
      containers:
      - name: multiagent-container
        #image: freeswitchfd.azurecr.io/frees-verizon-poc-2-azure:1.0
        #image: freeswitchfd.azurecr.io/frees-verizon-poc-2-azure:latest
        image: btopenspanacrus.azurecr.io/multiagent:1.0.0
        imagePullPolicy: Always
        securityContext:
         capabilities:
          add:
            - SYS_NICE
        resources:
          limits:
            #cpu: "1"
            memory: "3Gi"
        env:
        - name: "BOOKING_AGENT_URL"
          value: "http://booking-assistant-loadbalancer:8008"
        - name: "GROQ_API_KEY"
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret54-key
        ports:
        - containerPort: 8001
          protocol: TCP
        volumeMounts:      # Correct placement
        - name: secrets-store-inline
          mountPath: "/mnt/secrets-store"  # Adjust path as needed
      volumes:            # Correct placement
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: "azure-keyvault-ic-bt-secrets"  # Ensure this matches your SecretProviderClass name

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: booking-assistant-deployment
spec:
  selector:
    matchLabels:
      app: booking-assistant
  replicas: 1
  template:
    metadata:
      labels:
        app: booking-assistant
    spec:
      containers:
      - name: booking-assistant-container
        #image: freeswitchfd.azurecr.io/frees-verizon-poc-2-azure:1.0
        #image: freeswitchfd.azurecr.io/frees-verizon-poc-2-azure:latest
        image: btopenspanacrus.azurecr.io/booking_assistant:1.0.1
        imagePullPolicy: Always
        securityContext:
         capabilities:
          add:
            - SYS_NICE
        resources:
          limits:
            #cpu: "1"
            memory: "2Gi"
        env:
        - name: "AUTHORIZATION_URL"
          value: "https://connect.squareup.com/oauth2/authorize?"
        - name: "TOKEN_URL"
          value: "https://connect.squareup.com/oauth2/token"
        - name: "MULTI_AGENT_URL"
          value: "http://multiagent-service:8001"
        - name: "DASHBOARD_URL"
          value: "http://4.254.93.43:8086"
        - name: "DEVELOPMENT_REDIRECT_URI"
          value: "https://bookingassistantus.norwoodsystems.com/auth/callback"
        - name: "DATABASE_URL"
          value: "sqlite:///./tokens.db"
        - name: "ENV"
          value: "sandbox"
        - name: "WEBHOOK_URL"
          value: "https://bookingassistantus.norwoodsystems.com/webhook/booking"
        - name: "WEBHOOK_KEY"
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret55-key
        - name: "CLIENT_SECRET"
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret56-key
        - name: "CLIENT_ID"
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret58-key
        ports:
        - containerPort: 8001
          protocol: TCP
        - containerPort: 5000
          protocol: TCP
        - containerPort: 8008
          protocol: TCP
        volumeMounts:      # Correct placement
        - name: secrets-store-inline
          mountPath: "/mnt/secrets-store"  # Adjust path as needed
      volumes:            # Correct placement
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: "azure-keyvault-ic-bt-secrets"  # Ensure this matches your SecretProviderClass name

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: tts-deployment
spec:
  selector:
    matchLabels:
      app: tts
  replicas: 1
  template:
    metadata:
      labels:
        app: tts
    spec:
      containers:
      - name: tts-container
        #image: freeswitchfd.azurecr.io/frees-verizon-poc-2-azure:1.0
        #image: freeswitchfd.azurecr.io/frees-verizon-poc-2-azure:latest
        image: btopenspanacrus.azurecr.io/tts:1.0.0
        imagePullPolicy: Always
        #command: ["sleep"]
        #args: ["infinity"]
        securityContext:
         capabilities:
          add:
            - SYS_NICE
        resources:
          limits:
            #cpu: "1"
            #memory: "2Gi"
            memory: "10Gi"
            nvidia.com/gpu: 1
        env:
        - name: "API_KEY"
          valueFrom:
            secretKeyRef:
              name: ic-k8s-secret
              key: secret57-key
        ports:
        - containerPort: 8000
          protocol: TCP
        volumeMounts:      # Correct placement
        - name: secrets-store-inline
          mountPath: "/mnt/secrets-store"  # Adjust path as needed
      volumes:            # Correct placement
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: "azure-keyvault-ic-bt-secrets"  # Ensure this matches your SecretProviderClass name

#apiVersion: apps/v1
#kind: Deployment
#metadata:
#  name: tts-deployment
#spec:
#  selector:
#    matchLabels:
#      app: tts
#  replicas: 1
#  template:
#    metadata:
#      labels:
#        app: tts
#    spec:
#      containers:
#      - name: tts-container
#        image: btopenspanacrus.azurecr.io/tts:1.0.0
#        imagePullPolicy: Always
#        command:
#          - "/bin/bash"
#        args:
#          - "-c"
#          - |
#            # Install curl and check connectivity to Hugging Face
#            apt-get update && apt-get install -y curl && curl -I https://huggingface.co || echo "Failed to reach Hugging Face"
#            # Sleep forever so the pod doesn't exit
#            sleep infinity
#        securityContext:
#         capabilities:
#          add:
#            - SYS_NICE
#        resources:
#          limits:
#            memory: "2Gi"
#            nvidia.com/gpu: 1
#        env:
#        - name: "API_KEY"
#          valueFrom:
#            secretKeyRef:
#              name: ic-k8s-secret
#              key: secret57-key
#        ports:
#        - containerPort: 8000
#          protocol: TCP
#        volumeMounts:
#        - name: secrets-store-inline
#          mountPath: "/mnt/secrets-store"
#      volumes:
#      - name: secrets-store-inline
#        csi:
#          driver: secrets-store.csi.k8s.io
#          readOnly: true
#          volumeAttributes:
#            secretProviderClass: "azure-keyvault-ic-bt-secrets"
#



---


apiVersion: apps/v1
kind: Deployment
metadata:
  name: stt-deployment
spec:
  selector:
    matchLabels:
      app: stt
  replicas: 1
  template:
    metadata:
      labels:
        app: stt
    spec:
      containers:
      - name: stt-container
        image: btopenspanacrus.azurecr.io/stt:1.0.0
        imagePullPolicy: Always
        securityContext:
         capabilities:
          add:
            - SYS_NICE
        resources:
          limits:
            #cpu: "1"
            #memory: "2Gi"
            nvidia.com/gpu: 1
#        env:
#        - name: "API_KEY"
#          valueFrom:
#            secretKeyRef:
#              name: ic-k8s-secret
#              key: secret57-key
        ports:
        - containerPort: 8004
          protocol: TCP
        volumeMounts:
        - name: secrets-store-inline
          mountPath: "/mnt/secrets-store"
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: "azure-keyvault-ic-bt-secrets"
---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: aigw-deployment
spec:
  selector:
    matchLabels:
      app: aigw
  replicas: 1
  template:
    metadata:
      labels:
        app: aigw
    spec:
      containers:
      - name: aigw-container
        image: btopenspanacrus.azurecr.io/ai_gateway:1.0.1
        imagePullPolicy: Always
        resources:
          limits:
            #cpu: "1"
            memory: "2Gi"
        ports:
        - containerPort: 8003
        - containerPort: 3000
          protocol: TCP
        env:
        - name: BOOKING_URL
          value: "http://booking-assistant-loadbalancer:8008"
        - name: TRANSLATION_URL
          value: "ws://127.0.0.1:8007"
        - name: STT_WS
          value: "ws://stt-loadbalancer:8004"
        - name: MULTI_AGENT_URL
          value: "http://multiagent-service:8001"
        - name: TTS_WS
          value: "ws://tts-loadbalancer:8000"
        - name: TTS_URL
          value: "http://tts-loadbalancer:8000"
        volumeMounts:
        - name: secrets-store-inline
          mountPath: "/mnt/secrets-store"
      volumes:
      - name: secrets-store-inline
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: "azure-keyvault-ic-bt-secrets"

---

apiVersion: v1
kind: Service
metadata:
  name: audiodocker-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - name: https
    protocol: TCP
    port: 8081
    targetPort: 8081
  selector:
    app: audio-docker

---

apiVersion: v1
kind: Service
metadata:
  name: freeswitch-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - name: udp
    protocol: UDP
    port: 6065
    targetPort: 6065
  - name: udp1
    protocol: UDP
    port: 8080
    targetPort: 8080
  - name: tcp
    protocol: TCP
    port: 8080
    targetPort: 8080
  - name: udp2
    protocol: UDP
    port: 55060
    targetPort: 55060
  selector:
    app: freeswitch

---

apiVersion: v1
kind: Service
metadata:
  name: multiagent-service
spec:
  selector:
    app: multiagent
  ports:
  - name: tcp1
    protocol: TCP
    port: 8001
    targetPort: 8001
  - name: tcp2
    protocol: TCP
    port: 8008
    targetPort: 8008
  - name: tcp3
    protocol: TCP
    port: 5000
    targetPort: 5000

---

#apiVersion: v1
#kind: Service
#metadata:
#  name: booking-assistant-service
#spec:
#  selector:
#    app: booking-assistant
#  ports:
#  - name: tcp1
#    protocol: TCP
#    port: 8001
#    targetPort: 8001
#  - name: tcp2
#    protocol: TCP
#    port: 5000
#    targetPort: 5000


apiVersion: v1
kind: Service
metadata:
  name: booking-assistant-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - name: tcp1
    protocol: TCP
    port: 8001
    targetPort: 8001
  - name: tcp2
    protocol: TCP
    port: 5000
    targetPort: 5000
  - name: https
    protocol: TCP
    port: 443
    targetPort: 8008
  - name: tcp3
    protocol: TCP
    port: 8008
    targetPort: 8008
  selector:
    app: booking-assistant

---

apiVersion: v1
kind: Service
metadata:
  name: tts-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - name: tcp1
    protocol: TCP
    port: 8000
    targetPort: 8000
  selector:
    app: tts

---

#apiVersion: v1
#kind: Service
#metadata:
#  name: stt-service
#spec:
#  selector:
#    app: stt
#  ports:
#  - name: tcp1
#    protocol: TCP
#    port: 8004
#    targetPort: 8004

apiVersion: v1
kind: Service
metadata:
  name: stt-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - name: tcp1
    protocol: TCP
    port: 8004
    targetPort: 8004
  selector:
    app: stt

---

apiVersion: v1
kind: Service
metadata:
  name: aigw-loadbalancer
spec:
  type: LoadBalancer
  ports:
  - name: tcp1
    protocol: TCP
    port: 8003
    targetPort: 8003
  - name: tcp2
    protocol: TCP
    port: 3000
    targetPort: 3000
  selector:
    app: aigw


---