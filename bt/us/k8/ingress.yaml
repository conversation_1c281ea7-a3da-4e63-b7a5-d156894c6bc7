---

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bt-openspan-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
      - bookingassistantus.norwoodsystems.com
    secretName: ic-k8s-tls-secret
  rules:
  - host: bookingassistantus.norwoodsystems.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: booking-assistant-loadbalancer
            port:
              number: 443

---