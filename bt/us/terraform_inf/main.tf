terraform {
  required_version = ">=1.0"

  required_providers {
    azapi = {
      source  = "azure/azapi"
      version = "~>1.5"
    }
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~>3.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~>3.0"
    }
    time = {
      source  = "hashicorp/time"
      version = "0.9.1"
    }
  }
}

provider "azurerm" {
  features {
    resource_group {
       prevent_deletion_if_contains_resources = false
    }
  }
  #skip_provider_registration = false
}

data "azurerm_client_config" "current" {}

resource "azurerm_resource_group" "main" {
  name     = var.resource_group_name
  location = var.location
}


module "acr" {
  source              = "./modules/acr"
  name                = var.acr_name
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
}

module "key_vault" {
  source              = "./modules/key_vault"
  name                = var.keyvault_name
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  tenant_id           = data.azurerm_client_config.current.tenant_id
}

module "log_analytics" {
  source              = "./modules/log_analytics"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
}

module "aks" {
  source                         = "./modules/aks"
  name                           = var.aks_name
  location                       = azurerm_resource_group.main.location
  resource_group_name            = azurerm_resource_group.main.name
  log_analytics_workspace_id     = module.log_analytics.workspace_id
  key_vault_id                   = module.key_vault.id
  acr_id                         = module.acr.id

  node_count                     = var.aks_node_count
  vm_size                        = var.aks_vm_size
  min_count                      = var.aks_min_count
  max_count                      = var.aks_max_count
}

module "role_assignments" {
  source                    = "./modules/role_assignments"
  aks_principal_id          = module.aks.aks_principal_id  # System-assigned identity for AKS
  key_vault_id              = module.key_vault.id
  acr_id                    = module.acr.id
  #aks_key_vault_secrets_provider_client_id = module.aks.aks_key_vault_secrets_provider_client_id
  aks_client_id              = module.aks.aks_client_id
  aks_object_id              = module.aks.aks_object_id


  depends_on = [module.aks]
}









