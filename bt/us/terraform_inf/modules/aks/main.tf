resource "azurerm_kubernetes_cluster" "aks" {
  name                = var.name
  location            = var.location
  resource_group_name = var.resource_group_name
  dns_prefix          = "btopenspan"

  default_node_pool {
    name                = "default"
    node_count          = var.node_count
    vm_size             = var.vm_size
    enable_auto_scaling = true
    min_count           = var.min_count
    max_count           = var.max_count
  }

  identity {
    type = "SystemAssigned"
  }

  oms_agent {
    log_analytics_workspace_id = var.log_analytics_workspace_id
  }

  #key_vault_secrets_provider {
  #  secret_rotation_enabled = true
  #}

  key_vault_secrets_provider {
    secret_rotation_enabled     = true
    #user_assigned_identity_id   = azurerm_user_assigned_identity.aks_csi.id
  }


  role_based_access_control_enabled = true

  network_profile {
    network_plugin    = "azure"
    load_balancer_sku = "standard"
  }

  depends_on = [var.key_vault_id, var.acr_id]
}

# Use the output from AKS to get the client_id of the generated identity
output "aks_client_id" {
  value = azurerm_kubernetes_cluster.aks.identity[0].principal_id
}

output "aks_object_id" {
  value = azurerm_kubernetes_cluster.aks.kubelet_identity[0].object_id
}

output "aks_system_identity" {
  value = azurerm_kubernetes_cluster.aks.identity[0].principal_id
}

#output "aks_keyvault_csi_identity" {
  #value = azurerm_kubernetes_cluster.aks.key_vault_secrets_provider[0].secret_identity[0].object_id
#  value = azurerm_user_assigned_identity.aks_csi.object_id
#}

output "aks_principal_id" {
  value = azurerm_kubernetes_cluster.aks.identity[0].principal_id
  #value = azurerm_user_assigned_identity.aks_csi.principal_id
}

output "aks_key_vault_secrets_provider_client_id" {
  value = azurerm_kubernetes_cluster.aks.key_vault_secrets_provider[0].secret_identity[0].client_id
  #value = azurerm_user_assigned_identity.aks_csi.client_id
}