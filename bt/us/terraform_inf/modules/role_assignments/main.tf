resource "azurerm_role_assignment" "aks_acr_pull" {
  #principal_id                     = var.aks_principal_id
  principal_id                     = var.aks_object_id
  role_definition_name             = "AcrPull"
  scope                            = var.acr_id
  skip_service_principal_aad_check = true
}

# System-assigned AKS identity for general Key Vault access
resource "azurerm_role_assignment" "aks_kv_identity" {
  scope                = var.key_vault_id
  role_definition_name = "Key Vault Secrets User"
  principal_id         = var.aks_object_id

  depends_on = [
    azurerm_role_assignment.aks_acr_pull
  ]
}

# User-assigned identity used by the Key Vault Secrets Provider
#resource "azurerm_role_assignment" "aks_user_assigned_identity_kv" {
#  scope                = var.key_vault_id
#  role_definition_name = "Key Vault Secrets User"
#  principal_id         = var.aks_key_vault_secrets_provider_principal_id
#  principal_type       = "ServicePrincipal"
#
#  depends_on = [
#    azurerm_role_assignment.aks_kv_identity
#  ]
#}
