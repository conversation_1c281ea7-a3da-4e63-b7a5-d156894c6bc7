variable "aks_principal_id" {
  type = string
}

variable "acr_id" {
  type = string
}

variable "key_vault_id" {
  type = string
}

variable "aks_client_id" {
  type = string
}

#variable "aks_key_vault_secrets_provider_client_id" {
#  type = string
#}

#variable "aks_key_vault_secrets_provider_principal_id" {
#  description = "Principal ID of the Key Vault CSI driver identity"
#  type        = string
#}

variable aks_object_id {
  type = string
}