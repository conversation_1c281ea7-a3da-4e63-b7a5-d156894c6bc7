variable "location" {
  description = "Azure region to deploy resources"
  default     = "northcentralus"
}

variable "resource_group_name" {
  description = "Name of the resource group"
  default     = "BTOpenSpan_US"
}

variable "acr_name" {
  description = "Name of the Azure Container Registry"
  default     = "btopenspanacrus"
}

variable "keyvault_name" {
  description = "Name of the Azure Key Vault"
  default     = "btopenspan-keyvault-us"
}

variable "aks_name" {
  description = "Name of the AKS cluster"
  default     = "bt-openspan-aks-cluster-us"
}

variable "aks_node_count" {
  description = "The number of nodes in the default node pool."
  type        = number
  default     = 2
}

variable "aks_vm_size" {
  description = "The size of the VMs in the default node pool."
  type        = string
  default     = "Standard_NC4as_T4_v3"
}

variable "aks_min_count" {
  description = "The minimum number of nodes in the default node pool."
  type        = number
  default     = 2
}

variable "aks_max_count" {
  description = "The maximum number of nodes in the default node pool."
  type        = number
  default     = 3
}

