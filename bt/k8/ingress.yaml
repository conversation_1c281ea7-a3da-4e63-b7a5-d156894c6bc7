---

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bt-openspan-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
      - bookingassistant.norwoodsystems.com
      - bookingassistant2.norwoodsystems.com
    secretName: ic-k8s-tls-secret
  rules:
  - host: bookingassistant.norwoodsystems.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: booking-assistant-loadbalancer
            port:
              number: 443
  - host: bookingassistant2.norwoodsystems.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: audiodocker-loadbalancer
            port:
              number: 443

---