#!/bin/bash

# You will need to make sure that you ran az login prior to runnning this script
# Set variables
SOURCE_VAULT="frauddetector-keyvault"
DEST_VAULT="btopenspan-keyvault"
PATH_TO_CERT="$HOME/.ssh/norwoodsystemscom/ivr_azure_certificate.pfx"
CERT_NAME="ivr-azure-certificate"

# List all secrets from the source Key Vault
for secret in $(az keyvault secret list --vault-name $SOURCE_VAULT --query "[].id" -o tsv); do
  # Extract the secret name from the full ID
  name=$(basename $secret)
  
  # Get the value of the secret
  value=$(az keyvault secret show --vault-name $SOURCE_VAULT --name $name --query "value" -o tsv)

  # Set the secret in the destination Key Vault
  echo "Copying secret: $name"
  az keyvault secret set --vault-name $DEST_VAULT --name $name --value "$value"
done

echo "Importing the certificate:"
az keyvault certificate import --vault-name btopenspan-keyvault --file $PATH_TO_CERT --name $CERT_NAME
