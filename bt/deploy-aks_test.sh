#!/bin/bash

RESOURCE_GROUP="BTOpenSpan"
LOCATION="australiaeast"
ACR_NAME="btopenspanacr"
AKS_NAME="bt-openspan-aks-cluster"
KEYVAULT_NAME="btopenspan-keyvault"

echo "Create Resource Group"
az group create --name $RESOURCE_GROUP --location $LOCATION


echo "Create ACR"
az acr create --resource-group $RESOURCE_GROUP --name $ACR_NAME --sku Standard --location $LOCATION

echo "Create Key Vault"
az keyvault create --name $KEYVAULT_NAME --resource-group $RESOURCE_GROUP --location $LOCATION --enable-rbac-authorization

echo "Create AKS Cluster"
az aks create --name $AKS_NAME \
  --resource-group $RESOURCE_GROUP \
  --enable-addons azure-keyvault-secrets-provider,monitoring \
  --enable-secret-rotation \
  --node-vm-size Standard_NC4as_T4_v3 \
  --attach-acr $ACR_NAME \
  --location $LOCATION \
  --node-count 2 \
  --enable-cluster-autoscaler --min-count 2 --max-count 3

echo "Assign AKS identity to Key Vault"
USER_ASSIGNED_CLIENT_ID=$(az aks show --resource-group $RESOURCE_GROUP --name $AKS_NAME --query addonProfiles.azureKeyvaultSecretsProvider.identity.clientId -o tsv)
KEYVAULT_SCOPE=$(az keyvault show --name $KEYVAULT_NAME --query id -o tsv)
az role assignment create --role "Key Vault Secrets User" --assignee $USER_ASSIGNED_CLIENT_ID --scope $KEYVAULT_SCOPE

echo "Deployment completed successfully!"
