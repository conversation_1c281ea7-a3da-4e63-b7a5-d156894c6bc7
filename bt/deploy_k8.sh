#!/bin/bash

set -e

echo "Starting k8 deployment...."

# === CONFIGURATION ===
MANIFEST_FILE="k8/bt_deployment_initial.yaml"     # Source manifest
UPDATED_FILE="k8/bt_deployment_final.yaml"        # Where the patched manifest will be saved
INGRESS_FILE="k8/ingress.yaml"
NGINX_VALUES="k8/nginx-values.yaml"

# === DYNAMIC VALUES FROM AZURE ===
RESOURCE_GROUP="BTOpenSpan"
LOCATION="australiaeast"
ACR_NAME="btopenspanacr"
AKS_NAME="bt-openspan-aks-cluster"
KEYVAULT_NAME="btopenspan-keyvault"


# Login into the cluster to deploy k8
echo "Login into the cluster to deploy k8...."
az aks get-credentials --name $AKS_NAME --resource-group $RESOURCE_GROUP

USER_ASSIGNED_ID=$(az aks show --resource-group $RESOURCE_GROUP --name $AKS_NAME --query addonProfiles.azureKeyvaultSecretsProvider.identity.clientId -o tsv)
TENANT_ID=$(az account show --query tenantId -o tsv)
KEYVAULT_SCOPE=$(az keyvault show --name $KEYVAULT_NAME --query id -o tsv)

echo "User Assigned Identity: $USER_ASSIGNED_ID"
echo "Tenant ID: $TENANT_ID"

# Creating a role so that pods can interact with the key vault
echo "Creating Key Vault Secrets User role..."
az role assignment create --role "Key Vault Secrets User" --assignee $USER_ASSIGNED_ID --scope $KEYVAULT_SCOPE

# === Install Nvidia GPU drivers if not installed already ===
if ! kubectl get daemonset nvidia-device-plugin-daemonset -n kube-system &>/dev/null; then
  echo "Installing NVIDIA device plugin..."
  kubectl apply -f https://raw.githubusercontent.com/NVIDIA/k8s-device-plugin/v0.14.1/nvidia-device-plugin.yml
else
  echo "NVIDIA device plugin already installed."
fi

# === PATCH SecretProviderClass section ===
echo "Patching SecretProviderClass in $MANIFEST_FILE..."

sed -e "s|userAssignedIdentityID: .*|userAssignedIdentityID: \"$USER_ASSIGNED_ID\"|" \
    -e "s|keyvaultName: .*|keyvaultName: \"$KEYVAULT_NAME\"|" \
    -e "s|tenantId: .*|tenantId: \"$TENANT_ID\"|" \
    "$MANIFEST_FILE" > "$UPDATED_FILE"

echo "Patched manifest saved to $UPDATED_FILE"

# === OPTIONAL: Apply it ===
read -p "Apply updated manifest to your cluster? (y/N): " CONFIRM
if [[ "$CONFIRM" == "y" || "$CONFIRM" == "Y" ]]; then
  kubectl apply -f "$UPDATED_FILE"
fi

# Wait for the LB IPs to be assinged so that ingress creation dosen't fail
echo "Wait 180 sec for the LB IPs to be assinged...."
sleep 180;

if [[ -n "$EXISTING_RELEASE" && -n "$EXISTING_NAMESPACE" ]]; then
  echo "Existing ingress-nginx release found: $EXISTING_RELEASE in namespace $EXISTING_NAMESPACE"
  echo "Uninstalling existing ingress-nginx release..."
  helm uninstall "$EXISTING_RELEASE" -n "$EXISTING_NAMESPACE"
  sleep 10
else
  echo "No existing ingress-nginx release found."
fi


# === Install NGINX Ingress Controller ===
echo "Installing NGINX Ingress Controller..."
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update

echo "Processing........."
sleep 60;


# If the below fails to create the nginx LB under the services then it needs to be re-installed with:
# 1. helm uninstall <name of the nginx release from helm list -A command>
# 2. kubectl delete pods <nginx name of the pod>
# 3. Run the Install Nginx controller from above
# 4. Run the below command don't forget to replace the NGINX_VALUES variable with correct value
# 5. Also create the ingess resource
echo "Creating ingress-nginx pod and binding secre provider class to it......"
helm install ingress-nginx/ingress-nginx --generate-name \
  --timeout 15m \
  --set controller.replicaCount=1 \
  --set controller.nodeSelector."kubernetes\.io/os"=linux \
  --set defaultBackend.nodeSelector."kubernetes\.io/os"=linux \
  --set controller.service.annotations."service\.beta\.kubernetes\.io/azure-load-balancer-health-probe-request-path"=/healthz \
  -f "$NGINX_VALUES"

echo "Processing........"
sleep 60;

kubectl apply -f "$INGRESS_FILE"